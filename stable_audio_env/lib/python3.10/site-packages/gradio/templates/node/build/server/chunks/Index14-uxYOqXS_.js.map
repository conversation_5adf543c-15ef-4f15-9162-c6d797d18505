{"version": 3, "file": "Index14-uxYOqXS_.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index14.js"], "sourcesContent": ["import { create_ssr_component } from \"svelte/internal\";\nimport { onDestroy } from \"svelte\";\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { gradio } = $$props;\n  let { value = 1 } = $$props;\n  let { active = true } = $$props;\n  let old_value;\n  let old_active;\n  let interval;\n  onDestroy(() => {\n    if (interval)\n      clearInterval(interval);\n  });\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.active === void 0 && $$bindings.active && active !== void 0)\n    $$bindings.active(active);\n  {\n    if (old_value !== value || active !== old_active) {\n      if (interval)\n        clearInterval(interval);\n      if (active) {\n        interval = setInterval(\n          () => {\n            if (document.visibilityState === \"visible\")\n              gradio.dispatch(\"tick\");\n          },\n          value * 1e3\n        );\n      }\n      old_value = value;\n      old_active = active;\n    }\n  }\n  return ``;\n});\nexport {\n  Index as default\n};\n"], "names": [], "mappings": ";;AAEK,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,IAAI,QAAQ;AAChB,MAAM,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC9B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE;AACF,IAAI,IAAI,SAAS,KAAK,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AACtD,MAAM,IAAI,QAAQ;AAClB,QAAQ,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChC,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,QAAQ,GAAG,WAAW;AAC9B,UAAU,MAAM;AAChB,YAAY,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS;AACtD,cAAc,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtC,WAAW;AACX,UAAU,KAAK,GAAG,GAAG;AACrB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;;;;"}