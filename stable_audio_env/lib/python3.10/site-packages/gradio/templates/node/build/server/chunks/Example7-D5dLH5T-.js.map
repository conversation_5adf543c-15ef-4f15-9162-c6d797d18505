{"version": 3, "file": "Example7-D5dLH5T-.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example7.js"], "sourcesContent": ["import { create_ssr_component, escape } from \"svelte/internal\";\nconst css = {\n  code: \"div.svelte-h6ogpl{width:var(--size-10);height:var(--size-10)}.table.svelte-h6ogpl{margin:0 auto}\",\n  map: `{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tstyle=\\\\\"background-color: {value ? value : 'black'}\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n/>\\\\n\\\\n<style>\\\\n\\\\tdiv {\\\\n\\\\t\\\\twidth: var(--size-10);\\\\n\\\\t\\\\theight: var(--size-10);\\\\n\\\\t}\\\\n\\\\t.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAaC,iBAAI,CACH,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CACtB,CACA,oBAAO,CACN,MAAM,CAAE,CAAC,CAAC,IACX\"}`\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<div style=\"${\"background-color: \" + escape(value ? value : \"black\", true)}\" class=\"${[\n    \"svelte-h6ogpl\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\"></div>`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,kGAAkG;AAC1G,EAAE,GAAG,EAAE,CAAC,kpBAAkpB,CAAC;AAC3pB,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE,oBAAoB,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,SAAS,EAAE;AAChG,IAAI,eAAe;AACnB,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;;;;"}