#!/usr/bin/env python3
"""
Simple demo script for Stable Audio Open Small
This script demonstrates how to generate audio using the stable-audio-open-small model.
"""

import os
import sys
import torch
import torchaudio
import tempfile
from pathlib import Path

# Add the stable-audio-tools to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'stable-audio-tools'))

def test_basic_imports():
    """Test if we can import the basic modules"""
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        
        import torchaudio
        print(f"✓ TorchAudio version: {torchaudio.__version__}")
        
        from einops import rearrange
        print("✓ einops imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_stable_audio_import():
    """Test if we can import stable-audio-tools"""
    try:
        from stable_audio_tools import get_pretrained_model
        print("✓ stable-audio-tools imported successfully")
        return True
    except ImportError as e:
        print(f"✗ stable-audio-tools import error: {e}")
        return False

def generate_audio_simple(prompt="128 BPM tech house drum loop", duration=8):
    """
    Simple audio generation function
    """
    try:
        print(f"\n🎵 Generating audio for: '{prompt}'")
        print(f"Duration: {duration} seconds")
        
        # Import required modules
        from einops import rearrange
        from stable_audio_tools import get_pretrained_model
        from stable_audio_tools.inference.generation import generate_diffusion_cond
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        # Load model
        print("Loading model...")
        model, model_config = get_pretrained_model("stabilityai/stable-audio-open-small")
        sample_rate = model_config["sample_rate"]
        sample_size = model_config["sample_size"]
        
        model = model.to(device)
        print(f"Model loaded! Sample rate: {sample_rate}, Sample size: {sample_size}")
        
        # Set up conditioning
        conditioning = [{
            "prompt": prompt,
            "seconds_total": min(duration, 11)  # Max 11 seconds for small model
        }]
        
        # Generate audio
        print("Generating audio...")
        with torch.no_grad():
            output = generate_diffusion_cond(
                model,
                steps=8,
                cfg_scale=1.0,
                conditioning=conditioning,
                sample_size=sample_size,
                sampler_type="pingpong",
                device=device
            )
        
        # Process output
        output = rearrange(output, "b d n -> d (b n)")
        output = output.to(torch.float32).div(torch.max(torch.abs(output))).clamp(-1, 1).mul(32767).to(torch.int16).cpu()
        
        # Save to file
        output_file = f"generated_audio_{prompt.replace(' ', '_')[:20]}.wav"
        torchaudio.save(output_file, output, sample_rate)
        
        print(f"✓ Audio generated successfully: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"✗ Error generating audio: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    print("🎵 Stable Audio Open Small - Simple Demo")
    print("=" * 50)
    
    # Test imports
    print("\n1. Testing basic imports...")
    if not test_basic_imports():
        print("❌ Basic imports failed. Please install required packages.")
        return
    
    print("\n2. Testing stable-audio-tools import...")
    if not test_stable_audio_import():
        print("❌ stable-audio-tools import failed.")
        print("This might be due to missing dependencies or PyTorch version compatibility.")
        print("\nTo fix this, you may need to:")
        print("- Update PyTorch to a newer version")
        print("- Install missing dependencies")
        print("- Use a different Python environment")
        return
    
    print("\n3. Testing audio generation...")
    
    # Test prompts
    test_prompts = [
        ("128 BPM tech house drum loop", 8),
        ("acoustic guitar fingerpicking", 6),
        ("rain on window", 10),
    ]
    
    for prompt, duration in test_prompts:
        result = generate_audio_simple(prompt, duration)
        if result:
            print(f"✓ Generated: {result}")
        else:
            print(f"✗ Failed to generate audio for: {prompt}")
            break
    
    print("\n🎉 Demo completed!")

if __name__ == "__main__":
    main()
