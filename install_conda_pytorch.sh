#!/bin/bash
# Install Conda and PyTorch with flex_attention support
# This is the fastest and most reliable method

echo "🐍 Installing Miniconda..."

# Download and install Miniconda
curl -O https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh
bash Miniconda3-latest-MacOSX-x86_64.sh -b -p $HOME/miniconda3

# Initialize conda
source $HOME/miniconda3/bin/activate
conda init zsh

echo "📦 Creating new environment for Stable Audio..."

# Create new environment with Python 3.11
conda create -n stable-audio python=3.11 -y
conda activate stable-audio

echo "🔥 Installing PyTorch with conda-forge..."

# Install PyTorch from conda-forge (often has newer versions)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y

# Alternative: try conda-forge channel
# conda install pytorch torchvision torchaudio -c conda-forge -y

echo "📚 Installing additional dependencies..."

# Install other required packages
conda install gradio einops transformers safetensors -c conda-forge -y
pip install k-diffusion huggingface_hub

echo "✅ Installation complete!"
echo "To activate the environment, run: conda activate stable-audio"
echo "Then test with: python -c 'import torch.nn.attention; print(\"flex_attention available:\", hasattr(torch.nn.attention, \"flex_attention\"))'"
