/*******************************************************************************
* Copyright 2016-2025 Intel Corporation
* Copyright 2024 FUJITSU LIMITED
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

#ifndef COMMON_C_TYPES_MAP_HPP
#define COMMON_C_TYPES_MAP_HPP

#include "oneapi/dnnl/dnnl_types.h"

// These aliases should be in the global namespace as they are intended
// to give names that better reflects the meaning of the entities
using primitive_iface_t = dnnl_primitive;
using primitive_desc_iface_t = dnnl_primitive_desc;

namespace dnnl {
namespace impl {

// TODO: autogenerate this

using dim_t = dnnl_dim_t;
using dims_t = dnnl_dims_t;
using stride_t = dnnl_dim_t;
using strides_t = dnnl_dims_t;

#ifdef DNNL_STATUS_NODISCARD
// nodiscard is not allowed in type aliases
using status_t __attribute__((warn_unused_result)) = dnnl_status_t;
#else
using status_t = dnnl_status_t;
#endif
namespace status {
const status_t success = dnnl_success;
const status_t out_of_memory = dnnl_out_of_memory;
const status_t invalid_arguments = dnnl_invalid_arguments;
const status_t unimplemented = dnnl_unimplemented;
const status_t last_impl_reached = dnnl_last_impl_reached;
const status_t runtime_error = dnnl_runtime_error;
const status_t not_required = dnnl_not_required;
} // namespace status

using prop_kind_t = dnnl_prop_kind_t;
namespace prop_kind {
const prop_kind_t undef = dnnl_prop_kind_undef;
const prop_kind_t forward_training = dnnl_forward_training;
const prop_kind_t forward_inference = dnnl_forward_inference;
const prop_kind_t forward = dnnl_forward;
const prop_kind_t backward = dnnl_backward;
const prop_kind_t backward_data = dnnl_backward_data;
const prop_kind_t backward_weights = dnnl_backward_weights;
const prop_kind_t backward_bias = dnnl_backward_bias;
} // namespace prop_kind

using alg_kind_t = dnnl_alg_kind_t;
namespace alg_kind {
const alg_kind_t undef = dnnl_alg_kind_undef;
const alg_kind_t convolution_auto = dnnl_convolution_auto;
const alg_kind_t convolution_direct = dnnl_convolution_direct;
const alg_kind_t convolution_winograd = dnnl_convolution_winograd;
const alg_kind_t deconvolution_direct = dnnl_deconvolution_direct;
const alg_kind_t deconvolution_winograd = dnnl_deconvolution_winograd;
const alg_kind_t eltwise_relu = dnnl_eltwise_relu;
const alg_kind_t eltwise_tanh = dnnl_eltwise_tanh;
const alg_kind_t eltwise_elu = dnnl_eltwise_elu;
const alg_kind_t eltwise_square = dnnl_eltwise_square;
const alg_kind_t eltwise_abs = dnnl_eltwise_abs;
const alg_kind_t eltwise_sqrt = dnnl_eltwise_sqrt;
const alg_kind_t eltwise_swish = dnnl_eltwise_swish;
const alg_kind_t eltwise_linear = dnnl_eltwise_linear;
const alg_kind_t eltwise_soft_relu = dnnl_eltwise_soft_relu;
const alg_kind_t eltwise_logistic = dnnl_eltwise_logistic;
const alg_kind_t eltwise_mish = dnnl_eltwise_mish;
const alg_kind_t eltwise_exp = dnnl_eltwise_exp;
const alg_kind_t eltwise_log = dnnl_eltwise_log;
const alg_kind_t eltwise_clip = dnnl_eltwise_clip;
const alg_kind_t eltwise_clip_v2 = dnnl_eltwise_clip_v2;
const alg_kind_t eltwise_pow = dnnl_eltwise_pow;
const alg_kind_t eltwise_gelu_tanh = dnnl_eltwise_gelu_tanh;
const alg_kind_t eltwise_gelu_erf = dnnl_eltwise_gelu_erf;
const alg_kind_t eltwise_hardswish = dnnl_eltwise_hardswish;
const alg_kind_t eltwise_hardsigmoid = dnnl_eltwise_hardsigmoid;
const alg_kind_t eltwise_relu_use_dst_for_bwd
        = dnnl_eltwise_relu_use_dst_for_bwd;
const alg_kind_t eltwise_tanh_use_dst_for_bwd
        = dnnl_eltwise_tanh_use_dst_for_bwd;
const alg_kind_t eltwise_elu_use_dst_for_bwd = dnnl_eltwise_elu_use_dst_for_bwd;
const alg_kind_t eltwise_sqrt_use_dst_for_bwd
        = dnnl_eltwise_sqrt_use_dst_for_bwd;
const alg_kind_t eltwise_logistic_use_dst_for_bwd
        = dnnl_eltwise_logistic_use_dst_for_bwd;
const alg_kind_t eltwise_exp_use_dst_for_bwd = dnnl_eltwise_exp_use_dst_for_bwd;
const alg_kind_t eltwise_clip_v2_use_dst_for_bwd
        = dnnl_eltwise_clip_v2_use_dst_for_bwd;
const alg_kind_t eltwise_round = dnnl_eltwise_round;
const alg_kind_t pooling_max = dnnl_pooling_max;
const alg_kind_t pooling_avg_include_padding = dnnl_pooling_avg_include_padding;
const alg_kind_t pooling_avg_exclude_padding = dnnl_pooling_avg_exclude_padding;
const alg_kind_t lrn_across_channels = dnnl_lrn_across_channels;
const alg_kind_t lrn_within_channel = dnnl_lrn_within_channel;
const alg_kind_t vanilla_rnn = dnnl_vanilla_rnn;
const alg_kind_t vanilla_lstm = dnnl_vanilla_lstm;
const alg_kind_t vanilla_gru = dnnl_vanilla_gru;
const alg_kind_t lbr_gru = dnnl_lbr_gru;
const alg_kind_t vanilla_augru = dnnl_vanilla_augru;
const alg_kind_t lbr_augru = dnnl_lbr_augru;
const alg_kind_t binary_add = dnnl_binary_add;
const alg_kind_t binary_mul = dnnl_binary_mul;
const alg_kind_t binary_max = dnnl_binary_max;
const alg_kind_t binary_min = dnnl_binary_min;
const alg_kind_t binary_div = dnnl_binary_div;
const alg_kind_t binary_sub = dnnl_binary_sub;
const alg_kind_t binary_ge = dnnl_binary_ge;
const alg_kind_t binary_gt = dnnl_binary_gt;
const alg_kind_t binary_le = dnnl_binary_le;
const alg_kind_t binary_lt = dnnl_binary_lt;
const alg_kind_t binary_eq = dnnl_binary_eq;
const alg_kind_t binary_ne = dnnl_binary_ne;
const alg_kind_t binary_select = dnnl_binary_select;
const alg_kind_t resampling_nearest = dnnl_resampling_nearest;
const alg_kind_t resampling_linear = dnnl_resampling_linear;
const alg_kind_t reduction_max = dnnl_reduction_max;
const alg_kind_t reduction_min = dnnl_reduction_min;
const alg_kind_t reduction_sum = dnnl_reduction_sum;
const alg_kind_t reduction_mul = dnnl_reduction_mul;
const alg_kind_t reduction_mean = dnnl_reduction_mean;
const alg_kind_t reduction_norm_lp_max = dnnl_reduction_norm_lp_max;
const alg_kind_t reduction_norm_lp_sum = dnnl_reduction_norm_lp_sum;
const alg_kind_t reduction_norm_lp_power_p_max
        = dnnl_reduction_norm_lp_power_p_max;
const alg_kind_t reduction_norm_lp_power_p_sum
        = dnnl_reduction_norm_lp_power_p_sum;
const alg_kind_t softmax_accurate = dnnl_softmax_accurate;
const alg_kind_t softmax_log = dnnl_softmax_log;
// Internal only alg kinds.
const alg_kind_t internal_only_start = (alg_kind_t)(1 << 12);
// GPU only via jit_eltwise injector.
const alg_kind_t eltwise_stochastic_round
        = (alg_kind_t)(internal_only_start + 1);
} // namespace alg_kind

using data_type_t = dnnl_data_type_t;
namespace data_type {
const data_type_t undef = dnnl_data_type_undef;
const data_type_t f4_e3m0 = dnnl_f4_e3m0;
const data_type_t f4_e2m1 = dnnl_f4_e2m1;
const data_type_t e8m0 = dnnl_e8m0;
const data_type_t f8_e5m2 = dnnl_f8_e5m2;
const data_type_t f8_e4m3 = dnnl_f8_e4m3;
const data_type_t f16 = dnnl_f16;
const data_type_t bf16 = dnnl_bf16;
const data_type_t f32 = dnnl_f32;
const data_type_t f64 = dnnl_f64;
const data_type_t s32 = dnnl_s32;
const data_type_t s8 = dnnl_s8;
const data_type_t u8 = dnnl_u8;
const data_type_t s4 = dnnl_s4;
const data_type_t u4 = dnnl_u4;
const data_type_t boolean = dnnl_boolean;
const data_type_t data_type_max = dnnl_data_type_max;

// Not exposed through API as all current uses are internal only
const data_type_t tf32 = static_cast<data_type_t>(1 << 8);

} // namespace data_type

using fpmath_mode_t = dnnl_fpmath_mode_t;
namespace fpmath_mode {
const fpmath_mode_t strict = dnnl_fpmath_mode_strict;
const fpmath_mode_t bf16 = dnnl_fpmath_mode_bf16;
const fpmath_mode_t f16 = dnnl_fpmath_mode_f16;
const fpmath_mode_t tf32 = dnnl_fpmath_mode_tf32;
const fpmath_mode_t any = dnnl_fpmath_mode_any;
} // namespace fpmath_mode

using accumulation_mode_t = dnnl_accumulation_mode_t;
namespace accumulation_mode {
const accumulation_mode_t strict = dnnl_accumulation_mode_strict;
const accumulation_mode_t relaxed = dnnl_accumulation_mode_relaxed;
const accumulation_mode_t any = dnnl_accumulation_mode_any;
const accumulation_mode_t f32 = dnnl_accumulation_mode_f32;
const accumulation_mode_t s32 = dnnl_accumulation_mode_s32;
const accumulation_mode_t f16 = dnnl_accumulation_mode_f16;
} // namespace accumulation_mode

using scratchpad_mode_t = dnnl_scratchpad_mode_t;
namespace scratchpad_mode {
const scratchpad_mode_t library = dnnl_scratchpad_mode_library;
const scratchpad_mode_t user = dnnl_scratchpad_mode_user;
} // namespace scratchpad_mode

using rounding_mode_t = dnnl_rounding_mode_t;
namespace rounding_mode {
const rounding_mode_t environment = dnnl_rounding_mode_environment;
const rounding_mode_t stochastic = dnnl_rounding_mode_stochastic;
} // namespace rounding_mode

#ifdef DNNL_EXPERIMENTAL_SPARSE
using sparse_encoding_t = dnnl_sparse_encoding_t;
namespace sparse_encoding {
const sparse_encoding_t undef = dnnl_sparse_encoding_undef;
const sparse_encoding_t csr = dnnl_csr;
const sparse_encoding_t coo = dnnl_coo;
const sparse_encoding_t packed = dnnl_packed;
} // namespace sparse_encoding
#else
// Declare dummy values to avoid guarding internal implementation.
using sparse_encoding_t = int;
namespace sparse_encoding {
const sparse_encoding_t undef = 0;
const sparse_encoding_t csr = 1;
const sparse_encoding_t packed = 2;
const sparse_encoding_t coo = 3;
} // namespace sparse_encoding
#endif

using format_kind_t = dnnl_format_kind_t;
namespace format_kind {
const format_kind_t undef = dnnl_format_kind_undef;
const format_kind_t any = dnnl_format_kind_any;
const format_kind_t blocked = dnnl_blocked;
const format_kind_t opaque = dnnl_format_kind_opaque;
#ifdef DNNL_EXPERIMENTAL_SPARSE
const format_kind_t sparse = dnnl_format_kind_sparse;
#else
const format_kind_t sparse = static_cast<format_kind_t>(4);
#endif

// Internal only format kinds.
const format_kind_t internal_only_start = (format_kind_t)(1 << 8);
const format_kind_t wino = internal_only_start;
const format_kind_t rnn_packed = (format_kind_t)(internal_only_start + 1);
const format_kind_t cublaslt_blocked = (format_kind_t)(internal_only_start + 2);
} // namespace format_kind

#ifdef DNNL_EXPERIMENTAL_PROFILING
using profiling_data_kind_t = dnnl_profiling_data_kind_t;
namespace profiling_data_kind {
const profiling_data_kind_t undef = dnnl_profiling_data_kind_undef;
const profiling_data_kind_t time = dnnl_profiling_data_kind_time;
#else
using profiling_data_kind_t = int;
namespace profiling_data_kind {
const profiling_data_kind_t undef = 0;
const profiling_data_kind_t time = 1;
#endif
// Internal only data kinds.
const profiling_data_kind_t internal_only_start
        = (profiling_data_kind_t)(1 << 8);
const profiling_data_kind_t cycles
        = (profiling_data_kind_t)(internal_only_start + 1);
} // namespace profiling_data_kind

using format_tag_t = dnnl_format_tag_t;
namespace format_tag {
const format_tag_t undef = dnnl_format_tag_undef;
const format_tag_t any = dnnl_format_tag_any;
const format_tag_t a = dnnl_a;
const format_tag_t ab = dnnl_ab;
const format_tag_t abc = dnnl_abc;
const format_tag_t abcd = dnnl_abcd;
const format_tag_t abcde = dnnl_abcde;
const format_tag_t abcdef = dnnl_abcdef;
const format_tag_t abcdefg = dnnl_abcdefg;
const format_tag_t abcdefgh = dnnl_abcdefgh;
const format_tag_t abcdefghi = dnnl_abcdefghi;
const format_tag_t abcdefghij = dnnl_abcdefghij;
const format_tag_t abcdefghijk = dnnl_abcdefghijk;
const format_tag_t abcdefghijkl = dnnl_abcdefghijkl;
const format_tag_t abcdefghijlk = dnnl_abcdefghijlk;
const format_tag_t abcdefghikj = dnnl_abcdefghikj;
const format_tag_t abcdefghji = dnnl_abcdefghji;
const format_tag_t abcdefgih = dnnl_abcdefgih;
const format_tag_t abcdefhg = dnnl_abcdefhg;
const format_tag_t abcdegf = dnnl_abcdegf;
const format_tag_t abcdfe = dnnl_abcdfe;
const format_tag_t abced = dnnl_abced;
const format_tag_t abdc = dnnl_abdc;
const format_tag_t acbd = dnnl_acbd;
const format_tag_t abdec = dnnl_abdec;
const format_tag_t abdfce = dnnl_abdfce;
const format_tag_t acb = dnnl_acb;
const format_tag_t acbde = dnnl_acbde;
const format_tag_t acbdef = dnnl_acbdef;
const format_tag_t abdefc = dnnl_abdefc;
const format_tag_t acdb = dnnl_acdb;
const format_tag_t acdeb = dnnl_acdeb;
const format_tag_t adbc = dnnl_adbc;
const format_tag_t ba = dnnl_ba;
const format_tag_t bac = dnnl_bac;
const format_tag_t bacd = dnnl_bacd;
const format_tag_t bca = dnnl_bca;
const format_tag_t bcad = dnnl_bcad;
const format_tag_t bcda = dnnl_bcda;
const format_tag_t bcdea = dnnl_bcdea;
const format_tag_t bacde = dnnl_bacde;
const format_tag_t cab = dnnl_cab;
const format_tag_t cba = dnnl_cba;
const format_tag_t cabd = dnnl_cabd;
const format_tag_t cdab = dnnl_cdab;
const format_tag_t cdba = dnnl_cdba;
const format_tag_t dabc = dnnl_dabc;
const format_tag_t dcab = dnnl_dcab;
const format_tag_t cdeab = dnnl_cdeab;
const format_tag_t cdeba = dnnl_cdeba;
const format_tag_t decab = dnnl_decab;
const format_tag_t defcab = dnnl_defcab;
const format_tag_t AB16b16a = dnnl_AB16b16a;
const format_tag_t AB16b32a = dnnl_AB16b32a;
const format_tag_t AB16b48a = dnnl_AB16b48a;
const format_tag_t AB16b64a = dnnl_AB16b64a;
const format_tag_t AB8b16a2b = dnnl_AB8b16a2b;
const format_tag_t AB8b32a2b = dnnl_AB8b32a2b;
const format_tag_t AB8b64a2b = dnnl_AB8b64a2b;
const format_tag_t AB4b8a4b = dnnl_AB4b8a4b;
const format_tag_t AB4b16a4b = dnnl_AB4b16a4b;
const format_tag_t AB4b24a4b = dnnl_AB4b24a4b;
const format_tag_t AB4b32a4b = dnnl_AB4b32a4b;
const format_tag_t AB4b64a4b = dnnl_AB4b64a4b;
const format_tag_t AB32a16b = dnnl_AB32a16b;
const format_tag_t AB32a32b = dnnl_AB32a32b;
const format_tag_t AB48a16b = dnnl_AB48a16b;
const format_tag_t AB48a32b = dnnl_AB48a32b;
const format_tag_t BA4b8a8b2a = dnnl_BA4b8a8b2a;
const format_tag_t BA4b8a8b4a = dnnl_BA4b8a8b4a;
const format_tag_t BA4b8a16b2a = dnnl_BA4b8a16b2a;
const format_tag_t BA4b8a16b4a = dnnl_BA4b8a16b4a;
const format_tag_t aBC32b16c = dnnl_aBC32b16c;
const format_tag_t aBC32b32c = dnnl_aBC32b32c;
const format_tag_t aBC48b16c = dnnl_aBC48b16c;
const format_tag_t aBC48b32c = dnnl_aBC48b32c;
const format_tag_t aCB4c8b8c2b = dnnl_aCB4c8b8c2b;
const format_tag_t aCB4c8b8c4b = dnnl_aCB4c8b8c4b;
const format_tag_t aCB4c8b16c2b = dnnl_aCB4c8b16c2b;
const format_tag_t aCB4c8b16c4b = dnnl_aCB4c8b16c4b;
const format_tag_t AB16b16a4b = dnnl_AB16b16a4b;
const format_tag_t AB16b32a4b = dnnl_AB16b32a4b;
const format_tag_t AB16b48a4b = dnnl_AB16b48a4b;
const format_tag_t AB16b64a4b = dnnl_AB16b64a4b;
const format_tag_t AB16b16a2b = dnnl_AB16b16a2b;
const format_tag_t AB16b32a2b = dnnl_AB16b32a2b;
const format_tag_t AB16b48a2b = dnnl_AB16b48a2b;
const format_tag_t AB16b64a2b = dnnl_AB16b64a2b;
const format_tag_t BA4b4a = dnnl_BA4b4a;
const format_tag_t BA8b4a = dnnl_BA8b4a;
const format_tag_t BA16a16b = dnnl_BA16a16b;
const format_tag_t BA16a32b = dnnl_BA16a32b;
const format_tag_t BA16a48b = dnnl_BA16a48b;
const format_tag_t BA16a64b = dnnl_BA16a64b;
const format_tag_t BA16a16b2a = dnnl_BA16a16b2a;
const format_tag_t BA16a32b2a = dnnl_BA16a32b2a;
const format_tag_t BA16a48b2a = dnnl_BA16a48b2a;
const format_tag_t BA16a64b2a = dnnl_BA16a64b2a;
const format_tag_t BA16a16b4a = dnnl_BA16a16b4a;
const format_tag_t BA16a32b4a = dnnl_BA16a32b4a;
const format_tag_t BA16a48b4a = dnnl_BA16a48b4a;
const format_tag_t BA16a64b4a = dnnl_BA16a64b4a;
const format_tag_t aCB16b16c = dnnl_aCB16b16c;
const format_tag_t aCB16b32c = dnnl_aCB16b32c;
const format_tag_t aCB16b48c = dnnl_aCB16b48c;
const format_tag_t aCB16b64c = dnnl_aCB16b64c;
const format_tag_t aCB16b16c2b = dnnl_aCB16b16c2b;
const format_tag_t aCB16b32c2b = dnnl_aCB16b32c2b;
const format_tag_t aCB16b48c2b = dnnl_aCB16b48c2b;
const format_tag_t aCB16b64c2b = dnnl_aCB16b64c2b;
const format_tag_t aCB16b16c4b = dnnl_aCB16b16c4b;
const format_tag_t aCB16b32c4b = dnnl_aCB16b32c4b;
const format_tag_t aCB16b48c4b = dnnl_aCB16b48c4b;
const format_tag_t aCB16b64c4b = dnnl_aCB16b64c4b;

const format_tag_t Ab4a = dnnl_Ab4a;
const format_tag_t Ab8a = dnnl_Ab8a;
const format_tag_t Ab32a = dnnl_Ab32a;
const format_tag_t Abc16a = dnnl_Abc16a;
const format_tag_t ABc16a16b = dnnl_ABc16a16b;
const format_tag_t ABc4a2b = dnnl_ABc4a2b;
const format_tag_t ABc4a4b = dnnl_ABc4a4b;
const format_tag_t aBc16b = dnnl_aBc16b;
const format_tag_t aBc32b = dnnl_aBc32b;
const format_tag_t ABc16b16a = dnnl_ABc16b16a;
const format_tag_t AcB16b16a = dnnl_AcB16b16a;
const format_tag_t ABc16b32a = dnnl_ABc16b32a;
const format_tag_t AcB16b32a = dnnl_AcB16b32a;
const format_tag_t ABc16b48a = dnnl_ABc16b48a;
const format_tag_t AcB16b48a = dnnl_AcB16b48a;
const format_tag_t ABc16b64a = dnnl_ABc16b64a;
const format_tag_t AcB16b64a = dnnl_AcB16b64a;
const format_tag_t Abc4a = dnnl_Abc4a;
const format_tag_t aBc4b = dnnl_aBc4b;
const format_tag_t ABc4b8a4b = dnnl_ABc4b8a4b;
const format_tag_t AcB4b8a4b = dnnl_AcB4b8a4b;
const format_tag_t ABc4b16a4b = dnnl_ABc4b16a4b;
const format_tag_t AcB4b16a4b = dnnl_AcB4b16a4b;
const format_tag_t ABc4b24a4b = dnnl_ABc4b24a4b;
const format_tag_t AcB4b24a4b = dnnl_AcB4b24a4b;
const format_tag_t ABc4b32a4b = dnnl_ABc4b32a4b;
const format_tag_t AcB4b32a4b = dnnl_AcB4b32a4b;
const format_tag_t ABc4b64a4b = dnnl_ABc4b64a4b;
const format_tag_t AcB4b64a4b = dnnl_AcB4b64a4b;
const format_tag_t ABc2b8a4b = dnnl_ABc2b8a4b;
const format_tag_t ABc16b16a4b = dnnl_ABc16b16a4b;
const format_tag_t ABc16b32a4b = dnnl_ABc16b32a4b;
const format_tag_t ABc16b48a4b = dnnl_ABc16b48a4b;
const format_tag_t ABc16b64a4b = dnnl_ABc16b64a4b;
const format_tag_t ABc16b16a2b = dnnl_ABc16b16a2b;
const format_tag_t ABc16b32a2b = dnnl_ABc16b32a2b;
const format_tag_t ABc16b48a2b = dnnl_ABc16b48a2b;
const format_tag_t ABc16b64a2b = dnnl_ABc16b64a2b;
const format_tag_t ABc16a16b2a = dnnl_ABc16a16b2a;
const format_tag_t ABc4b4a = dnnl_ABc4b4a;
const format_tag_t ABc8a16b2a = dnnl_ABc8a16b2a;
const format_tag_t BAc8a16b2a = dnnl_BAc8a16b2a;
const format_tag_t ABc8a8b = dnnl_ABc8a8b;
const format_tag_t ABc8a2b = dnnl_ABc8a2b;
const format_tag_t ABc8a4b = dnnl_ABc8a4b;
const format_tag_t aBc8b = dnnl_aBc8b;
const format_tag_t ABc8b16a2b = dnnl_ABc8b16a2b;
const format_tag_t AcB8b16a2b = dnnl_AcB8b16a2b;
const format_tag_t ABc8b32a2b = dnnl_ABc8b32a2b;
const format_tag_t AcB8b32a2b = dnnl_AcB8b32a2b;
const format_tag_t ABc8b64a2b = dnnl_ABc8b64a2b;
const format_tag_t AcB8b64a2b = dnnl_AcB8b64a2b;
const format_tag_t ABc8b8a = dnnl_ABc8b8a;
const format_tag_t AcB8b8a = dnnl_AcB8b8a;
const format_tag_t Abcd16a = dnnl_Abcd16a;
const format_tag_t Abcd8a = dnnl_Abcd8a;
const format_tag_t Abcd32a = dnnl_Abcd32a;
const format_tag_t ABcd16a16b = dnnl_ABcd16a16b;
const format_tag_t aBcd16b = dnnl_aBcd16b;
const format_tag_t aBcd32b = dnnl_aBcd32b;
const format_tag_t ABcd16b16a = dnnl_ABcd16b16a;
const format_tag_t AcdB16b16a = dnnl_AcdB16b16a;
const format_tag_t ABcd16b32a = dnnl_ABcd16b32a;
const format_tag_t AcdB16b32a = dnnl_AcdB16b32a;
const format_tag_t ABcd16b48a = dnnl_ABcd16b48a;
const format_tag_t AcdB16b48a = dnnl_AcdB16b48a;
const format_tag_t ABcd16b64a = dnnl_ABcd16b64a;
const format_tag_t AcdB16b64a = dnnl_AcdB16b64a;
const format_tag_t aBCd16b16c = dnnl_aBCd16b16c;
const format_tag_t aBCd16c16b = dnnl_aBCd16c16b;
const format_tag_t Abcd4a = dnnl_Abcd4a;
const format_tag_t aBcd4b = dnnl_aBcd4b;
const format_tag_t ABcd4b8a4b = dnnl_ABcd4b8a4b;
const format_tag_t AcdB4b8a4b = dnnl_AcdB4b8a4b;
const format_tag_t ABcd4b16a4b = dnnl_ABcd4b16a4b;
const format_tag_t AcdB4b16a4b = dnnl_AcdB4b16a4b;
const format_tag_t ABcd4b24a4b = dnnl_ABcd4b24a4b;
const format_tag_t AcdB4b24a4b = dnnl_AcdB4b24a4b;
const format_tag_t ABcd4b32a4b = dnnl_ABcd4b32a4b;
const format_tag_t AcdB4b32a4b = dnnl_AcdB4b32a4b;
const format_tag_t ABcd4b64a4b = dnnl_ABcd4b64a4b;
const format_tag_t AcdB4b64a4b = dnnl_AcdB4b64a4b;
const format_tag_t ABcd16b16a4b = dnnl_ABcd16b16a4b;
const format_tag_t ABcd16b32a4b = dnnl_ABcd16b32a4b;
const format_tag_t ABcd16b48a4b = dnnl_ABcd16b48a4b;
const format_tag_t ABcd16b64a4b = dnnl_ABcd16b64a4b;
const format_tag_t ABcd16b16a2b = dnnl_ABcd16b16a2b;
const format_tag_t ABcd16b32a2b = dnnl_ABcd16b32a2b;
const format_tag_t ABcd16b48a2b = dnnl_ABcd16b48a2b;
const format_tag_t ABcd16b64a2b = dnnl_ABcd16b64a2b;
const format_tag_t ABcd16a16b2a = dnnl_ABcd16a16b2a;
const format_tag_t ABcde16a16b2a = dnnl_ABcde16a16b2a;
const format_tag_t ABcd4b4a = dnnl_ABcd4b4a;
const format_tag_t ABcd4a2b = dnnl_ABcd4a2b;
const format_tag_t ABcd4a4b = dnnl_ABcd4a4b;
const format_tag_t aBCd4c16b4c = dnnl_aBCd4c16b4c;
const format_tag_t aBCd2c8b4c = dnnl_aBCd2c8b4c;
const format_tag_t aBCd16c16b4c = dnnl_aBCd16c16b4c;
const format_tag_t aBCd16c16b2c = dnnl_aBCd16c16b2c;
const format_tag_t aBCd16b16c2b = dnnl_aBCd16b16c2b;
const format_tag_t aBCd4c4b = dnnl_aBCd4c4b;
const format_tag_t aBCd4b4c = dnnl_aBCd4b4c;
const format_tag_t ABcd8a16b2a = dnnl_ABcd8a16b2a;
const format_tag_t BAcd8a16b2a = dnnl_BAcd8a16b2a;
const format_tag_t ABcd8a8b = dnnl_ABcd8a8b;
const format_tag_t ABcd8a4b = dnnl_ABcd8a4b;
const format_tag_t ABcd8a2b = dnnl_ABcd8a2b;
const format_tag_t aBcd8b = dnnl_aBcd8b;
const format_tag_t ABcd8b16a2b = dnnl_ABcd8b16a2b;
const format_tag_t AcdB8b16a2b = dnnl_AcdB8b16a2b;
const format_tag_t ABcd8b32a2b = dnnl_ABcd8b32a2b;
const format_tag_t AcdB8b32a2b = dnnl_AcdB8b32a2b;
const format_tag_t ABcd8b64a2b = dnnl_ABcd8b64a2b;
const format_tag_t AcdB8b64a2b = dnnl_AcdB8b64a2b;
const format_tag_t ABcd2b8a4b = dnnl_ABcd2b8a4b;
const format_tag_t aBCd8b16c2b = dnnl_aBCd8b16c2b;
const format_tag_t aCBd8b16c2b = dnnl_aCBd8b16c2b;
const format_tag_t aBCd2c8b16c2b = dnnl_aBCd2c8b16c2b;
const format_tag_t ABcd8b8a = dnnl_ABcd8b8a;
const format_tag_t AcdB8b8a = dnnl_AcdB8b8a;
const format_tag_t aBCd8b8c = dnnl_aBCd8b8c;
const format_tag_t aBCd8b2c = dnnl_aBCd8b2c;
const format_tag_t aBCd8b4c = dnnl_aBCd8b4c;
const format_tag_t aBCd8c16b2c = dnnl_aBCd8c16b2c;
const format_tag_t aBCd8c8b = dnnl_aBCd8c8b;
const format_tag_t Abcde16a = dnnl_Abcde16a;
const format_tag_t Abcde32a = dnnl_Abcde32a;
const format_tag_t ABcde16a16b = dnnl_ABcde16a16b;
const format_tag_t aBcde16b = dnnl_aBcde16b;
const format_tag_t aBcde32b = dnnl_aBcde32b;
const format_tag_t ABcde16b16a = dnnl_ABcde16b16a;
const format_tag_t AcdeB16b16a = dnnl_AcdeB16b16a;
const format_tag_t ABcde16b32a = dnnl_ABcde16b32a;
const format_tag_t AcdeB16b32a = dnnl_AcdeB16b32a;
const format_tag_t ABcde16b48a = dnnl_ABcde16b48a;
const format_tag_t AcdeB16b48a = dnnl_AcdeB16b48a;
const format_tag_t ABcde16b64a = dnnl_ABcde16b64a;
const format_tag_t AcdeB16b64a = dnnl_AcdeB16b64a;
const format_tag_t aBCde16b16c = dnnl_aBCde16b16c;
const format_tag_t aBCde16c16b = dnnl_aBCde16c16b;
const format_tag_t aBCde2c8b4c = dnnl_aBCde2c8b4c;
const format_tag_t Abcde4a = dnnl_Abcde4a;
const format_tag_t aBcde4b = dnnl_aBcde4b;
const format_tag_t ABcde4b4a = dnnl_ABcde4b4a;
const format_tag_t ABcde4a2b = dnnl_ABcde4a2b;
const format_tag_t ABcde4a4b = dnnl_ABcde4a4b;
const format_tag_t aBCde4b4c = dnnl_aBCde4b4c;
const format_tag_t aBCde4c16b4c = dnnl_aBCde4c16b4c;
const format_tag_t aBCde16c16b4c = dnnl_aBCde16c16b4c;
const format_tag_t aBCde16c16b2c = dnnl_aBCde16c16b2c;
const format_tag_t aBCde16b16c2b = dnnl_aBCde16b16c2b;
const format_tag_t aBCde4c4b = dnnl_aBCde4c4b;
const format_tag_t Abcde8a = dnnl_Abcde8a;
const format_tag_t ABcde8a8b = dnnl_ABcde8a8b;
const format_tag_t ABcde8a2b = dnnl_ABcde8a2b;
const format_tag_t ABcde8a4b = dnnl_ABcde8a4b;
const format_tag_t aBcde8b = dnnl_aBcde8b;
const format_tag_t ABcde8b16a2b = dnnl_ABcde8b16a2b;
const format_tag_t AcdeB8b16a2b = dnnl_AcdeB8b16a2b;
const format_tag_t ABcde8b32a2b = dnnl_ABcde8b32a2b;
const format_tag_t AcdeB8b32a2b = dnnl_AcdeB8b32a2b;
const format_tag_t ABcde8b64a2b = dnnl_ABcde8b64a2b;
const format_tag_t AcdeB8b64a2b = dnnl_AcdeB8b64a2b;
const format_tag_t ABcde8a16b2a = dnnl_ABcde8a16b2a;
const format_tag_t BAcde8a16b2a = dnnl_BAcde8a16b2a;
const format_tag_t ABcde4b8a4b = dnnl_ABcde4b8a4b;
const format_tag_t AcdeB4b8a4b = dnnl_AcdeB4b8a4b;
const format_tag_t ABcde4b16a4b = dnnl_ABcde4b16a4b;
const format_tag_t AcdeB4b16a4b = dnnl_AcdeB4b16a4b;
const format_tag_t ABcde4b24a4b = dnnl_ABcde4b24a4b;
const format_tag_t AcdeB4b24a4b = dnnl_AcdeB4b24a4b;
const format_tag_t ABcde4b32a4b = dnnl_ABcde4b32a4b;
const format_tag_t AcdeB4b32a4b = dnnl_AcdeB4b32a4b;
const format_tag_t ABcde4b64a4b = dnnl_ABcde4b64a4b;
const format_tag_t AcdeB4b64a4b = dnnl_AcdeB4b64a4b;
const format_tag_t ABcde16b16a4b = dnnl_ABcde16b16a4b;
const format_tag_t ABcde16b32a4b = dnnl_ABcde16b32a4b;
const format_tag_t ABcde16b48a4b = dnnl_ABcde16b48a4b;
const format_tag_t ABcde16b64a4b = dnnl_ABcde16b64a4b;
const format_tag_t ABcde2b8a4b = dnnl_ABcde2b8a4b;
const format_tag_t aBCde8b16c2b = dnnl_aBCde8b16c2b;
const format_tag_t aCBde8b16c2b = dnnl_aCBde8b16c2b;
const format_tag_t ABcde8b8a = dnnl_ABcde8b8a;
const format_tag_t AcdeB8b8a = dnnl_AcdeB8b8a;
const format_tag_t aBCde8b8c = dnnl_aBCde8b8c;
const format_tag_t aBCde8b2c = dnnl_aBCde8b2c;
const format_tag_t aBCde8b4c = dnnl_aBCde8b4c;
const format_tag_t ABc4a8b8a4b = dnnl_ABc4a8b8a4b;
const format_tag_t ABcd4a8b8a4b = dnnl_ABcd4a8b8a4b;
const format_tag_t ABcde4a8b8a4b = dnnl_ABcde4a8b8a4b;
const format_tag_t ABcd2a8b8a2b = dnnl_ABcd2a8b8a2b;
const format_tag_t ABcde4a8b8a2b = dnnl_ABcde4a8b8a2b;
const format_tag_t ABcd4a8b8a2b = dnnl_ABcd4a8b8a2b;
const format_tag_t ABc4a8b8a2b = dnnl_ABc4a8b8a2b;
const format_tag_t aBCdef4b8c8b2c = dnnl_aBCdef4b8c8b2c;
const format_tag_t aBCde4b8c8b2c = dnnl_aBCde4b8c8b2c;
const format_tag_t aBCd4b8c8b2c = dnnl_aBCd4b8c8b2c;
const format_tag_t BAcde4b8a8b2a = dnnl_BAcde4b8a8b2a;
const format_tag_t BAcd4b8a8b2a = dnnl_BAcd4b8a8b2a;
const format_tag_t BAc4b8a8b2a = dnnl_BAc4b8a8b2a;
const format_tag_t aCBdef4c8b8c2b = dnnl_aCBdef4c8b8c2b;
const format_tag_t aCBde4c8b8c2b = dnnl_aCBde4c8b8c2b;
const format_tag_t aCBd4c8b8c2b = dnnl_aCBd4c8b8c2b;
const format_tag_t aBCd4b8c8b4c = dnnl_aBCd4b8c8b4c;
const format_tag_t aBCde4b8c8b4c = dnnl_aBCde4b8c8b4c;
const format_tag_t aBCdef4b8c8b4c = dnnl_aBCdef4b8c8b4c;
const format_tag_t BAc4b8a8b4a = dnnl_BAc4b8a8b4a;
const format_tag_t BAcd4b8a8b4a = dnnl_BAcd4b8a8b4a;
const format_tag_t BAcde4b8a8b4a = dnnl_BAcde4b8a8b4a;
const format_tag_t aCBd4c8b8c4b = dnnl_aCBd4c8b8c4b;
const format_tag_t aCBde4c8b8c4b = dnnl_aCBde4c8b8c4b;
const format_tag_t aCBdef4c8b8c4b = dnnl_aCBdef4c8b8c4b;
const format_tag_t aBCde2b8c8b2c = dnnl_aBCde2b8c8b2c;
const format_tag_t aBCde8c16b2c = dnnl_aBCde8c16b2c;
const format_tag_t aBCde8c8b = dnnl_aBCde8c8b;
const format_tag_t aBcdef16b = dnnl_aBcdef16b;
const format_tag_t aBCdef16b16c = dnnl_aBCdef16b16c;
const format_tag_t aBCdef16b16c2b = dnnl_aBCdef16b16c2b;
const format_tag_t aBCdef16c16b = dnnl_aBCdef16c16b;
const format_tag_t aBCdef4c16b4c = dnnl_aBCdef4c16b4c;
const format_tag_t aBCdef2c8b4c = dnnl_aBCdef2c8b4c;
const format_tag_t aBcdef4b = dnnl_aBcdef4b;
const format_tag_t aBCdef4c4b = dnnl_aBCdef4c4b;
const format_tag_t aBCdef4b4c = dnnl_aBCdef4b4c;
const format_tag_t aBCdef8b8c = dnnl_aBCdef8b8c;
const format_tag_t aBCdef8b2c = dnnl_aBCdef8b2c;
const format_tag_t aBCdef8b4c = dnnl_aBCdef8b4c;
const format_tag_t aBCdef8c16b2c = dnnl_aBCdef8c16b2c;
const format_tag_t aBCdef8b16c2b = dnnl_aBCdef8b16c2b;
const format_tag_t aCBdef8b16c2b = dnnl_aCBdef8b16c2b;
const format_tag_t aBCdef8c8b = dnnl_aBCdef8c8b;
const format_tag_t aBdc16b = dnnl_aBdc16b;
const format_tag_t aBdC16b2c = dnnl_aBdC16b2c;
const format_tag_t aBdC16b4c = dnnl_aBdC16b4c;
const format_tag_t aBdc4b = dnnl_aBdc4b;
const format_tag_t aBdc8b = dnnl_aBdc8b;
const format_tag_t aBdC8b2c = dnnl_aBdC8b2c;
const format_tag_t aBdC8b4c = dnnl_aBdC8b4c;
const format_tag_t aBdec16b = dnnl_aBdec16b;
const format_tag_t aBdeC16b2c = dnnl_aBdeC16b2c;
const format_tag_t aBdeC16b4c = dnnl_aBdeC16b4c;
const format_tag_t aBdec4b = dnnl_aBdec4b;
const format_tag_t aBdec8b = dnnl_aBdec8b;
const format_tag_t aBdeC8b2c = dnnl_aBdeC8b2c;
const format_tag_t aBdeC8b4c = dnnl_aBdeC8b4c;
const format_tag_t aBdefc16b = dnnl_aBdefc16b;
const format_tag_t aBdefC16b2c = dnnl_aBdefC16b2c;
const format_tag_t aBdefC16b4c = dnnl_aBdefC16b4c;
const format_tag_t aCBdef16c16b = dnnl_aCBdef16c16b;
const format_tag_t aCBdef8b8c = dnnl_aCBdef8b8c;
const format_tag_t aCBdef16b16c = dnnl_aCBdef16b16c;
const format_tag_t aBdefc4b = dnnl_aBdefc4b;
const format_tag_t aBdefc8b = dnnl_aBdefc8b;
const format_tag_t aBdefC8b2c = dnnl_aBdefC8b2c;
const format_tag_t aBdefC8b4c = dnnl_aBdefC8b4c;
const format_tag_t aBdfec16b = dnnl_aBdfec16b;
const format_tag_t aBedc16b = dnnl_aBedc16b;
const format_tag_t Acb16a = dnnl_Acb16a;
const format_tag_t AcB16a2b = dnnl_AcB16a2b;
const format_tag_t AcB16a4b = dnnl_AcB16a4b;
const format_tag_t Acb4a = dnnl_Acb4a;
const format_tag_t Acb8a = dnnl_Acb8a;
const format_tag_t AcB8a2b = dnnl_AcB8a2b;
const format_tag_t AcB8a4b = dnnl_AcB8a4b;
const format_tag_t aCBd8b8c = dnnl_aCBd8b8c;
const format_tag_t aCBd16b16c = dnnl_aCBd16b16c;
const format_tag_t aCBd16c16b = dnnl_aCBd16c16b;
const format_tag_t aCBde8b8c = dnnl_aCBde8b8c;
const format_tag_t aCBde16b16c = dnnl_aCBde16b16c;
const format_tag_t aCBde16c16b = dnnl_aCBde16c16b;
const format_tag_t Acdb16a = dnnl_Acdb16a;
const format_tag_t AcdB16a2b = dnnl_AcdB16a2b;
const format_tag_t AcdB16a4b = dnnl_AcdB16a4b;
const format_tag_t Acdb4a = dnnl_Acdb4a;
const format_tag_t Acdb8a = dnnl_Acdb8a;
const format_tag_t AcdB8a2b = dnnl_AcdB8a2b;
const format_tag_t AcdB8a4b = dnnl_AcdB8a4b;
const format_tag_t Acdeb16a = dnnl_Acdeb16a;
const format_tag_t AcdeB16a2b = dnnl_AcdeB16a2b;
const format_tag_t AcdeB16a4b = dnnl_AcdeB16a4b;
const format_tag_t Acdeb4a = dnnl_Acdeb4a;
const format_tag_t Acdeb8a = dnnl_Acdeb8a;
const format_tag_t AcdeB8a2b = dnnl_AcdeB8a2b;
const format_tag_t AcdeB8a4b = dnnl_AcdeB8a4b;
const format_tag_t Acedb16a = dnnl_Acedb16a;
const format_tag_t Adcb16a = dnnl_Adcb16a;
const format_tag_t BAc8a8b = dnnl_BAc8a8b;
const format_tag_t BAc16a16b = dnnl_BAc16a16b;
const format_tag_t BAcd8a8b = dnnl_BAcd8a8b;
const format_tag_t BAcd16a16b = dnnl_BAcd16a16b;
const format_tag_t ABc32a16b = dnnl_ABc32a16b;
const format_tag_t ABcd32a16b = dnnl_ABcd32a16b;
const format_tag_t ABcde32a16b = dnnl_ABcde32a16b;
const format_tag_t ABc40a16b = dnnl_ABc40a16b;
const format_tag_t ABcd40a16b = dnnl_ABcd40a16b;
const format_tag_t ABcde40a16b = dnnl_ABcde40a16b;
const format_tag_t ABc32a32b = dnnl_ABc32a32b;
const format_tag_t BAcde8a8b = dnnl_BAcde8a8b;
const format_tag_t BAcde16a16b = dnnl_BAcde16a16b;
const format_tag_t ABcd32a32b = dnnl_ABcd32a32b;
const format_tag_t ABcde32a32b = dnnl_ABcde32a32b;
const format_tag_t ABc40a32b = dnnl_ABc40a32b;
const format_tag_t ABcd40a32b = dnnl_ABcd40a32b;
const format_tag_t ABcde40a32b = dnnl_ABcde40a32b;
const format_tag_t BAcde16b16a = dnnl_BAcde16b16a;
const format_tag_t aBdec32b = dnnl_aBdec32b;
const format_tag_t Abcdef16a = dnnl_Abcdef16a;
const format_tag_t Abcdef32a = dnnl_Abcdef32a;
const format_tag_t Acdb32a = dnnl_Acdb32a;
const format_tag_t BAc16b16a = dnnl_BAc16b16a;
const format_tag_t BAcd16b16a = dnnl_BAcd16b16a;
const format_tag_t aBCd2b4c2b = dnnl_aBCd2b4c2b;
const format_tag_t aBCde2b4c2b = dnnl_aBCde2b4c2b;
const format_tag_t aBCdef2b4c2b = dnnl_aBCdef2b4c2b;
const format_tag_t aBCd2c4b2c = dnnl_aBCd2c4b2c;
const format_tag_t aBCde2c4b2c = dnnl_aBCde2c4b2c;
const format_tag_t aBCdef2c4b2c = dnnl_aBCdef2c4b2c;
const format_tag_t aBCd4b8c2b = dnnl_aBCd4b8c2b;
const format_tag_t aBCde4b8c2b = dnnl_aBCde4b8c2b;
const format_tag_t aBCdef4b8c2b = dnnl_aBCdef4b8c2b;
const format_tag_t aBCd4c8b2c = dnnl_aBCd4c8b2c;
const format_tag_t aBCde4c8b2c = dnnl_aBCde4c8b2c;
const format_tag_t aBCdef4c8b2c = dnnl_aBCdef4c8b2c;
const format_tag_t AB32a32b8a4b = dnnl_AB32a32b8a4b;
const format_tag_t AB8a4b = dnnl_AB8a4b;
const format_tag_t AB32a32b8a2b = dnnl_AB32a32b8a2b;
const format_tag_t AB8a2b = dnnl_AB8a2b;
const format_tag_t abDc16d = dnnl_abDc16d;
const format_tag_t abDc32d = dnnl_abDc32d;
const format_tag_t abDC16d4c = dnnl_abDC16d4c;
const format_tag_t abDC32d4c = dnnl_abDC32d4c;
const format_tag_t abCd4c = dnnl_abCd4c;
const format_tag_t abCde4c = dnnl_abCde4c;
const format_tag_t abCdef4c = dnnl_abCdef4c;
const format_tag_t abCd32c = dnnl_abCd32c;
const format_tag_t abCde32c = dnnl_abCde32c;
const format_tag_t abCdef32c = dnnl_abCdef32c;
const format_tag_t abdEc16e = dnnl_abdEc16e;
const format_tag_t abdEc32e = dnnl_abdEc32e;
const format_tag_t abdEC16e4c = dnnl_abdEC16e4c;
const format_tag_t abdEC32e2c = dnnl_abdEC32e2c;
const format_tag_t abdEC32e4c = dnnl_abdEC32e4c;
const format_tag_t abdEC64e2c = dnnl_abdEC64e2c;
const format_tag_t abdEC64e4c = dnnl_abdEC64e4c;
const format_tag_t abdCe16c = dnnl_abdCe16c;
const format_tag_t abdCe32c = dnnl_abdCe32c;
const format_tag_t abdCE32c2e = dnnl_abdCE32c2e;
const format_tag_t aBCdef16c16b4c = dnnl_aBCdef16c16b4c;
const format_tag_t ABcde16b16a2b = dnnl_ABcde16b16a2b;
const format_tag_t ABcde16b32a2b = dnnl_ABcde16b32a2b;
const format_tag_t ABcde16b48a2b = dnnl_ABcde16b48a2b;
const format_tag_t ABcde16b64a2b = dnnl_ABcde16b64a2b;
const format_tag_t aBCdef16c16b2c = dnnl_aBCdef16c16b2c;
const format_tag_t cBa2b = dnnl_cBa2b;
const format_tag_t cBa4b = dnnl_cBa4b;
const format_tag_t adcb = dnnl_adcb;
const format_tag_t adCb2c = dnnl_adCb2c;
const format_tag_t adCb4c = dnnl_adCb4c;
const format_tag_t cdBa2b = dnnl_cdBa2b;
const format_tag_t cdBa4b = dnnl_cdBa4b;
const format_tag_t adecb = dnnl_adecb;
const format_tag_t adeCb2c = dnnl_adeCb2c;
const format_tag_t adeCb4c = dnnl_adeCb4c;
const format_tag_t cdeBa2b = dnnl_cdeBa2b;
const format_tag_t cdeBa4b = dnnl_cdeBa4b;
const format_tag_t adefcb = dnnl_adefcb;
const format_tag_t adefCb2c = dnnl_adefCb2c;
const format_tag_t adefCb4c = dnnl_adefCb4c;
const format_tag_t Acb32a = dnnl_Acb32a;
const format_tag_t AcB32a2b = dnnl_AcB32a2b;
const format_tag_t AcB32a4b = dnnl_AcB32a4b;
const format_tag_t Acb48a = dnnl_Acb48a;
const format_tag_t AcB48a2b = dnnl_AcB48a2b;
const format_tag_t AcB48a4b = dnnl_AcB48a4b;
const format_tag_t Acb64a = dnnl_Acb64a;
const format_tag_t AcB64a2b = dnnl_AcB64a2b;
const format_tag_t AcB64a4b = dnnl_AcB64a4b;
const format_tag_t aBdc32b = dnnl_aBdc32b;
const format_tag_t aBdC32b2c = dnnl_aBdC32b2c;
const format_tag_t aBdC32b4c = dnnl_aBdC32b4c;
const format_tag_t aBdc48b = dnnl_aBdc48b;
const format_tag_t aBdC48b2c = dnnl_aBdC48b2c;
const format_tag_t aBdC48b4c = dnnl_aBdC48b4c;
const format_tag_t aBdc64b = dnnl_aBdc64b;
const format_tag_t aBdC64b2c = dnnl_aBdC64b2c;
const format_tag_t aBdC64b4c = dnnl_aBdC64b4c;
const format_tag_t AcdB32a2b = dnnl_AcdB32a2b;
const format_tag_t AcdB32a4b = dnnl_AcdB32a4b;
const format_tag_t Acdb48a = dnnl_Acdb48a;
const format_tag_t AcdB48a2b = dnnl_AcdB48a2b;
const format_tag_t AcdB48a4b = dnnl_AcdB48a4b;
const format_tag_t Acdb64a = dnnl_Acdb64a;
const format_tag_t AcdB64a2b = dnnl_AcdB64a2b;
const format_tag_t AcdB64a4b = dnnl_AcdB64a4b;
const format_tag_t aBdeC32b2c = dnnl_aBdeC32b2c;
const format_tag_t aBdeC32b4c = dnnl_aBdeC32b4c;
const format_tag_t aBdec48b = dnnl_aBdec48b;
const format_tag_t aBdeC48b2c = dnnl_aBdeC48b2c;
const format_tag_t aBdeC48b4c = dnnl_aBdeC48b4c;
const format_tag_t aBdec64b = dnnl_aBdec64b;
const format_tag_t aBdeC64b2c = dnnl_aBdeC64b2c;
const format_tag_t aBdeC64b4c = dnnl_aBdeC64b4c;
const format_tag_t Acdeb32a = dnnl_Acdeb32a;
const format_tag_t AcdeB32a2b = dnnl_AcdeB32a2b;
const format_tag_t AcdeB32a4b = dnnl_AcdeB32a4b;
const format_tag_t Acdeb48a = dnnl_Acdeb48a;
const format_tag_t AcdeB48a2b = dnnl_AcdeB48a2b;
const format_tag_t AcdeB48a4b = dnnl_AcdeB48a4b;
const format_tag_t Acdeb64a = dnnl_Acdeb64a;
const format_tag_t AcdeB64a2b = dnnl_AcdeB64a2b;
const format_tag_t AcdeB64a4b = dnnl_AcdeB64a4b;
const format_tag_t aBdefc32b = dnnl_aBdefc32b;
const format_tag_t aBdefC32b2c = dnnl_aBdefC32b2c;
const format_tag_t aBdefC32b4c = dnnl_aBdefC32b4c;
const format_tag_t aBdefc48b = dnnl_aBdefc48b;
const format_tag_t aBdefC48b2c = dnnl_aBdefC48b2c;
const format_tag_t aBdefC48b4c = dnnl_aBdefC48b4c;
const format_tag_t aBdefc64b = dnnl_aBdefc64b;
const format_tag_t aBdefC64b2c = dnnl_aBdefC64b2c;
const format_tag_t aBdefC64b4c = dnnl_aBdefC64b4c;
const format_tag_t aBdeC16c16b2c = dnnl_aBdeC16c16b2c;
const format_tag_t aBdeC16c16b4c = dnnl_aBdeC16c16b4c;
const format_tag_t aBdefC16c16b2c = dnnl_aBdefC16c16b2c;
const format_tag_t aBdefC16c16b4c = dnnl_aBdefC16c16b4c;
const format_tag_t AcB16b16a2b = dnnl_AcB16b16a2b;
const format_tag_t AcB16b16a4b = dnnl_AcB16b16a4b;
const format_tag_t aBdC16c16b2c = dnnl_aBdC16c16b2c;
const format_tag_t aBdC16c16b4c = dnnl_aBdC16c16b4c;
const format_tag_t AcdB16b16a2b = dnnl_AcdB16b16a2b;
const format_tag_t AcdB16b16a4b = dnnl_AcdB16b16a4b;
const format_tag_t AcdeB16b16a2b = dnnl_AcdeB16b16a2b;
const format_tag_t AcdeB16b16a4b = dnnl_AcdeB16b16a4b;
const format_tag_t AcB16b32a2b = dnnl_AcB16b32a2b;
const format_tag_t AcB16b32a4b = dnnl_AcB16b32a4b;
const format_tag_t AcB16b48a2b = dnnl_AcB16b48a2b;
const format_tag_t AcB16b48a4b = dnnl_AcB16b48a4b;
const format_tag_t AcB16b64a2b = dnnl_AcB16b64a2b;
const format_tag_t AcB16b64a4b = dnnl_AcB16b64a4b;
const format_tag_t aBdC16c32b2c = dnnl_aBdC16c32b2c;
const format_tag_t aBdC16c32b4c = dnnl_aBdC16c32b4c;
const format_tag_t aBdC16c48b2c = dnnl_aBdC16c48b2c;
const format_tag_t aBdC16c48b4c = dnnl_aBdC16c48b4c;
const format_tag_t aBdC16c64b2c = dnnl_aBdC16c64b2c;
const format_tag_t aBdC16c64b4c = dnnl_aBdC16c64b4c;
const format_tag_t AcdB16b32a2b = dnnl_AcdB16b32a2b;
const format_tag_t AcdB16b32a4b = dnnl_AcdB16b32a4b;
const format_tag_t AcdB16b48a2b = dnnl_AcdB16b48a2b;
const format_tag_t AcdB16b48a4b = dnnl_AcdB16b48a4b;
const format_tag_t AcdB16b64a2b = dnnl_AcdB16b64a2b;
const format_tag_t AcdB16b64a4b = dnnl_AcdB16b64a4b;
const format_tag_t aBdeC16c32b2c = dnnl_aBdeC16c32b2c;
const format_tag_t aBdeC16c32b4c = dnnl_aBdeC16c32b4c;
const format_tag_t aBdeC16c48b2c = dnnl_aBdeC16c48b2c;
const format_tag_t aBdeC16c48b4c = dnnl_aBdeC16c48b4c;
const format_tag_t aBdeC16c64b2c = dnnl_aBdeC16c64b2c;
const format_tag_t aBdeC16c64b4c = dnnl_aBdeC16c64b4c;
const format_tag_t AcdeB16b32a2b = dnnl_AcdeB16b32a2b;
const format_tag_t AcdeB16b32a4b = dnnl_AcdeB16b32a4b;
const format_tag_t AcdeB16b48a2b = dnnl_AcdeB16b48a2b;
const format_tag_t AcdeB16b48a4b = dnnl_AcdeB16b48a4b;
const format_tag_t AcdeB16b64a2b = dnnl_AcdeB16b64a2b;
const format_tag_t AcdeB16b64a4b = dnnl_AcdeB16b64a4b;
const format_tag_t aBdefC16c32b2c = dnnl_aBdefC16c32b2c;
const format_tag_t aBdefC16c32b4c = dnnl_aBdefC16c32b4c;
const format_tag_t aBdefC16c48b2c = dnnl_aBdefC16c48b2c;
const format_tag_t aBdefC16c48b4c = dnnl_aBdefC16c48b4c;
const format_tag_t aBdefC16c64b2c = dnnl_aBdefC16c64b2c;
const format_tag_t aBdefC16c64b4c = dnnl_aBdefC16c64b4c;
const format_tag_t decbA16a = dnnl_decbA16a;
const format_tag_t decbA8a = dnnl_decbA8a;
const format_tag_t defcbA16a = dnnl_defcbA16a;
const format_tag_t defcbA8a = dnnl_defcbA8a;
const format_tag_t aCB16c2b = dnnl_aCB16c2b;
const format_tag_t aCB16c4b = dnnl_aCB16c4b;
const format_tag_t BA16b2a = dnnl_BA16b2a;
const format_tag_t BA16b4a = dnnl_BA16b4a;
const format_tag_t aBC16b16c = dnnl_aBC16b16c;
const format_tag_t aBC16b32c = dnnl_aBC16b32c;
const format_tag_t AB16a16b = dnnl_AB16a16b;
const format_tag_t AB16a32b = dnnl_AB16a32b;
const format_tag_t ABcd16a32b = dnnl_ABcd16a32b;
const format_tag_t aCdefB16b32c2b = dnnl_aCdefB16b32c2b;
const format_tag_t aCdefB16b32c4b = dnnl_aCdefB16b32c4b;
const format_tag_t aCdefB16b48c2b = dnnl_aCdefB16b48c2b;
const format_tag_t aCdefB16b48c4b = dnnl_aCdefB16b48c4b;
const format_tag_t aCdefB16b64c2b = dnnl_aCdefB16b64c2b;
const format_tag_t aCdefB16b64c4b = dnnl_aCdefB16b64c4b;
const format_tag_t BcdeA16a32b2a = dnnl_BcdeA16a32b2a;
const format_tag_t BcdeA16a32b4a = dnnl_BcdeA16a32b4a;
const format_tag_t BcdeA16a48b2a = dnnl_BcdeA16a48b2a;
const format_tag_t BcdeA16a48b4a = dnnl_BcdeA16a48b4a;
const format_tag_t BcdeA16a64b2a = dnnl_BcdeA16a64b2a;
const format_tag_t BcdeA16a64b4a = dnnl_BcdeA16a64b4a;
const format_tag_t aCdefb32c = dnnl_aCdefb32c;
const format_tag_t aCdefB32c2b = dnnl_aCdefB32c2b;
const format_tag_t aCdefB32c4b = dnnl_aCdefB32c4b;
const format_tag_t aCdefb48c = dnnl_aCdefb48c;
const format_tag_t aCdefB48c2b = dnnl_aCdefB48c2b;
const format_tag_t aCdefB48c4b = dnnl_aCdefB48c4b;
const format_tag_t aCdefb64c = dnnl_aCdefb64c;
const format_tag_t aCdefB64c2b = dnnl_aCdefB64c2b;
const format_tag_t aCdefB64c4b = dnnl_aCdefB64c4b;
const format_tag_t Bcdea32b = dnnl_Bcdea32b;
const format_tag_t BcdeA32b2a = dnnl_BcdeA32b2a;
const format_tag_t BcdeA32b4a = dnnl_BcdeA32b4a;
const format_tag_t Bcdea48b = dnnl_Bcdea48b;
const format_tag_t BcdeA48b2a = dnnl_BcdeA48b2a;
const format_tag_t BcdeA48b4a = dnnl_BcdeA48b4a;
const format_tag_t Bcdea64b = dnnl_Bcdea64b;
const format_tag_t BcdeA64b2a = dnnl_BcdeA64b2a;
const format_tag_t BcdeA64b4a = dnnl_BcdeA64b4a;
const format_tag_t Bca32b = dnnl_Bca32b;
const format_tag_t BcA32b2a = dnnl_BcA32b2a;
const format_tag_t BcA32b4a = dnnl_BcA32b4a;
const format_tag_t Bca48b = dnnl_Bca48b;
const format_tag_t BcA48b2a = dnnl_BcA48b2a;
const format_tag_t BcA48b4a = dnnl_BcA48b4a;
const format_tag_t Bca64b = dnnl_Bca64b;
const format_tag_t BcA64b2a = dnnl_BcA64b2a;
const format_tag_t BcA64b4a = dnnl_BcA64b4a;
const format_tag_t aCdb32c = dnnl_aCdb32c;
const format_tag_t aCdB32c2b = dnnl_aCdB32c2b;
const format_tag_t aCdB32c4b = dnnl_aCdB32c4b;
const format_tag_t aCdb48c = dnnl_aCdb48c;
const format_tag_t aCdB48c2b = dnnl_aCdB48c2b;
const format_tag_t aCdB48c4b = dnnl_aCdB48c4b;
const format_tag_t aCdb64c = dnnl_aCdb64c;
const format_tag_t aCdB64c2b = dnnl_aCdB64c2b;
const format_tag_t aCdB64c4b = dnnl_aCdB64c4b;
const format_tag_t BcA16a16b2a = dnnl_BcA16a16b2a;
const format_tag_t BcA16a16b4a = dnnl_BcA16a16b4a;
const format_tag_t BcdA16a16b2a = dnnl_BcdA16a16b2a;
const format_tag_t BcdA16a16b4a = dnnl_BcdA16a16b4a;
const format_tag_t BcdeA16a16b2a = dnnl_BcdeA16a16b2a;
const format_tag_t BcdeA16a16b4a = dnnl_BcdeA16a16b4a;
const format_tag_t aCdB16b16c2b = dnnl_aCdB16b16c2b;
const format_tag_t aCdB16b16c4b = dnnl_aCdB16b16c4b;
const format_tag_t aCdeB16b16c2b = dnnl_aCdeB16b16c2b;
const format_tag_t aCdeB16b16c4b = dnnl_aCdeB16b16c4b;
const format_tag_t aCdefB16b16c2b = dnnl_aCdefB16b16c2b;
const format_tag_t aCdefB16b16c4b = dnnl_aCdefB16b16c4b;
const format_tag_t BcA16a32b2a = dnnl_BcA16a32b2a;
const format_tag_t BcA16a32b4a = dnnl_BcA16a32b4a;
const format_tag_t BcA16a48b2a = dnnl_BcA16a48b2a;
const format_tag_t BcA16a48b4a = dnnl_BcA16a48b4a;
const format_tag_t BcA16a64b2a = dnnl_BcA16a64b2a;
const format_tag_t BcA16a64b4a = dnnl_BcA16a64b4a;
const format_tag_t aCdB16b32c2b = dnnl_aCdB16b32c2b;
const format_tag_t aCdB16b32c4b = dnnl_aCdB16b32c4b;
const format_tag_t aCdB16b48c2b = dnnl_aCdB16b48c2b;
const format_tag_t aCdB16b48c4b = dnnl_aCdB16b48c4b;
const format_tag_t aCdB16b64c2b = dnnl_aCdB16b64c2b;
const format_tag_t aCdB16b64c4b = dnnl_aCdB16b64c4b;
const format_tag_t BcdA16a32b2a = dnnl_BcdA16a32b2a;
const format_tag_t BcdA16a32b4a = dnnl_BcdA16a32b4a;
const format_tag_t BcdA16a48b2a = dnnl_BcdA16a48b2a;
const format_tag_t BcdA16a48b4a = dnnl_BcdA16a48b4a;
const format_tag_t BcdA16a64b2a = dnnl_BcdA16a64b2a;
const format_tag_t BcdA16a64b4a = dnnl_BcdA16a64b4a;
const format_tag_t aCdeB16b32c2b = dnnl_aCdeB16b32c2b;
const format_tag_t aCdeB16b32c4b = dnnl_aCdeB16b32c4b;
const format_tag_t aCdeB16b48c2b = dnnl_aCdeB16b48c2b;
const format_tag_t aCdeB16b48c4b = dnnl_aCdeB16b48c4b;
const format_tag_t aCdeB16b64c2b = dnnl_aCdeB16b64c2b;
const format_tag_t aCdeB16b64c4b = dnnl_aCdeB16b64c4b;
const format_tag_t Bca8b = dnnl_Bca8b;
const format_tag_t BcA8b2a = dnnl_BcA8b2a;
const format_tag_t BcA8b4a = dnnl_BcA8b4a;
const format_tag_t Bcda8b = dnnl_Bcda8b;
const format_tag_t BcdA8b2a = dnnl_BcdA8b2a;
const format_tag_t BcdA8b4a = dnnl_BcdA8b4a;
const format_tag_t Bcdea8b = dnnl_Bcdea8b;
const format_tag_t BcdeA8b2a = dnnl_BcdeA8b2a;
const format_tag_t BcdeA8b4a = dnnl_BcdeA8b4a;
const format_tag_t aCdb8c = dnnl_aCdb8c;
const format_tag_t aCdB8c2b = dnnl_aCdB8c2b;
const format_tag_t aCdB8c4b = dnnl_aCdB8c4b;
const format_tag_t aCdeb8c = dnnl_aCdeb8c;
const format_tag_t aCdeB8c2b = dnnl_aCdeB8c2b;
const format_tag_t aCdeB8c4b = dnnl_aCdeB8c4b;
const format_tag_t aCdefb8c = dnnl_aCdefb8c;
const format_tag_t aCdefB8c2b = dnnl_aCdefB8c2b;
const format_tag_t aCdefB8c4b = dnnl_aCdefB8c4b;
const format_tag_t Bca16b = dnnl_Bca16b;
const format_tag_t BcA16b2a = dnnl_BcA16b2a;
const format_tag_t BcA16b4a = dnnl_BcA16b4a;
const format_tag_t Bcda16b = dnnl_Bcda16b;
const format_tag_t BcdA16b2a = dnnl_BcdA16b2a;
const format_tag_t BcdA16b4a = dnnl_BcdA16b4a;
const format_tag_t Bcdea16b = dnnl_Bcdea16b;
const format_tag_t BcdeA16b2a = dnnl_BcdeA16b2a;
const format_tag_t BcdeA16b4a = dnnl_BcdeA16b4a;
const format_tag_t aCdb16c = dnnl_aCdb16c;
const format_tag_t aCdB16c2b = dnnl_aCdB16c2b;
const format_tag_t aCdB16c4b = dnnl_aCdB16c4b;
const format_tag_t aCdeb16c = dnnl_aCdeb16c;
const format_tag_t aCdeB16c2b = dnnl_aCdeB16c2b;
const format_tag_t aCdeB16c4b = dnnl_aCdeB16c4b;
const format_tag_t aCdefb16c = dnnl_aCdefb16c;
const format_tag_t aCdefB16c2b = dnnl_aCdefB16c2b;
const format_tag_t aCdefB16c4b = dnnl_aCdefB16c4b;
const format_tag_t Bca24b = dnnl_Bca24b;
const format_tag_t BcA24b2a = dnnl_BcA24b2a;
const format_tag_t BcA24b4a = dnnl_BcA24b4a;
const format_tag_t Bcda24b = dnnl_Bcda24b;
const format_tag_t BcdA24b2a = dnnl_BcdA24b2a;
const format_tag_t BcdA24b4a = dnnl_BcdA24b4a;
const format_tag_t Bcdea24b = dnnl_Bcdea24b;
const format_tag_t BcdeA24b2a = dnnl_BcdeA24b2a;
const format_tag_t BcdeA24b4a = dnnl_BcdeA24b4a;
const format_tag_t aCdb24c = dnnl_aCdb24c;
const format_tag_t aCdB24c2b = dnnl_aCdB24c2b;
const format_tag_t aCdB24c4b = dnnl_aCdB24c4b;
const format_tag_t aCdeb24c = dnnl_aCdeb24c;
const format_tag_t aCdeB24c2b = dnnl_aCdeB24c2b;
const format_tag_t aCdeB24c4b = dnnl_aCdeB24c4b;
const format_tag_t aCdefb24c = dnnl_aCdefb24c;
const format_tag_t aCdefB24c2b = dnnl_aCdefB24c2b;
const format_tag_t aCdefB24c4b = dnnl_aCdefB24c4b;
const format_tag_t Bcda32b = dnnl_Bcda32b;
const format_tag_t BcdA32b2a = dnnl_BcdA32b2a;
const format_tag_t BcdA32b4a = dnnl_BcdA32b4a;
const format_tag_t Bcda48b = dnnl_Bcda48b;
const format_tag_t BcdA48b2a = dnnl_BcdA48b2a;
const format_tag_t BcdA48b4a = dnnl_BcdA48b4a;
const format_tag_t Bcda64b = dnnl_Bcda64b;
const format_tag_t BcdA64b2a = dnnl_BcdA64b2a;
const format_tag_t BcdA64b4a = dnnl_BcdA64b4a;
const format_tag_t aCdeb32c = dnnl_aCdeb32c;
const format_tag_t aCdeB32c2b = dnnl_aCdeB32c2b;
const format_tag_t aCdeB32c4b = dnnl_aCdeB32c4b;
const format_tag_t aCdeb48c = dnnl_aCdeb48c;
const format_tag_t aCdeB48c2b = dnnl_aCdeB48c2b;
const format_tag_t aCdeB48c4b = dnnl_aCdeB48c4b;
const format_tag_t aCdeb64c = dnnl_aCdeb64c;
const format_tag_t aCdeB64c2b = dnnl_aCdeB64c2b;
const format_tag_t aCdeB64c4b = dnnl_aCdeB64c4b;
const format_tag_t Acb24a = dnnl_Acb24a;
const format_tag_t Acdb24a = dnnl_Acdb24a;
const format_tag_t Acdeb24a = dnnl_Acdeb24a;
const format_tag_t aBdc24b = dnnl_aBdc24b;
const format_tag_t aBdec24b = dnnl_aBdec24b;
const format_tag_t aBdefc24b = dnnl_aBdefc24b;
const format_tag_t AcB24a2b = dnnl_AcB24a2b;
const format_tag_t AcdB24a2b = dnnl_AcdB24a2b;
const format_tag_t AcdeB24a2b = dnnl_AcdeB24a2b;
const format_tag_t aBdC24b2c = dnnl_aBdC24b2c;
const format_tag_t aBdeC24b2c = dnnl_aBdeC24b2c;
const format_tag_t aBdefC24b2c = dnnl_aBdefC24b2c;
const format_tag_t AB8b32a = dnnl_AB8b32a;
const format_tag_t ABc8b32a = dnnl_ABc8b32a;
const format_tag_t AcB8b32a = dnnl_AcB8b32a;
const format_tag_t ABcd8b32a = dnnl_ABcd8b32a;
const format_tag_t AcdB8b32a = dnnl_AcdB8b32a;
const format_tag_t ABcde8b32a = dnnl_ABcde8b32a;
const format_tag_t AcdeB8b32a = dnnl_AcdeB8b32a;
const format_tag_t AB8b24a = dnnl_AB8b24a;
const format_tag_t ABc8b24a = dnnl_ABc8b24a;
const format_tag_t AcB8b24a = dnnl_AcB8b24a;
const format_tag_t ABcd8b24a = dnnl_ABcd8b24a;
const format_tag_t AcdB8b24a = dnnl_AcdB8b24a;
const format_tag_t ABcde8b24a = dnnl_ABcde8b24a;
const format_tag_t AcdeB8b24a = dnnl_AcdeB8b24a;
const format_tag_t AB8b16a = dnnl_AB8b16a;
const format_tag_t ABc8b16a = dnnl_ABc8b16a;
const format_tag_t AcB8b16a = dnnl_AcB8b16a;
const format_tag_t ABcd8b16a = dnnl_ABcd8b16a;
const format_tag_t AcdB8b16a = dnnl_AcdB8b16a;
const format_tag_t ABcde8b16a = dnnl_ABcde8b16a;
const format_tag_t AcdeB8b16a = dnnl_AcdeB8b16a;
const format_tag_t AB8b8a = dnnl_AB8b8a;
const format_tag_t AcB24a4b = dnnl_AcB24a4b;
const format_tag_t AcdB24a4b = dnnl_AcdB24a4b;
const format_tag_t AcdeB24a4b = dnnl_AcdeB24a4b;
const format_tag_t aBdC24b4c = dnnl_aBdC24b4c;
const format_tag_t aBdeC24b4c = dnnl_aBdeC24b4c;
const format_tag_t aBdefC24b4c = dnnl_aBdefC24b4c;
const format_tag_t AB8b8a2b = dnnl_AB8b8a2b;
const format_tag_t ABc8b8a2b = dnnl_ABc8b8a2b;
const format_tag_t AcB8b8a2b = dnnl_AcB8b8a2b;
const format_tag_t ABcd8b8a2b = dnnl_ABcd8b8a2b;
const format_tag_t AcdB8b8a2b = dnnl_AcdB8b8a2b;
const format_tag_t ABcde8b8a2b = dnnl_ABcde8b8a2b;
const format_tag_t AcdeB8b8a2b = dnnl_AcdeB8b8a2b;
const format_tag_t AB8b24a2b = dnnl_AB8b24a2b;
const format_tag_t ABc8b24a2b = dnnl_ABc8b24a2b;
const format_tag_t AcB8b24a2b = dnnl_AcB8b24a2b;
const format_tag_t ABcd8b24a2b = dnnl_ABcd8b24a2b;
const format_tag_t AcdB8b24a2b = dnnl_AcdB8b24a2b;
const format_tag_t ABcde8b24a2b = dnnl_ABcde8b24a2b;
const format_tag_t AcdeB8b24a2b = dnnl_AcdeB8b24a2b;
const format_tag_t BA2a24b = dnnl_BA2a24b;
const format_tag_t aCB2b24c = dnnl_aCB2b24c;
const format_tag_t BA2a8b = dnnl_BA2a8b;
const format_tag_t aCB2b8c = dnnl_aCB2b8c;
const format_tag_t BA8a24b = dnnl_BA8a24b;
const format_tag_t aCB8b24c = dnnl_aCB8b24c;
const format_tag_t BA8a16b = dnnl_BA8a16b;
const format_tag_t aCB8b16c = dnnl_aCB8b16c;
const format_tag_t BA8a8b = dnnl_BA8a8b;
const format_tag_t aCB8b8c = dnnl_aCB8b8c;

const format_tag_t last = dnnl_format_tag_last;

const format_tag_t x = dnnl_x;
const format_tag_t nc = dnnl_nc;
const format_tag_t cn = dnnl_cn;
const format_tag_t ncw = dnnl_ncw;
const format_tag_t nwc = dnnl_nwc;
const format_tag_t nchw = dnnl_nchw;
const format_tag_t nhwc = dnnl_nhwc;
const format_tag_t chwn = dnnl_chwn;
const format_tag_t ncdhw = dnnl_ncdhw;
const format_tag_t ndhwc = dnnl_ndhwc;
const format_tag_t oi = dnnl_oi;
const format_tag_t io = dnnl_io;
const format_tag_t oiw = dnnl_oiw;
const format_tag_t wio = dnnl_wio;
const format_tag_t woi = dnnl_woi;
const format_tag_t owi = dnnl_owi;
const format_tag_t iwo = dnnl_iwo;
const format_tag_t oihw = dnnl_oihw;
const format_tag_t hwio = dnnl_hwio;
const format_tag_t hwoi = dnnl_hwoi;
const format_tag_t ohwi = dnnl_ohwi;
const format_tag_t ihwo = dnnl_ihwo;
const format_tag_t iohw = dnnl_iohw;
const format_tag_t oidhw = dnnl_oidhw;
const format_tag_t dhwio = dnnl_dhwio;
const format_tag_t dhwoi = dnnl_dhwoi;
const format_tag_t odhwi = dnnl_odhwi;
const format_tag_t idhwo = dnnl_idhwo;

const format_tag_t iodhw = dnnl_iodhw;
const format_tag_t goiw = dnnl_goiw;
const format_tag_t goihw = dnnl_goihw;
const format_tag_t wigo = dnnl_wigo;
const format_tag_t hwigo = dnnl_hwigo;
const format_tag_t dhwigo = dnnl_dhwigo;
const format_tag_t giohw = dnnl_giohw;
const format_tag_t goidhw = dnnl_goidhw;
const format_tag_t giodhw = dnnl_giodhw;
const format_tag_t gowi = dnnl_gowi;
const format_tag_t gohwi = dnnl_gohwi;
const format_tag_t godhwi = dnnl_godhwi;
const format_tag_t tnc = dnnl_tnc;
const format_tag_t ntc = dnnl_ntc;
const format_tag_t ldnc = dnnl_ldnc;
const format_tag_t ldigo = dnnl_ldigo;
const format_tag_t ldgoi = dnnl_ldgoi;
const format_tag_t ldio = dnnl_ldio;
const format_tag_t ldoi = dnnl_ldoi;
const format_tag_t ldgo = dnnl_ldgo;
const format_tag_t nCdhw32c = dnnl_nCdhw32c;
const format_tag_t nCdhw16c = dnnl_nCdhw16c;
const format_tag_t nCdhw4c = dnnl_nCdhw4c;
const format_tag_t nCdhw8c = dnnl_nCdhw8c;
const format_tag_t nChw32c = dnnl_nChw32c;
const format_tag_t nChw16c = dnnl_nChw16c;
const format_tag_t nChw4c = dnnl_nChw4c;
const format_tag_t nChw8c = dnnl_nChw8c;
const format_tag_t nCw32c = dnnl_nCw32c;
const format_tag_t nCw16c = dnnl_nCw16c;
const format_tag_t nCw4c = dnnl_nCw4c;
const format_tag_t nCw8c = dnnl_nCw8c;
const format_tag_t NCw16n16c = dnnl_NCw16n16c;
const format_tag_t NChw16n16c = dnnl_NChw16n16c;
const format_tag_t NCdhw16n16c = dnnl_NCdhw16n16c;
const format_tag_t NCw32n16c = dnnl_NCw32n16c;
const format_tag_t NChw32n16c = dnnl_NChw32n16c;
const format_tag_t NCdhw32n16c = dnnl_NCdhw32n16c;
const format_tag_t NCw40n16c = dnnl_NCw40n16c;
const format_tag_t NChw40n16c = dnnl_NChw40n16c;
const format_tag_t NCdhw40n16c = dnnl_NCdhw40n16c;
const format_tag_t NCw32n32c = dnnl_NCw32n32c;
const format_tag_t NChw32n32c = dnnl_NChw32n32c;
const format_tag_t NCdhw32n32c = dnnl_NCdhw32n32c;
const format_tag_t NCw40n32c = dnnl_NCw40n32c;
const format_tag_t NChw40n32c = dnnl_NChw40n32c;
const format_tag_t NCdhw40n32c = dnnl_NCdhw40n32c;
const format_tag_t OI16i16o = dnnl_OI16i16o;
const format_tag_t OI16i32o = dnnl_OI16i32o;
const format_tag_t OI16i48o = dnnl_OI16i48o;
const format_tag_t OI16i64o = dnnl_OI16i64o;
const format_tag_t OI8i16o2i = dnnl_OI8i16o2i;
const format_tag_t OI8i32o2i = dnnl_OI8i32o2i;
const format_tag_t OI8i64o2i = dnnl_OI8i64o2i;
const format_tag_t OI4i8o4i = dnnl_OI4i8o4i;
const format_tag_t OI4i16o4i = dnnl_OI4i16o4i;
const format_tag_t OI4i24o4i = dnnl_OI4i24o4i;
const format_tag_t OI4i32o4i = dnnl_OI4i32o4i;
const format_tag_t OI4i64o4i = dnnl_OI4i64o4i;
const format_tag_t OI16i16o4i = dnnl_OI16i16o4i;
const format_tag_t OI16i32o4i = dnnl_OI16i32o4i;
const format_tag_t OI16i48o4i = dnnl_OI16i48o4i;
const format_tag_t OI16i64o4i = dnnl_OI16i64o4i;
const format_tag_t OI16i16o2i = dnnl_OI16i16o2i;
const format_tag_t OI16i32o2i = dnnl_OI16i32o2i;
const format_tag_t OI16i48o2i = dnnl_OI16i48o2i;
const format_tag_t OI16i64o2i = dnnl_OI16i64o2i;
const format_tag_t IOdhw16i16o = dnnl_IOdhw16i16o;
const format_tag_t IOhw16i16o = dnnl_IOhw16i16o;
const format_tag_t Ohwi32o = dnnl_Ohwi32o;
const format_tag_t gIOhw16i16o = dnnl_gIOhw16i16o;
const format_tag_t gOhwi32o = dnnl_gOhwi32o;
const format_tag_t Goidhw16g = dnnl_Goidhw16g;
const format_tag_t IOw8o8i = dnnl_IOw8o8i;
const format_tag_t IOw16o16i = dnnl_IOw16o16i;
const format_tag_t IOw16i16o = dnnl_IOw16i16o;
const format_tag_t gIOw16i16o = dnnl_gIOw16i16o;
const format_tag_t OIw16i16o = dnnl_OIw16i16o;
const format_tag_t OwI16i16o = dnnl_OwI16i16o;
const format_tag_t OIw16i32o = dnnl_OIw16i32o;
const format_tag_t OwI16i32o = dnnl_OwI16i32o;
const format_tag_t OIw16i48o = dnnl_OIw16i48o;
const format_tag_t OwI16i48o = dnnl_OwI16i48o;
const format_tag_t OIw16i64o = dnnl_OIw16i64o;
const format_tag_t OwI16i64o = dnnl_OwI16i64o;
const format_tag_t OIw16o16i = dnnl_OIw16o16i;
const format_tag_t Oiw16o = dnnl_Oiw16o;
const format_tag_t OIw4i8o4i = dnnl_OIw4i8o4i;
const format_tag_t OwI4i8o4i = dnnl_OwI4i8o4i;
const format_tag_t OIw4i16o4i = dnnl_OIw4i16o4i;
const format_tag_t OwI4i16o4i = dnnl_OwI4i16o4i;
const format_tag_t OIw4i24o4i = dnnl_OIw4i24o4i;
const format_tag_t OwI4i24o4i = dnnl_OwI4i24o4i;
const format_tag_t OIw4i32o4i = dnnl_OIw4i32o4i;
const format_tag_t OwI4i32o4i = dnnl_OwI4i32o4i;
const format_tag_t OIw4i64o4i = dnnl_OIw4i64o4i;
const format_tag_t OwI4i64o4i = dnnl_OwI4i64o4i;
const format_tag_t OIw2i8o4i = dnnl_OIw2i8o4i;
const format_tag_t OIw16i16o4i = dnnl_OIw16i16o4i;
const format_tag_t OIw16i32o4i = dnnl_OIw16i32o4i;
const format_tag_t OIw16i48o4i = dnnl_OIw16i48o4i;
const format_tag_t OIw16i64o4i = dnnl_OIw16i64o4i;
const format_tag_t OIw16i16o2i = dnnl_OIw16i16o2i;
const format_tag_t OIw16i32o2i = dnnl_OIw16i32o2i;
const format_tag_t OIw16i48o2i = dnnl_OIw16i48o2i;
const format_tag_t OIw16i64o2i = dnnl_OIw16i64o2i;
const format_tag_t OIw16o16i2o = dnnl_OIw16o16i2o;
const format_tag_t OIw4i4o = dnnl_OIw4i4o;
const format_tag_t OIw4o4i = dnnl_OIw4o4i;
const format_tag_t Oiw4o = dnnl_Oiw4o;
const format_tag_t OIw8i16o2i = dnnl_OIw8i16o2i;
const format_tag_t OwI8i16o2i = dnnl_OwI8i16o2i;
const format_tag_t OIw8i32o2i = dnnl_OIw8i32o2i;
const format_tag_t OwI8i32o2i = dnnl_OwI8i32o2i;
const format_tag_t OIw8i64o2i = dnnl_OIw8i64o2i;
const format_tag_t OwI8i64o2i = dnnl_OwI8i64o2i;
const format_tag_t OIw8i8o = dnnl_OIw8i8o;
const format_tag_t OwI8i8o = dnnl_OwI8i8o;
const format_tag_t OIw8o16i2o = dnnl_OIw8o16i2o;
const format_tag_t IOw8o16i2o = dnnl_IOw8o16i2o;
const format_tag_t OIw8o8i = dnnl_OIw8o8i;
const format_tag_t OIw8o4i = dnnl_OIw8o4i;
const format_tag_t Owi16o = dnnl_Owi16o;
const format_tag_t OwI16o2i = dnnl_OwI16o2i;
const format_tag_t OwI16o4i = dnnl_OwI16o4i;
const format_tag_t Owi4o = dnnl_Owi4o;
const format_tag_t Owi8o = dnnl_Owi8o;
const format_tag_t OwI8o2i = dnnl_OwI8o2i;
const format_tag_t OwI8o4i = dnnl_OwI8o4i;
const format_tag_t IOdhw8o8i = dnnl_IOdhw8o8i;
const format_tag_t IOdhw16o16i = dnnl_IOdhw16o16i;
const format_tag_t IOhw8o8i = dnnl_IOhw8o8i;
const format_tag_t IOhw16o16i = dnnl_IOhw16o16i;
const format_tag_t Ohwi16o = dnnl_Ohwi16o;
const format_tag_t OhwI16o2i = dnnl_OhwI16o2i;
const format_tag_t OhwI16o4i = dnnl_OhwI16o4i;
const format_tag_t Ohwi4o = dnnl_Ohwi4o;
const format_tag_t Ohwi8o = dnnl_Ohwi8o;
const format_tag_t OhwI8o2i = dnnl_OhwI8o2i;
const format_tag_t OhwI8o4i = dnnl_OhwI8o4i;
const format_tag_t OIhw16i16o = dnnl_OIhw16i16o;
const format_tag_t OhwI16i16o = dnnl_OhwI16i16o;
const format_tag_t OIhw16i32o = dnnl_OIhw16i32o;
const format_tag_t OhwI16i32o = dnnl_OhwI16i32o;
const format_tag_t OIhw16i48o = dnnl_OIhw16i48o;
const format_tag_t OhwI16i48o = dnnl_OhwI16i48o;
const format_tag_t OIhw16i64o = dnnl_OIhw16i64o;
const format_tag_t OhwI16i64o = dnnl_OhwI16i64o;
const format_tag_t OIhw16o16i = dnnl_OIhw16o16i;
const format_tag_t Oihw16o = dnnl_Oihw16o;
const format_tag_t OIhw4i8o4i = dnnl_OIhw4i8o4i;
const format_tag_t OhwI4i8o4i = dnnl_OhwI4i8o4i;
const format_tag_t OIhw4i16o4i = dnnl_OIhw4i16o4i;
const format_tag_t OhwI4i16o4i = dnnl_OhwI4i16o4i;
const format_tag_t OIhw4i24o4i = dnnl_OIhw4i24o4i;
const format_tag_t OhwI4i24o4i = dnnl_OhwI4i24o4i;
const format_tag_t OIhw4i32o4i = dnnl_OIhw4i32o4i;
const format_tag_t OhwI4i32o4i = dnnl_OhwI4i32o4i;
const format_tag_t OIhw4i64o4i = dnnl_OIhw4i64o4i;
const format_tag_t OhwI4i64o4i = dnnl_OhwI4i64o4i;
const format_tag_t OIhw16i16o4i = dnnl_OIhw16i16o4i;
const format_tag_t OIhw16i32o4i = dnnl_OIhw16i32o4i;
const format_tag_t OIhw16i48o4i = dnnl_OIhw16i48o4i;
const format_tag_t OIhw16i64o4i = dnnl_OIhw16i64o4i;
const format_tag_t OIhw16i16o2i = dnnl_OIhw16i16o2i;
const format_tag_t OIhw16i32o2i = dnnl_OIhw16i32o2i;
const format_tag_t OIhw16i48o2i = dnnl_OIhw16i48o2i;
const format_tag_t OIhw16i64o2i = dnnl_OIhw16i64o2i;
const format_tag_t OIhw16o16i2o = dnnl_OIhw16o16i2o;
const format_tag_t OIhw4i4o = dnnl_OIhw4i4o;
const format_tag_t OIhw4o4i = dnnl_OIhw4o4i;
const format_tag_t Oihw4o = dnnl_Oihw4o;
const format_tag_t OIhw8i16o2i = dnnl_OIhw8i16o2i;
const format_tag_t OhwI8i16o2i = dnnl_OhwI8i16o2i;
const format_tag_t OIhw8i32o2i = dnnl_OIhw8i32o2i;
const format_tag_t OhwI8i32o2i = dnnl_OhwI8i32o2i;
const format_tag_t OIhw8i64o2i = dnnl_OIhw8i64o2i;
const format_tag_t OhwI8i64o2i = dnnl_OhwI8i64o2i;
const format_tag_t OIhw2i8o4i = dnnl_OIhw2i8o4i;
const format_tag_t OIhw8i8o = dnnl_OIhw8i8o;
const format_tag_t OhwI8i8o = dnnl_OhwI8i8o;
const format_tag_t OIhw8o16i2o = dnnl_OIhw8o16i2o;
const format_tag_t IOhw8o16i2o = dnnl_IOhw8o16i2o;
const format_tag_t OIhw8o8i = dnnl_OIhw8o8i;
const format_tag_t OIhw8o4i = dnnl_OIhw8o4i;
const format_tag_t Owhi16o = dnnl_Owhi16o;
const format_tag_t Odwhi16o = dnnl_Odwhi16o;
const format_tag_t Odhwi16o = dnnl_Odhwi16o;
const format_tag_t OdhwI16o2i = dnnl_OdhwI16o2i;
const format_tag_t OdhwI16o4i = dnnl_OdhwI16o4i;
const format_tag_t Odhwi4o = dnnl_Odhwi4o;
const format_tag_t Odhwi8o = dnnl_Odhwi8o;
const format_tag_t OdhwI8o2i = dnnl_OdhwI8o2i;
const format_tag_t OdhwI8o4i = dnnl_OdhwI8o4i;
const format_tag_t OIdhw16i16o = dnnl_OIdhw16i16o;
const format_tag_t OdhwI16i16o = dnnl_OdhwI16i16o;
const format_tag_t OIdhw16i32o = dnnl_OIdhw16i32o;
const format_tag_t OdhwI16i32o = dnnl_OdhwI16i32o;
const format_tag_t OIdhw16i48o = dnnl_OIdhw16i48o;
const format_tag_t OdhwI16i48o = dnnl_OdhwI16i48o;
const format_tag_t OIdhw16i64o = dnnl_OIdhw16i64o;
const format_tag_t OdhwI16i64o = dnnl_OdhwI16i64o;
const format_tag_t OIdhw16o16i = dnnl_OIdhw16o16i;
const format_tag_t OIdhw16o16i2o = dnnl_OIdhw16o16i2o;
const format_tag_t Oidhw16o = dnnl_Oidhw16o;
const format_tag_t OIdhw4i4o = dnnl_OIdhw4i4o;
const format_tag_t OIdhw4o4i = dnnl_OIdhw4o4i;
const format_tag_t Oidhw4o = dnnl_Oidhw4o;
const format_tag_t OIdhw8i16o2i = dnnl_OIdhw8i16o2i;
const format_tag_t OdhwI8i16o2i = dnnl_OdhwI8i16o2i;
const format_tag_t OIdhw8i32o2i = dnnl_OIdhw8i32o2i;
const format_tag_t OdhwI8i32o2i = dnnl_OdhwI8i32o2i;
const format_tag_t OIdhw8i64o2i = dnnl_OIdhw8i64o2i;
const format_tag_t OdhwI8i64o2i = dnnl_OdhwI8i64o2i;
const format_tag_t OIdhw4i8o4i = dnnl_OIdhw4i8o4i;
const format_tag_t OdhwI4i8o4i = dnnl_OdhwI4i8o4i;
const format_tag_t OIdhw4i16o4i = dnnl_OIdhw4i16o4i;
const format_tag_t OdhwI4i16o4i = dnnl_OdhwI4i16o4i;
const format_tag_t OIdhw4i24o4i = dnnl_OIdhw4i24o4i;
const format_tag_t OdhwI4i24o4i = dnnl_OdhwI4i24o4i;
const format_tag_t OIdhw4i32o4i = dnnl_OIdhw4i32o4i;
const format_tag_t OdhwI4i32o4i = dnnl_OdhwI4i32o4i;
const format_tag_t OIdhw4i64o4i = dnnl_OIdhw4i64o4i;
const format_tag_t OdhwI4i64o4i = dnnl_OdhwI4i64o4i;
const format_tag_t OIdhw16i16o4i = dnnl_OIdhw16i16o4i;
const format_tag_t OIdhw16i32o4i = dnnl_OIdhw16i32o4i;
const format_tag_t OIdhw16i48o4i = dnnl_OIdhw16i48o4i;
const format_tag_t OIdhw16i64o4i = dnnl_OIdhw16i64o4i;
const format_tag_t OIdhw16i16o2i = dnnl_OIdhw16i16o2i;
const format_tag_t OIdhw16i32o2i = dnnl_OIdhw16i32o2i;
const format_tag_t OIdhw16i48o2i = dnnl_OIdhw16i48o2i;
const format_tag_t OIdhw16i64o2i = dnnl_OIdhw16i64o2i;
const format_tag_t OIdhw2i8o4i = dnnl_OIdhw2i8o4i;
const format_tag_t OIdhw8o16i2o = dnnl_OIdhw8o16i2o;
const format_tag_t IOdhw8o16i2o = dnnl_IOdhw8o16i2o;
const format_tag_t OIdhw8i8o = dnnl_OIdhw8i8o;
const format_tag_t OdhwI8i8o = dnnl_OdhwI8i8o;
const format_tag_t OIdhw8o8i = dnnl_OIdhw8o8i;
const format_tag_t OIdhw8o4i = dnnl_OIdhw8o4i;
const format_tag_t gIOw8o8i = dnnl_gIOw8o8i;
const format_tag_t gIOw16o16i = dnnl_gIOw16o16i;
const format_tag_t Goiw16g = dnnl_Goiw16g;
const format_tag_t Goiw8g = dnnl_Goiw8g;
const format_tag_t Goiw4g = dnnl_Goiw4g;
const format_tag_t gOIw16i16o = dnnl_gOIw16i16o;
const format_tag_t gOIw16o16i = dnnl_gOIw16o16i;
const format_tag_t gOiw16o = dnnl_gOiw16o;
const format_tag_t gOIw4i16o4i = dnnl_gOIw4i16o4i;
const format_tag_t gOIw2i8o4i = dnnl_gOIw2i8o4i;
const format_tag_t gOIw16i16o4i = dnnl_gOIw16i16o4i;
const format_tag_t gOIw16i16o2i = dnnl_gOIw16i16o2i;
const format_tag_t gOIw16o16i2o = dnnl_gOIw16o16i2o;
const format_tag_t gOIw4i4o = dnnl_gOIw4i4o;
const format_tag_t gOIw4o4i = dnnl_gOIw4o4i;
const format_tag_t gOiw4o = dnnl_gOiw4o;
const format_tag_t gOIw8i16o2i = dnnl_gOIw8i16o2i;
const format_tag_t gOIw8i8o = dnnl_gOIw8i8o;
const format_tag_t gOIw8o16i2o = dnnl_gOIw8o16i2o;
const format_tag_t gIOw8o16i2o = dnnl_gIOw8o16i2o;
const format_tag_t gOIw8o8i = dnnl_gOIw8o8i;
const format_tag_t gOIw8o4i = dnnl_gOIw8o4i;
const format_tag_t gOwi16o = dnnl_gOwi16o;
const format_tag_t gOwI16o2i = dnnl_gOwI16o2i;
const format_tag_t gOwI16o4i = dnnl_gOwI16o4i;
const format_tag_t gOwi4o = dnnl_gOwi4o;
const format_tag_t gOwi8o = dnnl_gOwi8o;
const format_tag_t gOwI8o2i = dnnl_gOwI8o2i;
const format_tag_t gOwI8o4i = dnnl_gOwI8o4i;
const format_tag_t gIOdhw8o8i = dnnl_gIOdhw8o8i;
const format_tag_t gIOdhw16o16i = dnnl_gIOdhw16o16i;
const format_tag_t gIOhw8o8i = dnnl_gIOhw8o8i;
const format_tag_t gIOhw16o16i = dnnl_gIOhw16o16i;
const format_tag_t gOhwi16o = dnnl_gOhwi16o;
const format_tag_t gOhwI16o2i = dnnl_gOhwI16o2i;
const format_tag_t gOhwI16o4i = dnnl_gOhwI16o4i;
const format_tag_t gOhwi4o = dnnl_gOhwi4o;
const format_tag_t gOhwi8o = dnnl_gOhwi8o;
const format_tag_t gOhwI8o2i = dnnl_gOhwI8o2i;
const format_tag_t gOhwI8o4i = dnnl_gOhwI8o4i;
const format_tag_t Goihw16g = dnnl_Goihw16g;
const format_tag_t gOIhw16i16o = dnnl_gOIhw16i16o;
const format_tag_t gOIhw16o16i = dnnl_gOIhw16o16i;
const format_tag_t gOihw16o = dnnl_gOihw16o;
const format_tag_t gOIhw2i8o4i = dnnl_gOIhw2i8o4i;
const format_tag_t gOIhw4i16o4i = dnnl_gOIhw4i16o4i;
const format_tag_t gOIhw16i16o4i = dnnl_gOIhw16i16o4i;
const format_tag_t gOIhw16i16o2i = dnnl_gOIhw16i16o2i;
const format_tag_t gOIhw16o16i2o = dnnl_gOIhw16o16i2o;
const format_tag_t gOIhw4i4o = dnnl_gOIhw4i4o;
const format_tag_t gOIhw4o4i = dnnl_gOIhw4o4i;
const format_tag_t gOihw4o = dnnl_gOihw4o;
const format_tag_t Goihw8g = dnnl_Goihw8g;
const format_tag_t Goihw4g = dnnl_Goihw4g;
const format_tag_t gOIhw8i16o2i = dnnl_gOIhw8i16o2i;
const format_tag_t gOIhw8i8o = dnnl_gOIhw8i8o;
const format_tag_t gOIhw8o16i2o = dnnl_gOIhw8o16i2o;
const format_tag_t OIw4o8i8o4i = dnnl_OIw4o8i8o4i;
const format_tag_t gIOhw8o16i2o = dnnl_gIOhw8o16i2o;
const format_tag_t OIhw4o8i8o4i = dnnl_OIhw4o8i8o4i;
const format_tag_t OIdhw4o8i8o4i = dnnl_OIdhw4o8i8o4i;
const format_tag_t IOw4i8o8i4o = dnnl_IOw4i8o8i4o;
const format_tag_t IOhw4i8o8i4o = dnnl_IOhw4i8o8i4o;
const format_tag_t IOdhw4i8o8i4o = dnnl_IOdhw4i8o8i4o;
const format_tag_t gIOw4i8o8i4o = dnnl_gIOw4i8o8i4o;
const format_tag_t gIOhw4i8o8i4o = dnnl_gIOhw4i8o8i4o;
const format_tag_t gIOdhw4i8o8i4o = dnnl_gIOdhw4i8o8i4o;
const format_tag_t OIhw2o8i8o2i = dnnl_OIhw2o8i8o2i;
const format_tag_t gOIw4o8i8o4i = dnnl_gOIw4o8i8o4i;
const format_tag_t gOIhw4o8i8o4i = dnnl_gOIhw4o8i8o4i;
const format_tag_t gOIdhw4o8i8o4i = dnnl_gOIdhw4o8i8o4i;
const format_tag_t gOIhw2o8i8o2i = dnnl_gOIhw2o8i8o2i;
const format_tag_t gOIhw8o8i = dnnl_gOIhw8o8i;
const format_tag_t gOIhw8o4i = dnnl_gOIhw8o4i;
const format_tag_t gOwhi16o = dnnl_gOwhi16o;
const format_tag_t gOdwhi16o = dnnl_gOdwhi16o;
const format_tag_t gIOdhw16i16o = dnnl_gIOdhw16i16o;
const format_tag_t gOdhwi16o = dnnl_gOdhwi16o;
const format_tag_t gOdhwI16o2i = dnnl_gOdhwI16o2i;
const format_tag_t gOdhwI16o4i = dnnl_gOdhwI16o4i;
const format_tag_t gOdhwi4o = dnnl_gOdhwi4o;
const format_tag_t gOdhwi8o = dnnl_gOdhwi8o;
const format_tag_t gOdhwI8o2i = dnnl_gOdhwI8o2i;
const format_tag_t gOdhwI8o4i = dnnl_gOdhwI8o4i;
const format_tag_t gOIdhw16i16o = dnnl_gOIdhw16i16o;
const format_tag_t gOIdhw16o16i = dnnl_gOIdhw16o16i;
const format_tag_t gOIdhw16o16i2o = dnnl_gOIdhw16o16i2o;
const format_tag_t gOidhw16o = dnnl_gOidhw16o;
const format_tag_t gOIdhw4i4o = dnnl_gOIdhw4i4o;
const format_tag_t gOIdhw4o4i = dnnl_gOIdhw4o4i;
const format_tag_t gOidhw4o = dnnl_gOidhw4o;
const format_tag_t gOIdhw8i16o2i = dnnl_gOIdhw8i16o2i;
const format_tag_t gOIdhw4i16o4i = dnnl_gOIdhw4i16o4i;
const format_tag_t gOIdhw16i16o4i = dnnl_gOIdhw16i16o4i;
const format_tag_t gOIdhw2i8o4i = dnnl_gOIdhw2i8o4i;
const format_tag_t gOIdhw16i16o2i = dnnl_gOIdhw16i16o2i;
const format_tag_t gOIdhw8o16i2o = dnnl_gOIdhw8o16i2o;
const format_tag_t gIOdhw8o16i2o = dnnl_gIOdhw8o16i2o;
const format_tag_t gOIdhw8i8o = dnnl_gOIdhw8i8o;
const format_tag_t gOIdhw8o8i = dnnl_gOIdhw8o8i;
const format_tag_t gOIdhw8o4i = dnnl_gOIdhw8o4i;
const format_tag_t Goiw32g = dnnl_Goiw32g;
const format_tag_t Goihw32g = dnnl_Goihw32g;
const format_tag_t Goidhw32g = dnnl_Goidhw32g;
const format_tag_t OIdhw4o8i8o2i = dnnl_OIdhw4o8i8o2i;
const format_tag_t OIhw4o8i8o2i = dnnl_OIhw4o8i8o2i;
const format_tag_t OIw4o8i8o2i = dnnl_OIw4o8i8o2i;
const format_tag_t gOIdhw4o8i8o2i = dnnl_gOIdhw4o8i8o2i;
const format_tag_t gOIhw4o8i8o2i = dnnl_gOIhw4o8i8o2i;
const format_tag_t gOIw4o8i8o2i = dnnl_gOIw4o8i8o2i;
const format_tag_t IOdhw4i8o8i2o = dnnl_IOdhw4i8o8i2o;
const format_tag_t IOhw4i8o8i2o = dnnl_IOhw4i8o8i2o;
const format_tag_t IOw4i8o8i2o = dnnl_IOw4i8o8i2o;
const format_tag_t gIOdhw4i8o8i2o = dnnl_gIOdhw4i8o8i2o;
const format_tag_t gIOhw4i8o8i2o = dnnl_gIOhw4i8o8i2o;
const format_tag_t gIOw4i8o8i2o = dnnl_gIOw4i8o8i2o;
const format_tag_t gOIw2i4o2i = dnnl_gOIw2i4o2i;
const format_tag_t gOIhw2i4o2i = dnnl_gOIhw2i4o2i;
const format_tag_t gOIdhw2i4o2i = dnnl_gOIdhw2i4o2i;
const format_tag_t gOIw2o4i2o = dnnl_gOIw2o4i2o;
const format_tag_t gOIhw2o4i2o = dnnl_gOIhw2o4i2o;
const format_tag_t gOIdhw2o4i2o = dnnl_gOIdhw2o4i2o;
const format_tag_t gOIw4i8o2i = dnnl_gOIw4i8o2i;
const format_tag_t gOIhw4i8o2i = dnnl_gOIhw4i8o2i;
const format_tag_t gOIdhw4i8o2i = dnnl_gOIdhw4i8o2i;
const format_tag_t gOIw4o8i2o = dnnl_gOIw4o8i2o;
const format_tag_t gOIhw4o8i2o = dnnl_gOIhw4o8i2o;
const format_tag_t gOIdhw4o8i2o = dnnl_gOIdhw4o8i2o;
const format_tag_t ldOi16o = dnnl_ldOi16o;
const format_tag_t ldOi32o = dnnl_ldOi32o;
const format_tag_t ldOI16o4i = dnnl_ldOI16o4i;
const format_tag_t ldOI32o4i = dnnl_ldOI32o4i;
const format_tag_t ldIo32i = dnnl_ldIo32i;
const format_tag_t ldgOi16o = dnnl_ldgOi16o;
const format_tag_t ldgOi32o = dnnl_ldgOi32o;
const format_tag_t ldgOI16o4i = dnnl_ldgOI16o4i;
const format_tag_t ldgOI32o2i = dnnl_ldgOI32o2i;
const format_tag_t ldgOI32o4i = dnnl_ldgOI32o4i;
const format_tag_t ldgOI64o2i = dnnl_ldgOI64o2i;
const format_tag_t ldgOI64o4i = dnnl_ldgOI64o4i;
const format_tag_t ldgIo16i = dnnl_ldgIo16i;
const format_tag_t ldgIo32i = dnnl_ldgIo32i;
const format_tag_t ldgIO32i2o = dnnl_ldgIO32i2o;

const format_tag_t wIo2i = dnnl_wIo2i;
const format_tag_t wIo4i = dnnl_wIo4i;
const format_tag_t gwio = dnnl_gwio;
const format_tag_t gwIo2i = dnnl_gwIo2i;
const format_tag_t gwIo4i = dnnl_gwIo4i;
const format_tag_t hwIo2i = dnnl_hwIo2i;
const format_tag_t hwIo4i = dnnl_hwIo4i;
const format_tag_t ghwio = dnnl_ghwio;
const format_tag_t ghwIo2i = dnnl_ghwIo2i;
const format_tag_t ghwIo4i = dnnl_ghwIo4i;
const format_tag_t dhwIo2i = dnnl_dhwIo2i;
const format_tag_t dhwIo4i = dnnl_dhwIo4i;
const format_tag_t gdhwio = dnnl_gdhwio;
const format_tag_t gdhwIo2i = dnnl_gdhwIo2i;
const format_tag_t gdhwIo4i = dnnl_gdhwIo4i;
const format_tag_t Owi32o = dnnl_Owi32o;
const format_tag_t OwI32o2i = dnnl_OwI32o2i;
const format_tag_t OwI32o4i = dnnl_OwI32o4i;
const format_tag_t Owi48o = dnnl_Owi48o;
const format_tag_t OwI48o2i = dnnl_OwI48o2i;
const format_tag_t OwI48o4i = dnnl_OwI48o4i;
const format_tag_t Owi64o = dnnl_Owi64o;
const format_tag_t OwI64o2i = dnnl_OwI64o2i;
const format_tag_t OwI64o4i = dnnl_OwI64o4i;
const format_tag_t OhwI32o2i = dnnl_OhwI32o2i;
const format_tag_t OhwI32o4i = dnnl_OhwI32o4i;
const format_tag_t Ohwi48o = dnnl_Ohwi48o;
const format_tag_t OhwI48o2i = dnnl_OhwI48o2i;
const format_tag_t OhwI48o4i = dnnl_OhwI48o4i;
const format_tag_t Ohwi64o = dnnl_Ohwi64o;
const format_tag_t OhwI64o2i = dnnl_OhwI64o2i;
const format_tag_t OhwI64o4i = dnnl_OhwI64o4i;
const format_tag_t Odhwi32o = dnnl_Odhwi32o;
const format_tag_t OdhwI32o2i = dnnl_OdhwI32o2i;
const format_tag_t OdhwI32o4i = dnnl_OdhwI32o4i;
const format_tag_t Odhwi48o = dnnl_Odhwi48o;
const format_tag_t OdhwI48o2i = dnnl_OdhwI48o2i;
const format_tag_t OdhwI48o4i = dnnl_OdhwI48o4i;
const format_tag_t Odhwi64o = dnnl_Odhwi64o;
const format_tag_t OdhwI64o2i = dnnl_OdhwI64o2i;
const format_tag_t OdhwI64o4i = dnnl_OdhwI64o4i;
const format_tag_t gOwi32o = dnnl_gOwi32o;
const format_tag_t gOwI32o2i = dnnl_gOwI32o2i;
const format_tag_t gOwI32o4i = dnnl_gOwI32o4i;
const format_tag_t gOwi48o = dnnl_gOwi48o;
const format_tag_t gOwI48o2i = dnnl_gOwI48o2i;
const format_tag_t gOwI48o4i = dnnl_gOwI48o4i;
const format_tag_t gOwi64o = dnnl_gOwi64o;
const format_tag_t gOwI64o2i = dnnl_gOwI64o2i;
const format_tag_t gOwI64o4i = dnnl_gOwI64o4i;
const format_tag_t gOhwI32o2i = dnnl_gOhwI32o2i;
const format_tag_t gOhwI32o4i = dnnl_gOhwI32o4i;
const format_tag_t gOhwi48o = dnnl_gOhwi48o;
const format_tag_t gOhwI48o2i = dnnl_gOhwI48o2i;
const format_tag_t gOhwI48o4i = dnnl_gOhwI48o4i;
const format_tag_t gOhwi64o = dnnl_gOhwi64o;
const format_tag_t gOhwI64o2i = dnnl_gOhwI64o2i;
const format_tag_t gOhwI64o4i = dnnl_gOhwI64o4i;
const format_tag_t gOdhwi32o = dnnl_gOdhwi32o;
const format_tag_t gOdhwI32o2i = dnnl_gOdhwI32o2i;
const format_tag_t gOdhwI32o4i = dnnl_gOdhwI32o4i;
const format_tag_t gOdhwi48o = dnnl_gOdhwi48o;
const format_tag_t gOdhwI48o2i = dnnl_gOdhwI48o2i;
const format_tag_t gOdhwI48o4i = dnnl_gOdhwI48o4i;
const format_tag_t gOdhwi64o = dnnl_gOdhwi64o;
const format_tag_t gOdhwI64o2i = dnnl_gOdhwI64o2i;
const format_tag_t gOdhwI64o4i = dnnl_gOdhwI64o4i;
const format_tag_t ABc2b8a16b4a = dnnl_ABc2b8a16b4a;
const format_tag_t ABcd2b8a16b4a = dnnl_ABcd2b8a16b4a;
const format_tag_t ABcde2b8a16b4a = dnnl_ABcde2b8a16b4a;
const format_tag_t ABc2a8b16a4b = dnnl_ABc2a8b16a4b;
const format_tag_t ABcd2a8b16a4b = dnnl_ABcd2a8b16a4b;
const format_tag_t ABcde2a8b16a4b = dnnl_ABcde2a8b16a4b;
const format_tag_t ABc2a8b16a2b = dnnl_ABc2a8b16a2b;
const format_tag_t ABcd2a8b16a2b = dnnl_ABcd2a8b16a2b;
const format_tag_t ABcde2a8b16a2b = dnnl_ABcde2a8b16a2b;
const format_tag_t aBCd2b8c16b2c = dnnl_aBCd2b8c16b2c;
const format_tag_t aBCde2b8c16b2c = dnnl_aBCde2b8c16b2c;
const format_tag_t aBCdef2b8c16b2c = dnnl_aBCdef2b8c16b2c;
const format_tag_t aBCd2b8c16b4c = dnnl_aBCd2b8c16b4c;
const format_tag_t aBCde2b8c16b4c = dnnl_aBCde2b8c16b4c;
const format_tag_t BAc2b8a16b2a = dnnl_BAc2b8a16b2a;
const format_tag_t aBCde2c8b16c2b = dnnl_aBCde2c8b16c2b;
const format_tag_t aBCdef2c8b16c2b = dnnl_aBCdef2c8b16c2b;
const format_tag_t BAcd2b8a16b2a = dnnl_BAcd2b8a16b2a;
const format_tag_t BAcde2b8a16b2a = dnnl_BAcde2b8a16b2a;
const format_tag_t aCBd2c8b16c2b = dnnl_aCBd2c8b16c2b;
const format_tag_t aCBde2c8b16c2b = dnnl_aCBde2c8b16c2b;
const format_tag_t aCBdef2c8b16c2b = dnnl_aCBdef2c8b16c2b;
const format_tag_t BAc2b8a16b4a = dnnl_BAc2b8a16b4a;
const format_tag_t BAcd2b8a16b4a = dnnl_BAcd2b8a16b4a;
const format_tag_t BAcde2b8a16b4a = dnnl_BAcde2b8a16b4a;
const format_tag_t ABc2b32a8b = dnnl_ABc2b32a8b;
const format_tag_t ABcd2b32a8b = dnnl_ABcd2b32a8b;
const format_tag_t ABcde2b32a8b = dnnl_ABcde2b32a8b;
const format_tag_t aBC2b8c16b2c = dnnl_aBC2b8c16b2c;
const format_tag_t ABc16a4b = dnnl_ABc16a4b;
const format_tag_t ABcd16a4b = dnnl_ABcd16a4b;
const format_tag_t ABcde16a4b = dnnl_ABcde16a4b;
const format_tag_t NCw2c32n8c = dnnl_NCw2c32n8c;
const format_tag_t NChw2c32n8c = dnnl_NChw2c32n8c;
const format_tag_t NCdhw2c32n8c = dnnl_NCdhw2c32n8c;
const format_tag_t OIw2i8o16i4o = dnnl_OIw2i8o16i4o;
const format_tag_t OIhw2i8o16i4o = dnnl_OIhw2i8o16i4o;
const format_tag_t OIdhw2i8o16i4o = dnnl_OIdhw2i8o16i4o;
const format_tag_t OIw2o8i16o4i = dnnl_OIw2o8i16o4i;
const format_tag_t OIhw2o8i16o4i = dnnl_OIhw2o8i16o4i;
const format_tag_t OIdhw2o8i16o4i = dnnl_OIdhw2o8i16o4i;
const format_tag_t OIw2o8i16o2i = dnnl_OIw2o8i16o2i;
const format_tag_t OIhw2o8i16o2i = dnnl_OIhw2o8i16o2i;
const format_tag_t OIdhw2o8i16o2i = dnnl_OIdhw2o8i16o2i;
const format_tag_t IOw2i8o16i4o = dnnl_IOw2i8o16i4o;
const format_tag_t IOhw2i8o16i4o = dnnl_IOhw2i8o16i4o;
const format_tag_t IOdhw2i8o16i4o = dnnl_IOdhw2i8o16i4o;
const format_tag_t IOw2i8o16i2o = dnnl_IOw2i8o16i2o;
const format_tag_t IOhw2i8o16i2o = dnnl_IOhw2i8o16i2o;
const format_tag_t IOdhw2i8o16i2o = dnnl_IOdhw2i8o16i2o;
const format_tag_t gOIw2o8i16o2i = dnnl_gOIw2o8i16o2i;
const format_tag_t gOIhw2o8i16o2i = dnnl_gOIhw2o8i16o2i;
const format_tag_t gOIdhw2o8i16o2i = dnnl_gOIdhw2o8i16o2i;
const format_tag_t gOIw2o8i16o4i = dnnl_gOIw2o8i16o4i;
const format_tag_t gOIhw2o8i16o4i = dnnl_gOIhw2o8i16o4i;
const format_tag_t gIOw2i8o16i2o = dnnl_gIOw2i8o16i2o;
const format_tag_t gIOhw2i8o16i2o = dnnl_gIOhw2i8o16i2o;
const format_tag_t gIOdhw2i8o16i2o = dnnl_gIOdhw2i8o16i2o;
const format_tag_t OwI16i16o2i = dnnl_OwI16i16o2i;
const format_tag_t OwI16i16o4i = dnnl_OwI16i16o4i;
const format_tag_t OhwI16i16o2i = dnnl_OhwI16i16o2i;
const format_tag_t OhwI16i16o4i = dnnl_OhwI16i16o4i;
const format_tag_t OdhwI16i16o2i = dnnl_OdhwI16i16o2i;
const format_tag_t OdhwI16i16o4i = dnnl_OdhwI16i16o4i;
const format_tag_t gOwI16i16o2i = dnnl_gOwI16i16o2i;
const format_tag_t gOwI16i16o4i = dnnl_gOwI16i16o4i;
const format_tag_t gOhwI16i16o2i = dnnl_gOhwI16i16o2i;
const format_tag_t gOhwI16i16o4i = dnnl_gOhwI16i16o4i;
const format_tag_t gOdhwI16i16o2i = dnnl_gOdhwI16i16o2i;
const format_tag_t gOdhwI16i16o4i = dnnl_gOdhwI16i16o4i;
const format_tag_t OwI16i32o2i = dnnl_OwI16i32o2i;
const format_tag_t OwI16i32o4i = dnnl_OwI16i32o4i;
const format_tag_t OwI16i48o2i = dnnl_OwI16i48o2i;
const format_tag_t OwI16i48o4i = dnnl_OwI16i48o4i;
const format_tag_t OwI16i64o2i = dnnl_OwI16i64o2i;
const format_tag_t OwI16i64o4i = dnnl_OwI16i64o4i;
const format_tag_t OhwI16i32o2i = dnnl_OhwI16i32o2i;
const format_tag_t OhwI16i32o4i = dnnl_OhwI16i32o4i;
const format_tag_t OhwI16i48o2i = dnnl_OhwI16i48o2i;
const format_tag_t OhwI16i48o4i = dnnl_OhwI16i48o4i;
const format_tag_t OhwI16i64o2i = dnnl_OhwI16i64o2i;
const format_tag_t OhwI16i64o4i = dnnl_OhwI16i64o4i;
const format_tag_t OdhwI16i32o2i = dnnl_OdhwI16i32o2i;
const format_tag_t OdhwI16i32o4i = dnnl_OdhwI16i32o4i;
const format_tag_t OdhwI16i48o2i = dnnl_OdhwI16i48o2i;
const format_tag_t OdhwI16i48o4i = dnnl_OdhwI16i48o4i;
const format_tag_t OdhwI16i64o2i = dnnl_OdhwI16i64o2i;
const format_tag_t OdhwI16i64o4i = dnnl_OdhwI16i64o4i;
const format_tag_t IdhwO16o32i2o = dnnl_IdhwO16o32i2o;
const format_tag_t IdhwO16o32i4o = dnnl_IdhwO16o32i4o;
const format_tag_t IdhwO16o48i2o = dnnl_IdhwO16o48i2o;
const format_tag_t IdhwO16o48i4o = dnnl_IdhwO16o48i4o;
const format_tag_t IdhwO16o64i2o = dnnl_IdhwO16o64i2o;
const format_tag_t IdhwO16o64i4o = dnnl_IdhwO16o64i4o;
const format_tag_t gOwI16i32o2i = dnnl_gOwI16i32o2i;
const format_tag_t gOwI16i32o4i = dnnl_gOwI16i32o4i;
const format_tag_t gOwI16i48o2i = dnnl_gOwI16i48o2i;
const format_tag_t gOwI16i48o4i = dnnl_gOwI16i48o4i;
const format_tag_t gOwI16i64o2i = dnnl_gOwI16i64o2i;
const format_tag_t gOwI16i64o4i = dnnl_gOwI16i64o4i;
const format_tag_t gOhwI16i32o2i = dnnl_gOhwI16i32o2i;
const format_tag_t gOhwI16i32o4i = dnnl_gOhwI16i32o4i;
const format_tag_t gOhwI16i48o2i = dnnl_gOhwI16i48o2i;
const format_tag_t gOhwI16i48o4i = dnnl_gOhwI16i48o4i;
const format_tag_t gOhwI16i64o2i = dnnl_gOhwI16i64o2i;
const format_tag_t gOhwI16i64o4i = dnnl_gOhwI16i64o4i;
const format_tag_t gOdhwI16i32o2i = dnnl_gOdhwI16i32o2i;
const format_tag_t gOdhwI16i32o4i = dnnl_gOdhwI16i32o4i;
const format_tag_t gOdhwI16i48o2i = dnnl_gOdhwI16i48o2i;
const format_tag_t gOdhwI16i48o4i = dnnl_gOdhwI16i48o4i;
const format_tag_t gOdhwI16i64o2i = dnnl_gOdhwI16i64o2i;
const format_tag_t gOdhwI16i64o4i = dnnl_gOdhwI16i64o4i;
const format_tag_t gIdhwO16o32i2o = dnnl_gIdhwO16o32i2o;
const format_tag_t gIdhwO16o32i4o = dnnl_gIdhwO16o32i4o;
const format_tag_t gIdhwO16o48i2o = dnnl_gIdhwO16o48i2o;
const format_tag_t gIdhwO16o48i4o = dnnl_gIdhwO16o48i4o;
const format_tag_t gIdhwO16o64i2o = dnnl_gIdhwO16o64i2o;
const format_tag_t gIdhwO16o64i4o = dnnl_gIdhwO16o64i4o;

const format_tag_t Idhwo32i = dnnl_Idhwo32i;
const format_tag_t IdhwO32i2o = dnnl_IdhwO32i2o;
const format_tag_t IdhwO32i4o = dnnl_IdhwO32i4o;
const format_tag_t Idhwo48i = dnnl_Idhwo48i;
const format_tag_t IdhwO48i2o = dnnl_IdhwO48i2o;
const format_tag_t IdhwO48i4o = dnnl_IdhwO48i4o;
const format_tag_t Idhwo64i = dnnl_Idhwo64i;
const format_tag_t IdhwO64i2o = dnnl_IdhwO64i2o;
const format_tag_t IdhwO64i4o = dnnl_IdhwO64i4o;

const format_tag_t gIdhwo32i = dnnl_gIdhwo32i;
const format_tag_t gIdhwO32i2o = dnnl_gIdhwO32i2o;
const format_tag_t gIdhwO32i4o = dnnl_gIdhwO32i4o;
const format_tag_t gIdhwo48i = dnnl_gIdhwo48i;
const format_tag_t gIdhwO48i2o = dnnl_gIdhwO48i2o;
const format_tag_t gIdhwO48i4o = dnnl_gIdhwO48i4o;
const format_tag_t gIdhwo64i = dnnl_gIdhwo64i;
const format_tag_t gIdhwO64i2o = dnnl_gIdhwO64i2o;
const format_tag_t gIdhwO64i4o = dnnl_gIdhwO64i4o;

const format_tag_t Iwo32i = dnnl_Iwo32i;
const format_tag_t IwO32i2o = dnnl_IwO32i2o;
const format_tag_t IwO32i4o = dnnl_IwO32i4o;
const format_tag_t Iwo48i = dnnl_Iwo48i;
const format_tag_t IwO48i2o = dnnl_IwO48i2o;
const format_tag_t IwO48i4o = dnnl_IwO48i4o;
const format_tag_t Iwo64i = dnnl_Iwo64i;
const format_tag_t IwO64i2o = dnnl_IwO64i2o;
const format_tag_t IwO64i4o = dnnl_IwO64i4o;

const format_tag_t gIwo32i = dnnl_gIwo32i;
const format_tag_t gIwO32i2o = dnnl_gIwO32i2o;
const format_tag_t gIwO32i4o = dnnl_gIwO32i4o;
const format_tag_t gIwo48i = dnnl_gIwo48i;
const format_tag_t gIwO48i2o = dnnl_gIwO48i2o;
const format_tag_t gIwO48i4o = dnnl_gIwO48i4o;
const format_tag_t gIwo64i = dnnl_gIwo64i;
const format_tag_t gIwO64i2o = dnnl_gIwO64i2o;
const format_tag_t gIwO64i4o = dnnl_gIwO64i4o;

const format_tag_t IwO16o16i2o = dnnl_IwO16o16i2o;
const format_tag_t IwO16o16i4o = dnnl_IwO16o16i4o;
const format_tag_t IhwO16o16i2o = dnnl_IhwO16o16i2o;
const format_tag_t IhwO16o16i4o = dnnl_IhwO16o16i4o;
const format_tag_t IdhwO16o16i2o = dnnl_IdhwO16o16i2o;
const format_tag_t IdhwO16o16i4o = dnnl_IdhwO16o16i4o;

const format_tag_t gIwO16o16i2o = dnnl_gIwO16o16i2o;
const format_tag_t gIwO16o16i4o = dnnl_gIwO16o16i4o;
const format_tag_t gIhwO16o16i2o = dnnl_gIhwO16o16i2o;
const format_tag_t gIhwO16o16i4o = dnnl_gIhwO16o16i4o;
const format_tag_t gIdhwO16o16i2o = dnnl_gIdhwO16o16i2o;
const format_tag_t gIdhwO16o16i4o = dnnl_gIdhwO16o16i4o;

const format_tag_t IwO16o32i2o = dnnl_IwO16o32i2o;
const format_tag_t IwO16o32i4o = dnnl_IwO16o32i4o;
const format_tag_t IwO16o48i2o = dnnl_IwO16o48i2o;
const format_tag_t IwO16o48i4o = dnnl_IwO16o48i4o;
const format_tag_t IwO16o64i2o = dnnl_IwO16o64i2o;
const format_tag_t IwO16o64i4o = dnnl_IwO16o64i4o;

const format_tag_t gIwO16o32i2o = dnnl_gIwO16o32i2o;
const format_tag_t gIwO16o32i4o = dnnl_gIwO16o32i4o;
const format_tag_t gIwO16o48i2o = dnnl_gIwO16o48i2o;
const format_tag_t gIwO16o48i4o = dnnl_gIwO16o48i4o;
const format_tag_t gIwO16o64i2o = dnnl_gIwO16o64i2o;
const format_tag_t gIwO16o64i4o = dnnl_gIwO16o64i4o;

const format_tag_t IhwO16o32i2o = dnnl_IhwO16o32i2o;
const format_tag_t IhwO16o32i4o = dnnl_IhwO16o32i4o;
const format_tag_t IhwO16o48i2o = dnnl_IhwO16o48i2o;
const format_tag_t IhwO16o48i4o = dnnl_IhwO16o48i4o;
const format_tag_t IhwO16o64i2o = dnnl_IhwO16o64i2o;
const format_tag_t IhwO16o64i4o = dnnl_IhwO16o64i4o;

const format_tag_t gIhwO16o32i2o = dnnl_gIhwO16o32i2o;
const format_tag_t gIhwO16o32i4o = dnnl_gIhwO16o32i4o;
const format_tag_t gIhwO16o48i2o = dnnl_gIhwO16o48i2o;
const format_tag_t gIhwO16o48i4o = dnnl_gIhwO16o48i4o;
const format_tag_t gIhwO16o64i2o = dnnl_gIhwO16o64i2o;
const format_tag_t gIhwO16o64i4o = dnnl_gIhwO16o64i4o;

const format_tag_t Ihwo32i = dnnl_Ihwo32i;
const format_tag_t IhwO32i2o = dnnl_IhwO32i2o;
const format_tag_t IhwO32i4o = dnnl_IhwO32i4o;
const format_tag_t Ihwo48i = dnnl_Ihwo48i;
const format_tag_t IhwO48i2o = dnnl_IhwO48i2o;
const format_tag_t IhwO48i4o = dnnl_IhwO48i4o;
const format_tag_t Ihwo64i = dnnl_Ihwo64i;
const format_tag_t IhwO64i2o = dnnl_IhwO64i2o;
const format_tag_t IhwO64i4o = dnnl_IhwO64i4o;

const format_tag_t gIhwo32i = dnnl_gIhwo32i;
const format_tag_t gIhwO32i2o = dnnl_gIhwO32i2o;
const format_tag_t gIhwO32i4o = dnnl_gIhwO32i4o;
const format_tag_t gIhwo48i = dnnl_gIhwo48i;
const format_tag_t gIhwO48i2o = dnnl_gIhwO48i2o;
const format_tag_t gIhwO48i4o = dnnl_gIhwO48i4o;
const format_tag_t gIhwo64i = dnnl_gIhwo64i;
const format_tag_t gIhwO64i2o = dnnl_gIhwO64i2o;
const format_tag_t gIhwO64i4o = dnnl_gIhwO64i4o;

const format_tag_t Iwo8i = dnnl_Iwo8i;
const format_tag_t IwO8i2o = dnnl_IwO8i2o;
const format_tag_t IwO8i4o = dnnl_IwO8i4o;
const format_tag_t Ihwo8i = dnnl_Ihwo8i;
const format_tag_t IhwO8i2o = dnnl_IhwO8i2o;
const format_tag_t IhwO8i4o = dnnl_IhwO8i4o;
const format_tag_t Idhwo8i = dnnl_Idhwo8i;
const format_tag_t IdhwO8i2o = dnnl_IdhwO8i2o;
const format_tag_t IdhwO8i4o = dnnl_IdhwO8i4o;

const format_tag_t Iwo16i = dnnl_Iwo16i;
const format_tag_t IwO16i2o = dnnl_IwO16i2o;
const format_tag_t IwO16i4o = dnnl_IwO16i4o;
const format_tag_t Ihwo16i = dnnl_Ihwo16i;
const format_tag_t IhwO16i2o = dnnl_IhwO16i2o;
const format_tag_t IhwO16i4o = dnnl_IhwO16i4o;
const format_tag_t Idhwo16i = dnnl_Idhwo16i;
const format_tag_t IdhwO16i2o = dnnl_IdhwO16i2o;
const format_tag_t IdhwO16i4o = dnnl_IdhwO16i4o;

const format_tag_t Iwo24i = dnnl_Iwo24i;
const format_tag_t IwO24i2o = dnnl_IwO24i2o;
const format_tag_t IwO24i4o = dnnl_IwO24i4o;
const format_tag_t Ihwo24i = dnnl_Ihwo24i;
const format_tag_t IhwO24i2o = dnnl_IhwO24i2o;
const format_tag_t IhwO24i4o = dnnl_IhwO24i4o;
const format_tag_t Idhwo24i = dnnl_Idhwo24i;
const format_tag_t IdhwO24i2o = dnnl_IdhwO24i2o;
const format_tag_t IdhwO24i4o = dnnl_IdhwO24i4o;

const format_tag_t gIwo8i = dnnl_gIwo8i;
const format_tag_t gIwO8i2o = dnnl_gIwO8i2o;
const format_tag_t gIwO8i4o = dnnl_gIwO8i4o;
const format_tag_t gIhwo8i = dnnl_gIhwo8i;
const format_tag_t gIhwO8i2o = dnnl_gIhwO8i2o;
const format_tag_t gIhwO8i4o = dnnl_gIhwO8i4o;
const format_tag_t gIdhwo8i = dnnl_gIdhwo8i;
const format_tag_t gIdhwO8i2o = dnnl_gIdhwO8i2o;
const format_tag_t gIdhwO8i4o = dnnl_gIdhwO8i4o;

const format_tag_t gIwo16i = dnnl_gIwo16i;
const format_tag_t gIwO16i2o = dnnl_gIwO16i2o;
const format_tag_t gIwO16i4o = dnnl_gIwO16i4o;

const format_tag_t gIhwo16i = dnnl_gIhwo16i;
const format_tag_t gIhwO16i2o = dnnl_gIhwO16i2o;
const format_tag_t gIhwO16i4o = dnnl_gIhwO16i4o;

const format_tag_t gIdhwo16i = dnnl_gIdhwo16i;
const format_tag_t gIdhwO16i2o = dnnl_gIdhwO16i2o;
const format_tag_t gIdhwO16i4o = dnnl_gIdhwO16i4o;

const format_tag_t gIwo24i = dnnl_gIwo24i;
const format_tag_t gIwO24i2o = dnnl_gIwO24i2o;
const format_tag_t gIwO24i4o = dnnl_gIwO24i4o;
const format_tag_t gIhwo24i = dnnl_gIhwo24i;
const format_tag_t gIhwO24i2o = dnnl_gIhwO24i2o;
const format_tag_t gIhwO24i4o = dnnl_gIhwO24i4o;
const format_tag_t gIdhwo24i = dnnl_gIdhwo24i;
const format_tag_t gIdhwO24i2o = dnnl_gIdhwO24i2o;
const format_tag_t gIdhwO24i4o = dnnl_gIdhwO24i4o;

const format_tag_t hwioG16g = dnnl_hwioG16g;
const format_tag_t hwioG8g = dnnl_hwioG8g;
const format_tag_t dhwioG16g = dnnl_dhwioG16g;
const format_tag_t dhwioG8g = dnnl_dhwioG8g;
const format_tag_t Owi24o = dnnl_Owi24o;
const format_tag_t Ohwi24o = dnnl_Ohwi24o;
const format_tag_t Odhwi24o = dnnl_Odhwi24o;
const format_tag_t gOwi24o = dnnl_gOwi24o;
const format_tag_t gOhwi24o = dnnl_gOhwi24o;
const format_tag_t gOdhwi24o = dnnl_gOdhwi24o;
const format_tag_t OwI24o2i = dnnl_OwI24o2i;
const format_tag_t OhwI24o2i = dnnl_OhwI24o2i;
const format_tag_t OdhwI24o2i = dnnl_OdhwI24o2i;
const format_tag_t gOwI24o2i = dnnl_gOwI24o2i;
const format_tag_t gOhwI24o2i = dnnl_gOhwI24o2i;
const format_tag_t gOdhwI24o2i = dnnl_gOdhwI24o2i;
const format_tag_t OwI24o4i = dnnl_OwI24o4i;
const format_tag_t OhwI24o4i = dnnl_OhwI24o4i;
const format_tag_t OdhwI24o4i = dnnl_OdhwI24o4i;
const format_tag_t gOwI24o4i = dnnl_gOwI24o4i;
const format_tag_t gOhwI24o4i = dnnl_gOhwI24o4i;
const format_tag_t gOdhwI24o4i = dnnl_gOdhwI24o4i;

const format_tag_t OI8i32o = dnnl_OI8i32o;
const format_tag_t OIw8i32o = dnnl_OIw8i32o;
const format_tag_t OwI8i32o = dnnl_OwI8i32o;
const format_tag_t OIhw8i32o = dnnl_OIhw8i32o;
const format_tag_t OhwI8i32o = dnnl_OhwI8i32o;
const format_tag_t OIdhw8i32o = dnnl_OIdhw8i32o;
const format_tag_t OdhwI8i32o = dnnl_OdhwI8i32o;
const format_tag_t OI8i24o = dnnl_OI8i24o;
const format_tag_t OIw8i24o = dnnl_OIw8i24o;
const format_tag_t OwI8i24o = dnnl_OwI8i24o;
const format_tag_t OIhw8i24o = dnnl_OIhw8i24o;
const format_tag_t OhwI8i24o = dnnl_OhwI8i24o;
const format_tag_t OIdhw8i24o = dnnl_OIdhw8i24o;
const format_tag_t OdhwI8i24o = dnnl_OdhwI8i24o;
const format_tag_t OI8i16o = dnnl_OI8i16o;
const format_tag_t OIw8i16o = dnnl_OIw8i16o;
const format_tag_t OwI8i16o = dnnl_OwI8i16o;
const format_tag_t OIhw8i16o = dnnl_OIhw8i16o;
const format_tag_t OhwI8i16o = dnnl_OhwI8i16o;
const format_tag_t OIdhw8i16o = dnnl_OIdhw8i16o;
const format_tag_t OdhwI8i16o = dnnl_OdhwI8i16o;
const format_tag_t OI8i8o = dnnl_OI8i8o;
const format_tag_t OI8i8o2i = dnnl_OI8i8o2i;
const format_tag_t OIw8i8o2i = dnnl_OIw8i8o2i;
const format_tag_t OwI8i8o2i = dnnl_OwI8i8o2i;
const format_tag_t OIhw8i8o2i = dnnl_OIhw8i8o2i;
const format_tag_t OhwI8i8o2i = dnnl_OhwI8i8o2i;
const format_tag_t OIdhw8i8o2i = dnnl_OIdhw8i8o2i;
const format_tag_t OdhwI8i8o2i = dnnl_OdhwI8i8o2i;
const format_tag_t OI8i24o2i = dnnl_OI8i24o2i;
const format_tag_t OIw8i24o2i = dnnl_OIw8i24o2i;
const format_tag_t OwI8i24o2i = dnnl_OwI8i24o2i;
const format_tag_t OIhw8i24o2i = dnnl_OIhw8i24o2i;
const format_tag_t OhwI8i24o2i = dnnl_OhwI8i24o2i;
const format_tag_t OIdhw8i24o2i = dnnl_OIdhw8i24o2i;
const format_tag_t OdhwI8i24o2i = dnnl_OdhwI8i24o2i;
} // namespace format_tag

using normalization_flags_t = dnnl_normalization_flags_t;
namespace normalization_flags {
const normalization_flags_t none = dnnl_normalization_flags_none;
const normalization_flags_t use_global_stats = dnnl_use_global_stats;
const normalization_flags_t use_scale = dnnl_use_scale;
const normalization_flags_t use_shift = dnnl_use_shift;
const normalization_flags_t fuse_norm_relu = dnnl_fuse_norm_relu;
const normalization_flags_t fuse_norm_add_relu = dnnl_fuse_norm_add_relu;
} // namespace normalization_flags

using rnn_flags_t = dnnl_rnn_flags_t;
namespace rnn_flags {
const rnn_flags_t undef = dnnl_rnn_flags_undef;
const rnn_flags_t diff_weights_overwrite
        = dnnl_rnn_flags_diff_weights_overwrite;
} // namespace rnn_flags

using engine_kind_t = dnnl_engine_kind_t;
namespace engine_kind {
const engine_kind_t any_engine = dnnl_any_engine;
const engine_kind_t cpu = dnnl_cpu;
const engine_kind_t gpu = dnnl_gpu;
} // namespace engine_kind

enum runtime_kind_t {
    dnnl_runtime_none,
    dnnl_runtime_seq,
    dnnl_runtime_omp,
    dnnl_runtime_tbb,
    dnnl_runtime_threadpool,
    dnnl_runtime_ocl,
    dnnl_runtime_sycl,
};

namespace runtime_kind {
const runtime_kind_t none = dnnl_runtime_none;
const runtime_kind_t seq = dnnl_runtime_seq;
const runtime_kind_t omp = dnnl_runtime_omp;
const runtime_kind_t tbb = dnnl_runtime_tbb;
const runtime_kind_t threadpool = dnnl_runtime_threadpool;
const runtime_kind_t ocl = dnnl_runtime_ocl;
const runtime_kind_t sycl = dnnl_runtime_sycl;
} // namespace runtime_kind

using primitive_kind_t = dnnl_primitive_kind_t;
namespace primitive_kind {
const primitive_kind_t undefined = dnnl_undefined_primitive;
const primitive_kind_t reorder = dnnl_reorder;
const primitive_kind_t concat = dnnl_concat;
const primitive_kind_t sum = dnnl_sum;
const primitive_kind_t convolution = dnnl_convolution;
const primitive_kind_t deconvolution = dnnl_deconvolution;
const primitive_kind_t shuffle = dnnl_shuffle;
const primitive_kind_t eltwise = dnnl_eltwise;
const primitive_kind_t pooling = dnnl_pooling;
const primitive_kind_t prelu = dnnl_prelu;
const primitive_kind_t lrn = dnnl_lrn;
const primitive_kind_t batch_normalization = dnnl_batch_normalization;
const primitive_kind_t inner_product = dnnl_inner_product;
const primitive_kind_t rnn = dnnl_rnn;
const primitive_kind_t gemm = dnnl_gemm;
const primitive_kind_t binary = dnnl_binary;
const primitive_kind_t matmul = dnnl_matmul;
const primitive_kind_t resampling = dnnl_resampling;
const primitive_kind_t reduction = dnnl_reduction;
const primitive_kind_t softmax = dnnl_softmax;
const primitive_kind_t layer_normalization = dnnl_layer_normalization;
const primitive_kind_t group_normalization = dnnl_group_normalization;

// Internal only primitive kinds.
const primitive_kind_t internal_only_start = (primitive_kind_t)(1 << 12);
const primitive_kind_t zero_pad = internal_only_start;
const primitive_kind_t sdpa = (primitive_kind_t)(internal_only_start + 1);
} // namespace primitive_kind

using query_t = dnnl_query_t;
namespace query {
const query_t undef = dnnl_query_undef;

const query_t engine = dnnl_query_engine;
const query_t primitive_kind = dnnl_query_primitive_kind;

const query_t num_of_inputs_s32 = dnnl_query_num_of_inputs_s32;
const query_t num_of_outputs_s32 = dnnl_query_num_of_outputs_s32;

const query_t time_estimate_f64 = dnnl_query_time_estimate_f64;
const query_t memory_consumption_s64 = dnnl_query_memory_consumption_s64;

const query_t scratchpad_engine = dnnl_query_scratchpad_engine;

const query_t impl_info_str = dnnl_query_impl_info_str;

const query_t reorder_src_engine = dnnl_query_reorder_src_engine;
const query_t reorder_dst_engine = dnnl_query_reorder_dst_engine;

const query_t prop_kind = dnnl_query_prop_kind;

const query_t cache_blob_id_size_s64 = dnnl_query_cache_blob_id_size_s64;
const query_t cache_blob_id = dnnl_query_cache_blob_id;

const query_t strides = dnnl_query_strides;
const query_t dilations = dnnl_query_dilations;
const query_t padding_l = dnnl_query_padding_l;
const query_t padding_r = dnnl_query_padding_r;
const query_t epsilon_f32 = dnnl_query_epsilon_f32;
const query_t flags = dnnl_query_flags;
const query_t alg_kind = dnnl_query_alg_kind;
const query_t alpha_f32 = dnnl_query_alpha_f32;
const query_t beta_f32 = dnnl_query_beta_f32;
const query_t axis_s32 = dnnl_query_axis_s32;
const query_t local_size_s64 = dnnl_query_local_size_s64;
const query_t k_f32 = dnnl_query_k_f32;
const query_t p_f32 = dnnl_query_p_f32;
const query_t factors = dnnl_query_factors;
const query_t cell_kind = dnnl_query_cell_kind;
const query_t direction = dnnl_query_direction;
const query_t activation_kind = dnnl_query_activation_kind;
const query_t kernel = dnnl_query_kernel;
const query_t group_size_s64 = dnnl_query_group_size_s64;

const query_t some_md = dnnl_query_some_md;
const query_t src_md = dnnl_query_src_md;
const query_t diff_src_md = dnnl_query_diff_src_md;
const query_t weights_md = dnnl_query_weights_md;
const query_t diff_weights_md = dnnl_query_diff_weights_md;
const query_t dst_md = dnnl_query_dst_md;
const query_t diff_dst_md = dnnl_query_diff_dst_md;
const query_t exec_arg_md = dnnl_query_exec_arg_md;

const query_t workspace_md = dnnl_query_workspace_md;
const query_t scratchpad_md = dnnl_query_scratchpad_md;

const query_t ndims_s32 = dnnl_query_ndims_s32;
const query_t dims = dnnl_query_dims;
const query_t data_type = dnnl_query_data_type;
const query_t submemory_offset_s64 = dnnl_query_submemory_offset_s64;
const query_t padded_dims = dnnl_query_padded_dims;
const query_t padded_offsets = dnnl_query_padded_offsets;
const query_t format_kind = dnnl_query_format_kind;
const query_t inner_nblks_s32 = dnnl_query_inner_nblks_s32;
const query_t inner_blks = dnnl_query_inner_blks;
const query_t inner_idxs = dnnl_query_inner_idxs;

#ifdef DNNL_EXPERIMENTAL_SPARSE
const query_t sparse_encoding = dnnl_query_sparse_encoding;
const query_t nnz_s64 = dnnl_query_nnz_s64;
const query_t num_handles_s32 = dnnl_query_num_handles_s32;
#else
const query_t sparse_encoding = static_cast<query_t>(266);
const query_t nnz_s64 = static_cast<query_t>(267);
const query_t num_handles_s32 = static_cast<query_t>(268);
#endif

// Internal only query kinds.
const query_t internal_only_start = (query_t)(1 << 12);
const query_t zero_pad_d = internal_only_start;
const query_t preferred_gpu_threads_per_eu = (query_t)(internal_only_start + 1);
} // namespace query

using rnn_direction_t = dnnl_rnn_direction_t;

using engine_t = dnnl_engine;
using primitive_attr_t = dnnl_primitive_attr;
using post_ops_t = dnnl_post_ops;
using memory_desc_t = dnnl_memory_desc;
using memory_t = dnnl_memory;

using stream_flags_t = dnnl_stream_flags_t;
namespace stream_flags {
const stream_flags_t in_order = dnnl_stream_in_order;
const stream_flags_t out_of_order = dnnl_stream_out_of_order;
const stream_flags_t default_flags = dnnl_stream_default_flags;
#ifdef DNNL_EXPERIMENTAL_PROFILING
const stream_flags_t profiling = dnnl_stream_profiling;
#else
const stream_flags_t profiling = static_cast<stream_flags_t>(1 << 2);
#endif
} // namespace stream_flags
using stream_t = dnnl_stream;

struct memory_storage_t;

/* forward declaration of the internal primitive_desc types */
struct batch_normalization_bwd_pd_t;
struct batch_normalization_fwd_pd_t;
struct batch_normalization_pd_t;
struct binary_pd_t;
struct concat_pd_t;
struct convolution_bwd_data_pd_t;
struct convolution_bwd_weights_pd_t;
struct convolution_fwd_pd_t;
struct convolution_pd_t;
struct deconvolution_bwd_data_pd_t;
struct deconvolution_bwd_weights_pd_t;
struct deconvolution_fwd_pd_t;
struct deconvolution_pd_t;
struct eltwise_bwd_pd_t;
struct eltwise_fwd_pd_t;
struct eltwise_pd_t;
struct gemm_pd_t;
struct group_normalization_bwd_pd_t;
struct group_normalization_fwd_pd_t;
struct group_normalization_pd_t;
struct inner_product_bwd_data_pd_t;
struct inner_product_bwd_weights_pd_t;
struct inner_product_fwd_pd_t;
struct inner_product_pd_t;
struct layer_normalization_bwd_pd_t;
struct layer_normalization_fwd_pd_t;
struct layer_normalization_pd_t;
struct lrn_bwd_pd_t;
struct lrn_fwd_pd_t;
struct lrn_pd_t;
struct matmul_pd_t;
struct pooling_bwd_pd_t;
struct pooling_fwd_pd_t;
struct pooling_pd_t;
struct prelu_pd_t;
struct reduction_pd_t;
struct reorder_pd_t;
struct resampling_pd_t;
struct rnn_bwd_pd_t;
struct rnn_fwd_pd_t;
struct rnn_pd_t;
struct shuffle_pd_t;
struct softmax_bwd_pd_t;
struct softmax_fwd_pd_t;
struct softmax_pd_t;
struct sum_pd_t;

} // namespace impl
} // namespace dnnl

#endif

// vim: et ts=4 sw=4 cindent cino+=l0,\:4,N-s
