/*******************************************************************************
* Copyright 2018-2025 Intel Corporation
* Copyright 2024 FUJITSU LIMITED
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

// DO NOT EDIT, AUTO-GENERATED
// Use this script to update the file: scripts/generate_dnnl_debug.py

// clang-format off

#include <assert.h>

#include "oneapi/dnnl/dnnl_debug.h"
#include "oneapi/dnnl/dnnl_types.h"

#include "common/c_types_map.hpp"

const char *dnnl_status2str(dnnl_status_t v) {
    if (v == dnnl_success) return "success";
    if (v == dnnl_out_of_memory) return "out_of_memory";
    if (v == dnnl_invalid_arguments) return "invalid_arguments";
    if (v == dnnl_unimplemented) return "unimplemented";
    if (v == dnnl_last_impl_reached) return "last_impl_reached";
    if (v == dnnl_runtime_error) return "runtime_error";
    if (v == dnnl_not_required) return "not_required";
    if (v == dnnl_invalid_graph) return "invalid_graph";
    if (v == dnnl_invalid_graph_op) return "invalid_graph_op";
    if (v == dnnl_invalid_shape) return "invalid_shape";
    if (v == dnnl_invalid_data_type) return "invalid_data_type";
    assert(!"unknown status");
    return "unknown status";
}

const char *dnnl_dt2str(dnnl_data_type_t v) {
    if (v == dnnl_data_type_undef) return "undef";
    if (v == dnnl_f16) return "f16";
    if (v == dnnl_bf16) return "bf16";
    if (v == dnnl_f32) return "f32";
    if (v == dnnl_s32) return "s32";
    if (v == dnnl_s8) return "s8";
    if (v == dnnl_u8) return "u8";
    if (v == dnnl_f64) return "f64";
    if (v == dnnl_boolean) return "boolean";
    if (v == dnnl_f8_e5m2) return "f8_e5m2";
    if (v == dnnl_f8_e4m3) return "f8_e4m3";
    if (v == dnnl_s4) return "s4";
    if (v == dnnl_u4) return "u4";
    if (v == dnnl_e8m0) return "e8m0";
    if (v == dnnl_f4_e2m1) return "f4_e2m1";
    if (v == dnnl_f4_e3m0) return "f4_e3m0";
    if (v == dnnl_data_type_max) return "data_type_max";
    assert(!"unknown dt");
    return "unknown dt";
}

const char *dnnl_fpmath_mode2str(dnnl_fpmath_mode_t v) {
    if (v == dnnl_fpmath_mode_strict) return "strict";
    if (v == dnnl_fpmath_mode_bf16) return "bf16";
    if (v == dnnl_fpmath_mode_f16) return "f16";
    if (v == dnnl_fpmath_mode_any) return "any";
    if (v == dnnl_fpmath_mode_tf32) return "tf32";
    assert(!"unknown fpmath_mode");
    return "unknown fpmath_mode";
}

const char *dnnl_accumulation_mode2str(dnnl_accumulation_mode_t v) {
    if (v == dnnl_accumulation_mode_strict) return "strict";
    if (v == dnnl_accumulation_mode_relaxed) return "relaxed";
    if (v == dnnl_accumulation_mode_any) return "any";
    if (v == dnnl_accumulation_mode_s32) return "s32";
    if (v == dnnl_accumulation_mode_f32) return "f32";
    if (v == dnnl_accumulation_mode_f16) return "f16";
    assert(!"unknown accumulation_mode");
    return "unknown accumulation_mode";
}

const char *dnnl_engine_kind2str(dnnl_engine_kind_t v) {
    if (v == dnnl_any_engine) return "any";
    if (v == dnnl_cpu) return "cpu";
    if (v == dnnl_gpu) return "gpu";
    assert(!"unknown engine_kind");
    return "unknown engine_kind";
}

#ifdef DNNL_EXPERIMENTAL_SPARSE
const char *dnnl_sparse_encoding2str(dnnl_sparse_encoding_t v) {
    if (v == dnnl_sparse_encoding_undef) return "undef";
    if (v == dnnl_csr) return "csr";
    if (v == dnnl_packed) return "packed";
    if (v == dnnl_coo) return "coo";
    assert(!"unknown sparse_encoding");
    return "unknown sparse_encoding";
}

#endif
const char *dnnl_fmt_tag2str(dnnl_format_tag_t v) {
    if (v == dnnl_format_tag_undef) return "undef";
    if (v == dnnl_format_tag_any) return "any";
    if (v == dnnl_a) return "a";
    if (v == dnnl_ab) return "ab";
    if (v == dnnl_abc) return "abc";
    if (v == dnnl_abcd) return "abcd";
    if (v == dnnl_abcde) return "abcde";
    if (v == dnnl_abcdef) return "abcdef";
    if (v == dnnl_abcdefg) return "abcdefg";
    if (v == dnnl_abcdefgh) return "abcdefgh";
    if (v == dnnl_abcdefghi) return "abcdefghi";
    if (v == dnnl_abcdefghij) return "abcdefghij";
    if (v == dnnl_abcdefghijk) return "abcdefghijk";
    if (v == dnnl_abcdefghijkl) return "abcdefghijkl";
    if (v == dnnl_ba) return "ba";
    if (v == dnnl_acb) return "acb";
    if (v == dnnl_bac) return "bac";
    if (v == dnnl_bca) return "bca";
    if (v == dnnl_cab) return "cab";
    if (v == dnnl_cba) return "cba";
    if (v == dnnl_abdc) return "abdc";
    if (v == dnnl_acbd) return "acbd";
    if (v == dnnl_acdb) return "acdb";
    if (v == dnnl_adbc) return "adbc";
    if (v == dnnl_adcb) return "adcb";
    if (v == dnnl_bacd) return "bacd";
    if (v == dnnl_bcda) return "bcda";
    if (v == dnnl_cdab) return "cdab";
    if (v == dnnl_cdba) return "cdba";
    if (v == dnnl_dcab) return "dcab";
    if (v == dnnl_abced) return "abced";
    if (v == dnnl_abdec) return "abdec";
    if (v == dnnl_acbde) return "acbde";
    if (v == dnnl_acdeb) return "acdeb";
    if (v == dnnl_adecb) return "adecb";
    if (v == dnnl_bacde) return "bacde";
    if (v == dnnl_bcdea) return "bcdea";
    if (v == dnnl_cdeab) return "cdeab";
    if (v == dnnl_cdeba) return "cdeba";
    if (v == dnnl_decab) return "decab";
    if (v == dnnl_abcdfe) return "abcdfe";
    if (v == dnnl_abdefc) return "abdefc";
    if (v == dnnl_abdfce) return "abdfce";
    if (v == dnnl_acbdef) return "acbdef";
    if (v == dnnl_adefcb) return "adefcb";
    if (v == dnnl_defcab) return "defcab";
    if (v == dnnl_abcdegf) return "abcdegf";
    if (v == dnnl_abcdefhg) return "abcdefhg";
    if (v == dnnl_abcdefgih) return "abcdefgih";
    if (v == dnnl_abcdefghji) return "abcdefghji";
    if (v == dnnl_abcdefghikj) return "abcdefghikj";
    if (v == dnnl_abcdefghijlk) return "abcdefghijlk";
    if (v == dnnl_Abc16a) return "Abc16a";
    if (v == dnnl_ABc16a16b) return "ABc16a16b";
    if (v == dnnl_ABc32a32b) return "ABc32a32b";
    if (v == dnnl_ABc4a4b) return "ABc4a4b";
    if (v == dnnl_aBc16b) return "aBc16b";
    if (v == dnnl_ABc16b16a) return "ABc16b16a";
    if (v == dnnl_Abc4a) return "Abc4a";
    if (v == dnnl_aBc32b) return "aBc32b";
    if (v == dnnl_aBc4b) return "aBc4b";
    if (v == dnnl_ABc4b16a4b) return "ABc4b16a4b";
    if (v == dnnl_ABc2b8a4b) return "ABc2b8a4b";
    if (v == dnnl_ABc16b16a4b) return "ABc16b16a4b";
    if (v == dnnl_ABc16b16a2b) return "ABc16b16a2b";
    if (v == dnnl_ABc4b4a) return "ABc4b4a";
    if (v == dnnl_ABc8a16b2a) return "ABc8a16b2a";
    if (v == dnnl_ABc8a8b) return "ABc8a8b";
    if (v == dnnl_ABc8a4b) return "ABc8a4b";
    if (v == dnnl_aBc8b) return "aBc8b";
    if (v == dnnl_ABc8b16a2b) return "ABc8b16a2b";
    if (v == dnnl_BAc8a16b2a) return "BAc8a16b2a";
    if (v == dnnl_ABc8b8a) return "ABc8b8a";
    if (v == dnnl_Abcd16a) return "Abcd16a";
    if (v == dnnl_Abcd8a) return "Abcd8a";
    if (v == dnnl_ABcd16a16b) return "ABcd16a16b";
    if (v == dnnl_Abcd32a) return "Abcd32a";
    if (v == dnnl_ABcd32a32b) return "ABcd32a32b";
    if (v == dnnl_aBcd16b) return "aBcd16b";
    if (v == dnnl_ABcd16b16a) return "ABcd16b16a";
    if (v == dnnl_aBCd16b16c) return "aBCd16b16c";
    if (v == dnnl_aBCd16c16b) return "aBCd16c16b";
    if (v == dnnl_Abcd4a) return "Abcd4a";
    if (v == dnnl_aBcd32b) return "aBcd32b";
    if (v == dnnl_aBcd4b) return "aBcd4b";
    if (v == dnnl_ABcd4b16a4b) return "ABcd4b16a4b";
    if (v == dnnl_ABcd16b16a4b) return "ABcd16b16a4b";
    if (v == dnnl_ABcd16b16a2b) return "ABcd16b16a2b";
    if (v == dnnl_ABcd4b4a) return "ABcd4b4a";
    if (v == dnnl_ABcd4a4b) return "ABcd4a4b";
    if (v == dnnl_aBCd2c4b2c) return "aBCd2c4b2c";
    if (v == dnnl_aBCd4b8c2b) return "aBCd4b8c2b";
    if (v == dnnl_aBCd4c16b4c) return "aBCd4c16b4c";
    if (v == dnnl_aBCd2c8b4c) return "aBCd2c8b4c";
    if (v == dnnl_aBCd16c16b4c) return "aBCd16c16b4c";
    if (v == dnnl_aBCd16c16b2c) return "aBCd16c16b2c";
    if (v == dnnl_aBCd4c4b) return "aBCd4c4b";
    if (v == dnnl_aBCd4b4c) return "aBCd4b4c";
    if (v == dnnl_ABcd8a16b2a) return "ABcd8a16b2a";
    if (v == dnnl_ABcd2b8a4b) return "ABcd2b8a4b";
    if (v == dnnl_ABcd8a8b) return "ABcd8a8b";
    if (v == dnnl_ABcd8a4b) return "ABcd8a4b";
    if (v == dnnl_aBcd8b) return "aBcd8b";
    if (v == dnnl_aBCd4c8b2c) return "aBCd4c8b2c";
    if (v == dnnl_ABcd8b16a2b) return "ABcd8b16a2b";
    if (v == dnnl_aBCd8b16c2b) return "aBCd8b16c2b";
    if (v == dnnl_BAcd8a16b2a) return "BAcd8a16b2a";
    if (v == dnnl_ABcd8b8a) return "ABcd8b8a";
    if (v == dnnl_aBCd8b8c) return "aBCd8b8c";
    if (v == dnnl_aBCd8b4c) return "aBCd8b4c";
    if (v == dnnl_aBCd8c16b2c) return "aBCd8c16b2c";
    if (v == dnnl_ABcde8a16b2a) return "ABcde8a16b2a";
    if (v == dnnl_aCBd8b16c2b) return "aCBd8b16c2b";
    if (v == dnnl_aBCd8c8b) return "aBCd8c8b";
    if (v == dnnl_Abcde16a) return "Abcde16a";
    if (v == dnnl_Abcde32a) return "Abcde32a";
    if (v == dnnl_ABcde16a16b) return "ABcde16a16b";
    if (v == dnnl_BAcde8a16b2a) return "BAcde8a16b2a";
    if (v == dnnl_aBCd2b4c2b) return "aBCd2b4c2b";
    if (v == dnnl_ABcde4b16a4b) return "ABcde4b16a4b";
    if (v == dnnl_ABcde2b8a4b) return "ABcde2b8a4b";
    if (v == dnnl_aBcde16b) return "aBcde16b";
    if (v == dnnl_ABcde16b16a) return "ABcde16b16a";
    if (v == dnnl_aBCde16b16c) return "aBCde16b16c";
    if (v == dnnl_aBCde16c16b) return "aBCde16c16b";
    if (v == dnnl_aBCde2c8b4c) return "aBCde2c8b4c";
    if (v == dnnl_Abcde4a) return "Abcde4a";
    if (v == dnnl_aBcde32b) return "aBcde32b";
    if (v == dnnl_aBcde4b) return "aBcde4b";
    if (v == dnnl_ABcde4b4a) return "ABcde4b4a";
    if (v == dnnl_ABcde4a4b) return "ABcde4a4b";
    if (v == dnnl_aBCde4b4c) return "aBCde4b4c";
    if (v == dnnl_aBCde2c4b2c) return "aBCde2c4b2c";
    if (v == dnnl_aBCde4b8c2b) return "aBCde4b8c2b";
    if (v == dnnl_aBCde4c16b4c) return "aBCde4c16b4c";
    if (v == dnnl_aBCde16c16b4c) return "aBCde16c16b4c";
    if (v == dnnl_aBCde16c16b2c) return "aBCde16c16b2c";
    if (v == dnnl_aBCde4c4b) return "aBCde4c4b";
    if (v == dnnl_Abcde8a) return "Abcde8a";
    if (v == dnnl_ABcde8a8b) return "ABcde8a8b";
    if (v == dnnl_ABcde8a4b) return "ABcde8a4b";
    if (v == dnnl_BAcde16b16a) return "BAcde16b16a";
    if (v == dnnl_aBcde8b) return "aBcde8b";
    if (v == dnnl_ABcde8b16a2b) return "ABcde8b16a2b";
    if (v == dnnl_aBCde8b16c2b) return "aBCde8b16c2b";
    if (v == dnnl_aBCde4c8b2c) return "aBCde4c8b2c";
    if (v == dnnl_aCBde8b16c2b) return "aCBde8b16c2b";
    if (v == dnnl_ABcde8b8a) return "ABcde8b8a";
    if (v == dnnl_ABcde32a32b) return "ABcde32a32b";
    if (v == dnnl_aBCde8b8c) return "aBCde8b8c";
    if (v == dnnl_aBCde8b4c) return "aBCde8b4c";
    if (v == dnnl_ABc4a8b8a4b) return "ABc4a8b8a4b";
    if (v == dnnl_ABcd4a8b8a4b) return "ABcd4a8b8a4b";
    if (v == dnnl_ABcde4a8b8a4b) return "ABcde4a8b8a4b";
    if (v == dnnl_BAc4b8a8b4a) return "BAc4b8a8b4a";
    if (v == dnnl_BAcd4b8a8b4a) return "BAcd4b8a8b4a";
    if (v == dnnl_BAcde4b8a8b4a) return "BAcde4b8a8b4a";
    if (v == dnnl_ABcd2a8b8a2b) return "ABcd2a8b8a2b";
    if (v == dnnl_aBCd4b8c8b4c) return "aBCd4b8c8b4c";
    if (v == dnnl_aBCde4b8c8b4c) return "aBCde4b8c8b4c";
    if (v == dnnl_aBCde2b8c8b2c) return "aBCde2b8c8b2c";
    if (v == dnnl_aBCde8c16b2c) return "aBCde8c16b2c";
    if (v == dnnl_aBCde8c8b) return "aBCde8c8b";
    if (v == dnnl_aBCde2b4c2b) return "aBCde2b4c2b";
    if (v == dnnl_aBcdef16b) return "aBcdef16b";
    if (v == dnnl_aBCdef16b16c) return "aBCdef16b16c";
    if (v == dnnl_aBCdef16c16b) return "aBCdef16c16b";
    if (v == dnnl_aBCdef4c16b4c) return "aBCdef4c16b4c";
    if (v == dnnl_aBCdef2c8b4c) return "aBCdef2c8b4c";
    if (v == dnnl_aBCdef4c8b2c) return "aBCdef4c8b2c";
    if (v == dnnl_aBCdef2b4c2b) return "aBCdef2b4c2b";
    if (v == dnnl_aBcdef4b) return "aBcdef4b";
    if (v == dnnl_aBCdef4c4b) return "aBCdef4c4b";
    if (v == dnnl_aBCdef4b4c) return "aBCdef4b4c";
    if (v == dnnl_aBCdef2c4b2c) return "aBCdef2c4b2c";
    if (v == dnnl_aBCdef4b8c2b) return "aBCdef4b8c2b";
    if (v == dnnl_aBCdef8b8c) return "aBCdef8b8c";
    if (v == dnnl_aBCdef8b4c) return "aBCdef8b4c";
    if (v == dnnl_aBCdef8c16b2c) return "aBCdef8c16b2c";
    if (v == dnnl_aBCdef4b8c8b4c) return "aBCdef4b8c8b4c";
    if (v == dnnl_aBCdef8b16c2b) return "aBCdef8b16c2b";
    if (v == dnnl_aCBdef8b16c2b) return "aCBdef8b16c2b";
    if (v == dnnl_aBCdef8c8b) return "aBCdef8c8b";
    if (v == dnnl_aBdc16b) return "aBdc16b";
    if (v == dnnl_aBdC16b2c) return "aBdC16b2c";
    if (v == dnnl_aBdC16b4c) return "aBdC16b4c";
    if (v == dnnl_aBdc4b) return "aBdc4b";
    if (v == dnnl_aBdc8b) return "aBdc8b";
    if (v == dnnl_aBdec16b) return "aBdec16b";
    if (v == dnnl_aBdeC16b2c) return "aBdeC16b2c";
    if (v == dnnl_aBdeC16b4c) return "aBdeC16b4c";
    if (v == dnnl_aBdec32b) return "aBdec32b";
    if (v == dnnl_aBdec4b) return "aBdec4b";
    if (v == dnnl_aBdec8b) return "aBdec8b";
    if (v == dnnl_aBdefc16b) return "aBdefc16b";
    if (v == dnnl_aBdefC16b2c) return "aBdefC16b2c";
    if (v == dnnl_aCBdef16c16b) return "aCBdef16c16b";
    if (v == dnnl_aBdefc4b) return "aBdefc4b";
    if (v == dnnl_aBdefc8b) return "aBdefc8b";
    if (v == dnnl_Abcdef16a) return "Abcdef16a";
    if (v == dnnl_Abcdef32a) return "Abcdef32a";
    if (v == dnnl_aBedc16b) return "aBedc16b";
    if (v == dnnl_Acb16a) return "Acb16a";
    if (v == dnnl_AcB16a2b) return "AcB16a2b";
    if (v == dnnl_AcB16a4b) return "AcB16a4b";
    if (v == dnnl_Acb4a) return "Acb4a";
    if (v == dnnl_Acb8a) return "Acb8a";
    if (v == dnnl_aCBd16b16c) return "aCBd16b16c";
    if (v == dnnl_aCBd16c16b) return "aCBd16c16b";
    if (v == dnnl_aCBde16b16c) return "aCBde16b16c";
    if (v == dnnl_aCBde16c16b) return "aCBde16c16b";
    if (v == dnnl_Acdb16a) return "Acdb16a";
    if (v == dnnl_AcdB16a2b) return "AcdB16a2b";
    if (v == dnnl_AcdB16a4b) return "AcdB16a4b";
    if (v == dnnl_Acdb32a) return "Acdb32a";
    if (v == dnnl_Acdb4a) return "Acdb4a";
    if (v == dnnl_Acdb8a) return "Acdb8a";
    if (v == dnnl_Acdeb16a) return "Acdeb16a";
    if (v == dnnl_AcdeB16a2b) return "AcdeB16a2b";
    if (v == dnnl_Acdeb4a) return "Acdeb4a";
    if (v == dnnl_Acdeb8a) return "Acdeb8a";
    if (v == dnnl_Adcb16a) return "Adcb16a";
    if (v == dnnl_BAc16a16b) return "BAc16a16b";
    if (v == dnnl_BAc16b16a) return "BAc16b16a";
    if (v == dnnl_BAcd16a16b) return "BAcd16a16b";
    if (v == dnnl_BAcd16b16a) return "BAcd16b16a";
    if (v == dnnl_aCBd4c8b8c4b) return "aCBd4c8b8c4b";
    if (v == dnnl_aCBde4c8b8c4b) return "aCBde4c8b8c4b";
    if (v == dnnl_aCBdef4c8b8c4b) return "aCBdef4c8b8c4b";
    if (v == dnnl_BAcde16a16b) return "BAcde16a16b";
    if (v == dnnl_aCBdef16b16c) return "aCBdef16b16c";
    if (v == dnnl_ABc16b32a) return "ABc16b32a";
    if (v == dnnl_ABc16b64a) return "ABc16b64a";
    if (v == dnnl_ABc4b32a4b) return "ABc4b32a4b";
    if (v == dnnl_ABc4b64a4b) return "ABc4b64a4b";
    if (v == dnnl_ABc8b32a2b) return "ABc8b32a2b";
    if (v == dnnl_ABc8b64a2b) return "ABc8b64a2b";
    if (v == dnnl_AB16b16a) return "AB16b16a";
    if (v == dnnl_AB16b32a) return "AB16b32a";
    if (v == dnnl_AB16b64a) return "AB16b64a";
    if (v == dnnl_AB8b16a2b) return "AB8b16a2b";
    if (v == dnnl_AB8b32a2b) return "AB8b32a2b";
    if (v == dnnl_AB8b64a2b) return "AB8b64a2b";
    if (v == dnnl_AB4b16a4b) return "AB4b16a4b";
    if (v == dnnl_AB4b32a4b) return "AB4b32a4b";
    if (v == dnnl_AB4b64a4b) return "AB4b64a4b";
    if (v == dnnl_AB16b16a4b) return "AB16b16a4b";
    if (v == dnnl_ABcd16b32a) return "ABcd16b32a";
    if (v == dnnl_ABcd16b64a) return "ABcd16b64a";
    if (v == dnnl_ABcd4b32a4b) return "ABcd4b32a4b";
    if (v == dnnl_ABcd4b64a4b) return "ABcd4b64a4b";
    if (v == dnnl_ABcd8b32a2b) return "ABcd8b32a2b";
    if (v == dnnl_ABcd8b64a2b) return "ABcd8b64a2b";
    if (v == dnnl_ABcde4b32a4b) return "ABcde4b32a4b";
    if (v == dnnl_ABcde4b64a4b) return "ABcde4b64a4b";
    if (v == dnnl_ABcde16b16a4b) return "ABcde16b16a4b";
    if (v == dnnl_ABcde16b16a2b) return "ABcde16b16a2b";
    if (v == dnnl_ABcde16b32a) return "ABcde16b32a";
    if (v == dnnl_ABcde16b64a) return "ABcde16b64a";
    if (v == dnnl_ABcde8b32a2b) return "ABcde8b32a2b";
    if (v == dnnl_ABcde8b64a2b) return "ABcde8b64a2b";
    if (v == dnnl_aBCdef16c16b4c) return "aBCdef16c16b4c";
    if (v == dnnl_aBCdef16c16b2c) return "aBCdef16c16b2c";
    if (v == dnnl_AB32a32b8a4b) return "AB32a32b8a4b";
    if (v == dnnl_AB8a4b) return "AB8a4b";
    if (v == dnnl_AB32a32b8a2b) return "AB32a32b8a2b";
    if (v == dnnl_AB8a2b) return "AB8a2b";
    if (v == dnnl_abDc32d) return "abDc32d";
    if (v == dnnl_abDC16d4c) return "abDC16d4c";
    if (v == dnnl_abDC32d4c) return "abDC32d4c";
    if (v == dnnl_abdEc32e) return "abdEc32e";
    if (v == dnnl_abdEC16e4c) return "abdEC16e4c";
    if (v == dnnl_abdEC32e2c) return "abdEC32e2c";
    if (v == dnnl_abdEC32e4c) return "abdEC32e4c";
    if (v == dnnl_aBdefC16b4c) return "aBdefC16b4c";
    if (v == dnnl_AcdeB16a4b) return "AcdeB16a4b";
    if (v == dnnl_ABcd16a16b2a) return "ABcd16a16b2a";
    if (v == dnnl_ABc16a16b2a) return "ABc16a16b2a";
    if (v == dnnl_aBCd16b16c2b) return "aBCd16b16c2b";
    if (v == dnnl_aBCde16b16c2b) return "aBCde16b16c2b";
    if (v == dnnl_Acb32a) return "Acb32a";
    if (v == dnnl_AcB32a2b) return "AcB32a2b";
    if (v == dnnl_AcB32a4b) return "AcB32a4b";
    if (v == dnnl_Acb48a) return "Acb48a";
    if (v == dnnl_AcB48a2b) return "AcB48a2b";
    if (v == dnnl_AcB48a4b) return "AcB48a4b";
    if (v == dnnl_Acb64a) return "Acb64a";
    if (v == dnnl_AcB64a2b) return "AcB64a2b";
    if (v == dnnl_AcB64a4b) return "AcB64a4b";
    if (v == dnnl_cBa2b) return "cBa2b";
    if (v == dnnl_cBa4b) return "cBa4b";
    if (v == dnnl_aBdc32b) return "aBdc32b";
    if (v == dnnl_aBdC32b2c) return "aBdC32b2c";
    if (v == dnnl_aBdC32b4c) return "aBdC32b4c";
    if (v == dnnl_aBdc48b) return "aBdc48b";
    if (v == dnnl_aBdC48b2c) return "aBdC48b2c";
    if (v == dnnl_aBdC48b4c) return "aBdC48b4c";
    if (v == dnnl_aBdc64b) return "aBdc64b";
    if (v == dnnl_aBdC64b2c) return "aBdC64b2c";
    if (v == dnnl_aBdC64b4c) return "aBdC64b4c";
    if (v == dnnl_adCb2c) return "adCb2c";
    if (v == dnnl_adCb4c) return "adCb4c";
    if (v == dnnl_AcdB32a2b) return "AcdB32a2b";
    if (v == dnnl_AcdB32a4b) return "AcdB32a4b";
    if (v == dnnl_Acdb48a) return "Acdb48a";
    if (v == dnnl_AcdB48a2b) return "AcdB48a2b";
    if (v == dnnl_AcdB48a4b) return "AcdB48a4b";
    if (v == dnnl_Acdb64a) return "Acdb64a";
    if (v == dnnl_AcdB64a2b) return "AcdB64a2b";
    if (v == dnnl_AcdB64a4b) return "AcdB64a4b";
    if (v == dnnl_cdBa2b) return "cdBa2b";
    if (v == dnnl_cdBa4b) return "cdBa4b";
    if (v == dnnl_aBdeC32b2c) return "aBdeC32b2c";
    if (v == dnnl_aBdeC32b4c) return "aBdeC32b4c";
    if (v == dnnl_aBdec48b) return "aBdec48b";
    if (v == dnnl_aBdeC48b2c) return "aBdeC48b2c";
    if (v == dnnl_aBdeC48b4c) return "aBdeC48b4c";
    if (v == dnnl_aBdec64b) return "aBdec64b";
    if (v == dnnl_aBdeC64b2c) return "aBdeC64b2c";
    if (v == dnnl_aBdeC64b4c) return "aBdeC64b4c";
    if (v == dnnl_adeCb2c) return "adeCb2c";
    if (v == dnnl_adeCb4c) return "adeCb4c";
    if (v == dnnl_Acdeb32a) return "Acdeb32a";
    if (v == dnnl_AcdeB32a2b) return "AcdeB32a2b";
    if (v == dnnl_AcdeB32a4b) return "AcdeB32a4b";
    if (v == dnnl_Acdeb48a) return "Acdeb48a";
    if (v == dnnl_AcdeB48a2b) return "AcdeB48a2b";
    if (v == dnnl_AcdeB48a4b) return "AcdeB48a4b";
    if (v == dnnl_Acdeb64a) return "Acdeb64a";
    if (v == dnnl_AcdeB64a2b) return "AcdeB64a2b";
    if (v == dnnl_AcdeB64a4b) return "AcdeB64a4b";
    if (v == dnnl_cdeBa2b) return "cdeBa2b";
    if (v == dnnl_cdeBa4b) return "cdeBa4b";
    if (v == dnnl_aBdefc32b) return "aBdefc32b";
    if (v == dnnl_aBdefC32b2c) return "aBdefC32b2c";
    if (v == dnnl_aBdefC32b4c) return "aBdefC32b4c";
    if (v == dnnl_aBdefc48b) return "aBdefc48b";
    if (v == dnnl_aBdefC48b2c) return "aBdefC48b2c";
    if (v == dnnl_aBdefC48b4c) return "aBdefC48b4c";
    if (v == dnnl_aBdefc64b) return "aBdefc64b";
    if (v == dnnl_aBdefC64b2c) return "aBdefC64b2c";
    if (v == dnnl_aBdefC64b4c) return "aBdefC64b4c";
    if (v == dnnl_adefCb2c) return "adefCb2c";
    if (v == dnnl_adefCb4c) return "adefCb4c";
    if (v == dnnl_AB16b32a4b) return "AB16b32a4b";
    if (v == dnnl_AB16b48a4b) return "AB16b48a4b";
    if (v == dnnl_AB16b64a4b) return "AB16b64a4b";
    if (v == dnnl_AB16b16a2b) return "AB16b16a2b";
    if (v == dnnl_AB16b32a2b) return "AB16b32a2b";
    if (v == dnnl_AB16b48a2b) return "AB16b48a2b";
    if (v == dnnl_AB16b64a2b) return "AB16b64a2b";
    if (v == dnnl_ABc16b32a4b) return "ABc16b32a4b";
    if (v == dnnl_ABc16b48a4b) return "ABc16b48a4b";
    if (v == dnnl_ABc16b64a4b) return "ABc16b64a4b";
    if (v == dnnl_ABc16b32a2b) return "ABc16b32a2b";
    if (v == dnnl_ABc16b48a2b) return "ABc16b48a2b";
    if (v == dnnl_ABc16b64a2b) return "ABc16b64a2b";
    if (v == dnnl_ABcd16b32a4b) return "ABcd16b32a4b";
    if (v == dnnl_ABcd16b48a4b) return "ABcd16b48a4b";
    if (v == dnnl_ABcd16b64a4b) return "ABcd16b64a4b";
    if (v == dnnl_ABcd16b32a2b) return "ABcd16b32a2b";
    if (v == dnnl_ABcd16b48a2b) return "ABcd16b48a2b";
    if (v == dnnl_ABcd16b64a2b) return "ABcd16b64a2b";
    if (v == dnnl_ABcde16b32a4b) return "ABcde16b32a4b";
    if (v == dnnl_ABcde16b48a4b) return "ABcde16b48a4b";
    if (v == dnnl_ABcde16b64a4b) return "ABcde16b64a4b";
    if (v == dnnl_ABcde16b32a2b) return "ABcde16b32a2b";
    if (v == dnnl_ABcde16b48a2b) return "ABcde16b48a2b";
    if (v == dnnl_ABcde16b64a2b) return "ABcde16b64a2b";
    if (v == dnnl_ABc32a16b) return "ABc32a16b";
    if (v == dnnl_ABcd32a16b) return "ABcd32a16b";
    if (v == dnnl_ABcde32a16b) return "ABcde32a16b";
    if (v == dnnl_AB48a16b) return "AB48a16b";
    if (v == dnnl_AB48a32b) return "AB48a32b";
    if (v == dnnl_ABc40a16b) return "ABc40a16b";
    if (v == dnnl_ABc40a32b) return "ABc40a32b";
    if (v == dnnl_aBC48b16c) return "aBC48b16c";
    if (v == dnnl_aBC48b32c) return "aBC48b32c";
    if (v == dnnl_ABcd40a16b) return "ABcd40a16b";
    if (v == dnnl_ABcd40a32b) return "ABcd40a32b";
    if (v == dnnl_abCd32c) return "abCd32c";
    if (v == dnnl_abdCe32c) return "abdCe32c";
    if (v == dnnl_abdCE32c2e) return "abdCE32c2e";
    if (v == dnnl_BA16a16b2a) return "BA16a16b2a";
    if (v == dnnl_BA16a32b2a) return "BA16a32b2a";
    if (v == dnnl_BA16a48b2a) return "BA16a48b2a";
    if (v == dnnl_BA16a64b2a) return "BA16a64b2a";
    if (v == dnnl_BA16a16b4a) return "BA16a16b4a";
    if (v == dnnl_BA16a32b4a) return "BA16a32b4a";
    if (v == dnnl_BA16a48b4a) return "BA16a48b4a";
    if (v == dnnl_BA16a64b4a) return "BA16a64b4a";
    if (v == dnnl_ABcd8a2b) return "ABcd8a2b";
    if (v == dnnl_aBdeC16c16b2c) return "aBdeC16c16b2c";
    if (v == dnnl_aBdeC16c16b4c) return "aBdeC16c16b4c";
    if (v == dnnl_aBdefC16c16b2c) return "aBdefC16c16b2c";
    if (v == dnnl_AcB16b16a2b) return "AcB16b16a2b";
    if (v == dnnl_AcB16b16a4b) return "AcB16b16a4b";
    if (v == dnnl_AcdB16b16a2b) return "AcdB16b16a2b";
    if (v == dnnl_AcdB16b16a4b) return "AcdB16b16a4b";
    if (v == dnnl_AcdeB16b16a2b) return "AcdeB16b16a2b";
    if (v == dnnl_aBdefC16c16b4c) return "aBdefC16c16b4c";
    if (v == dnnl_AcdeB16b16a4b) return "AcdeB16b16a4b";
    if (v == dnnl_AcB16b32a2b) return "AcB16b32a2b";
    if (v == dnnl_AcB16b32a4b) return "AcB16b32a4b";
    if (v == dnnl_AcB16b48a2b) return "AcB16b48a2b";
    if (v == dnnl_AcB16b48a4b) return "AcB16b48a4b";
    if (v == dnnl_AcB16b64a2b) return "AcB16b64a2b";
    if (v == dnnl_AcB16b64a4b) return "AcB16b64a4b";
    if (v == dnnl_aBdC16c16b2c) return "aBdC16c16b2c";
    if (v == dnnl_aBdC16c16b4c) return "aBdC16c16b4c";
    if (v == dnnl_aBdC16c32b2c) return "aBdC16c32b2c";
    if (v == dnnl_aBdC16c32b4c) return "aBdC16c32b4c";
    if (v == dnnl_aBdC16c48b2c) return "aBdC16c48b2c";
    if (v == dnnl_aBdC16c48b4c) return "aBdC16c48b4c";
    if (v == dnnl_aBdC16c64b2c) return "aBdC16c64b2c";
    if (v == dnnl_aBdC16c64b4c) return "aBdC16c64b4c";
    if (v == dnnl_AcdB16b32a2b) return "AcdB16b32a2b";
    if (v == dnnl_AcdB16b32a4b) return "AcdB16b32a4b";
    if (v == dnnl_AcdB16b48a2b) return "AcdB16b48a2b";
    if (v == dnnl_AcdB16b48a4b) return "AcdB16b48a4b";
    if (v == dnnl_AcdB16b64a2b) return "AcdB16b64a2b";
    if (v == dnnl_AcdB16b64a4b) return "AcdB16b64a4b";
    if (v == dnnl_aBdeC16c32b2c) return "aBdeC16c32b2c";
    if (v == dnnl_aBdeC16c32b4c) return "aBdeC16c32b4c";
    if (v == dnnl_aBdeC16c48b2c) return "aBdeC16c48b2c";
    if (v == dnnl_aBdeC16c48b4c) return "aBdeC16c48b4c";
    if (v == dnnl_aBdeC16c64b2c) return "aBdeC16c64b2c";
    if (v == dnnl_aBdeC16c64b4c) return "aBdeC16c64b4c";
    if (v == dnnl_AcdeB16b32a2b) return "AcdeB16b32a2b";
    if (v == dnnl_AcdeB16b32a4b) return "AcdeB16b32a4b";
    if (v == dnnl_AcdeB16b48a2b) return "AcdeB16b48a2b";
    if (v == dnnl_AcdeB16b48a4b) return "AcdeB16b48a4b";
    if (v == dnnl_AcdeB16b64a2b) return "AcdeB16b64a2b";
    if (v == dnnl_AcdeB16b64a4b) return "AcdeB16b64a4b";
    if (v == dnnl_aBdefC16c32b2c) return "aBdefC16c32b2c";
    if (v == dnnl_aBdefC16c32b4c) return "aBdefC16c32b4c";
    if (v == dnnl_aBdefC16c48b2c) return "aBdefC16c48b2c";
    if (v == dnnl_aBdefC16c48b4c) return "aBdefC16c48b4c";
    if (v == dnnl_aBdefC16c64b2c) return "aBdefC16c64b2c";
    if (v == dnnl_aBdefC16c64b4c) return "aBdefC16c64b4c";
    if (v == dnnl_decbA16a) return "decbA16a";
    if (v == dnnl_ABc4a2b) return "ABc4a2b";
    if (v == dnnl_ABc8a2b) return "ABc8a2b";
    if (v == dnnl_aBCd8b2c) return "aBCd8b2c";
    if (v == dnnl_ABcde4a2b) return "ABcde4a2b";
    if (v == dnnl_ABcde8a2b) return "ABcde8a2b";
    if (v == dnnl_ABcde40a16b) return "ABcde40a16b";
    if (v == dnnl_ABcde40a32b) return "ABcde40a32b";
    if (v == dnnl_aBCde8b2c) return "aBCde8b2c";
    if (v == dnnl_ABcde4a8b8a2b) return "ABcde4a8b8a2b";
    if (v == dnnl_ABcd4a8b8a2b) return "ABcd4a8b8a2b";
    if (v == dnnl_ABc4a8b8a2b) return "ABc4a8b8a2b";
    if (v == dnnl_aBCdef4b8c8b2c) return "aBCdef4b8c8b2c";
    if (v == dnnl_aBCde4b8c8b2c) return "aBCde4b8c8b2c";
    if (v == dnnl_aBCd4b8c8b2c) return "aBCd4b8c8b2c";
    if (v == dnnl_BAcde4b8a8b2a) return "BAcde4b8a8b2a";
    if (v == dnnl_BAcd4b8a8b2a) return "BAcd4b8a8b2a";
    if (v == dnnl_BAc4b8a8b2a) return "BAc4b8a8b2a";
    if (v == dnnl_aCBdef4c8b8c2b) return "aCBdef4c8b8c2b";
    if (v == dnnl_aCBde4c8b8c2b) return "aCBde4c8b8c2b";
    if (v == dnnl_aCBd4c8b8c2b) return "aCBd4c8b8c2b";
    if (v == dnnl_aBCdef8b2c) return "aBCdef8b2c";
    if (v == dnnl_AB32a16b) return "AB32a16b";
    if (v == dnnl_AB32a32b) return "AB32a32b";
    if (v == dnnl_BA4b8a8b2a) return "BA4b8a8b2a";
    if (v == dnnl_BA4b8a8b4a) return "BA4b8a8b4a";
    if (v == dnnl_aBC32b16c) return "aBC32b16c";
    if (v == dnnl_aBC32b32c) return "aBC32b32c";
    if (v == dnnl_aCB4c8b8c2b) return "aCB4c8b8c2b";
    if (v == dnnl_aCB4c8b8c4b) return "aCB4c8b8c4b";
    if (v == dnnl_ABcd4a2b) return "ABcd4a2b";
    if (v == dnnl_ABc2b8a16b4a) return "ABc2b8a16b4a";
    if (v == dnnl_ABcd2b8a16b4a) return "ABcd2b8a16b4a";
    if (v == dnnl_ABcde2b8a16b4a) return "ABcde2b8a16b4a";
    if (v == dnnl_ABc2a8b16a4b) return "ABc2a8b16a4b";
    if (v == dnnl_ABc2a8b16a2b) return "ABc2a8b16a2b";
    if (v == dnnl_ABc2b32a8b) return "ABc2b32a8b";
    if (v == dnnl_ABcd2a8b16a4b) return "ABcd2a8b16a4b";
    if (v == dnnl_ABcd2a8b16a2b) return "ABcd2a8b16a2b";
    if (v == dnnl_aCBd2c8b16c2b) return "aCBd2c8b16c2b";
    if (v == dnnl_ABcd2b32a8b) return "ABcd2b32a8b";
    if (v == dnnl_aBCd2c8b16c2b) return "aBCd2c8b16c2b";
    if (v == dnnl_ABcde2a8b16a4b) return "ABcde2a8b16a4b";
    if (v == dnnl_ABcde2a8b16a2b) return "ABcde2a8b16a2b";
    if (v == dnnl_aCBde2c8b16c2b) return "aCBde2c8b16c2b";
    if (v == dnnl_ABcde2b32a8b) return "ABcde2b32a8b";
    if (v == dnnl_aBC2b8c16b2c) return "aBC2b8c16b2c";
    if (v == dnnl_aBCd2b8c16b2c) return "aBCd2b8c16b2c";
    if (v == dnnl_aBCde2b8c16b2c) return "aBCde2b8c16b2c";
    if (v == dnnl_aBCdef2b8c16b2c) return "aBCdef2b8c16b2c";
    if (v == dnnl_BAcde2b8a16b4a) return "BAcde2b8a16b4a";
    if (v == dnnl_BAcd2b8a16b4a) return "BAcd2b8a16b4a";
    if (v == dnnl_BAc2b8a16b4a) return "BAc2b8a16b4a";
    if (v == dnnl_BAcde2b8a16b2a) return "BAcde2b8a16b2a";
    if (v == dnnl_BAcd2b8a16b2a) return "BAcd2b8a16b2a";
    if (v == dnnl_BAc2b8a16b2a) return "BAc2b8a16b2a";
    if (v == dnnl_aBCde2c8b16c2b) return "aBCde2c8b16c2b";
    if (v == dnnl_aBCdef2c8b16c2b) return "aBCdef2c8b16c2b";
    if (v == dnnl_aCBdef2c8b16c2b) return "aCBdef2c8b16c2b";
    if (v == dnnl_aBCd2b8c16b4c) return "aBCd2b8c16b4c";
    if (v == dnnl_aBCde2b8c16b4c) return "aBCde2b8c16b4c";
    if (v == dnnl_BA4b8a16b2a) return "BA4b8a16b2a";
    if (v == dnnl_BA4b8a16b4a) return "BA4b8a16b4a";
    if (v == dnnl_aCB4c8b16c2b) return "aCB4c8b16c2b";
    if (v == dnnl_aCB4c8b16c4b) return "aCB4c8b16c4b";
    if (v == dnnl_BA16a16b) return "BA16a16b";
    if (v == dnnl_BA16a32b) return "BA16a32b";
    if (v == dnnl_BA16a48b) return "BA16a48b";
    if (v == dnnl_BA16a64b) return "BA16a64b";
    if (v == dnnl_aCB16c2b) return "aCB16c2b";
    if (v == dnnl_aCB16c4b) return "aCB16c4b";
    if (v == dnnl_BA16b2a) return "BA16b2a";
    if (v == dnnl_BA16b4a) return "BA16b4a";
    if (v == dnnl_aBC16b16c) return "aBC16b16c";
    if (v == dnnl_aBC16b32c) return "aBC16b32c";
    if (v == dnnl_AB16a16b) return "AB16a16b";
    if (v == dnnl_AB16a32b) return "AB16a32b";
    if (v == dnnl_ABcde16a16b2a) return "ABcde16a16b2a";
    if (v == dnnl_aBCdef16b16c2b) return "aBCdef16b16c2b";
    if (v == dnnl_Acedb16a) return "Acedb16a";
    if (v == dnnl_aBdfec16b) return "aBdfec16b";
    if (v == dnnl_abdEC64e2c) return "abdEC64e2c";
    if (v == dnnl_abdEC64e4c) return "abdEC64e4c";
    if (v == dnnl_aCB16b16c) return "aCB16b16c";
    if (v == dnnl_aCB16b32c) return "aCB16b32c";
    if (v == dnnl_aCB16b48c) return "aCB16b48c";
    if (v == dnnl_aCB16b64c) return "aCB16b64c";
    if (v == dnnl_aCB16b16c2b) return "aCB16b16c2b";
    if (v == dnnl_aCB16b32c2b) return "aCB16b32c2b";
    if (v == dnnl_aCB16b48c2b) return "aCB16b48c2b";
    if (v == dnnl_aCB16b64c2b) return "aCB16b64c2b";
    if (v == dnnl_aCB16b16c4b) return "aCB16b16c4b";
    if (v == dnnl_aCB16b32c4b) return "aCB16b32c4b";
    if (v == dnnl_aCB16b48c4b) return "aCB16b48c4b";
    if (v == dnnl_aCB16b64c4b) return "aCB16b64c4b";
    if (v == dnnl_abCd4c) return "abCd4c";
    if (v == dnnl_abCde4c) return "abCde4c";
    if (v == dnnl_abCdef4c) return "abCdef4c";
    if (v == dnnl_abCde32c) return "abCde32c";
    if (v == dnnl_abCdef32c) return "abCdef32c";
    if (v == dnnl_ABcd16a32b) return "ABcd16a32b";
    if (v == dnnl_decbA8a) return "decbA8a";
    if (v == dnnl_aCdefB16b32c2b) return "aCdefB16b32c2b";
    if (v == dnnl_aCdefB16b32c4b) return "aCdefB16b32c4b";
    if (v == dnnl_aCdefB16b48c2b) return "aCdefB16b48c2b";
    if (v == dnnl_aCdefB16b48c4b) return "aCdefB16b48c4b";
    if (v == dnnl_aCdefB16b64c2b) return "aCdefB16b64c2b";
    if (v == dnnl_aCdefB16b64c4b) return "aCdefB16b64c4b";
    if (v == dnnl_BcdeA16a32b2a) return "BcdeA16a32b2a";
    if (v == dnnl_BcdeA16a32b4a) return "BcdeA16a32b4a";
    if (v == dnnl_BcdeA16a48b2a) return "BcdeA16a48b2a";
    if (v == dnnl_BcdeA16a48b4a) return "BcdeA16a48b4a";
    if (v == dnnl_BcdeA16a64b2a) return "BcdeA16a64b2a";
    if (v == dnnl_BcdeA16a64b4a) return "BcdeA16a64b4a";
    if (v == dnnl_aCdefb32c) return "aCdefb32c";
    if (v == dnnl_aCdefB32c2b) return "aCdefB32c2b";
    if (v == dnnl_aCdefB32c4b) return "aCdefB32c4b";
    if (v == dnnl_aCdefb48c) return "aCdefb48c";
    if (v == dnnl_aCdefB48c2b) return "aCdefB48c2b";
    if (v == dnnl_aCdefB48c4b) return "aCdefB48c4b";
    if (v == dnnl_aCdefb64c) return "aCdefb64c";
    if (v == dnnl_aCdefB64c2b) return "aCdefB64c2b";
    if (v == dnnl_aCdefB64c4b) return "aCdefB64c4b";
    if (v == dnnl_Bcdea32b) return "Bcdea32b";
    if (v == dnnl_BcdeA32b2a) return "BcdeA32b2a";
    if (v == dnnl_BcdeA32b4a) return "BcdeA32b4a";
    if (v == dnnl_Bcdea48b) return "Bcdea48b";
    if (v == dnnl_BcdeA48b2a) return "BcdeA48b2a";
    if (v == dnnl_BcdeA48b4a) return "BcdeA48b4a";
    if (v == dnnl_Bcdea64b) return "Bcdea64b";
    if (v == dnnl_BcdeA64b2a) return "BcdeA64b2a";
    if (v == dnnl_BcdeA64b4a) return "BcdeA64b4a";
    if (v == dnnl_Bca32b) return "Bca32b";
    if (v == dnnl_BcA32b2a) return "BcA32b2a";
    if (v == dnnl_BcA32b4a) return "BcA32b4a";
    if (v == dnnl_Bca48b) return "Bca48b";
    if (v == dnnl_BcA48b2a) return "BcA48b2a";
    if (v == dnnl_BcA48b4a) return "BcA48b4a";
    if (v == dnnl_Bca64b) return "Bca64b";
    if (v == dnnl_BcA64b2a) return "BcA64b2a";
    if (v == dnnl_BcA64b4a) return "BcA64b4a";
    if (v == dnnl_aCdb32c) return "aCdb32c";
    if (v == dnnl_aCdB32c2b) return "aCdB32c2b";
    if (v == dnnl_aCdB32c4b) return "aCdB32c4b";
    if (v == dnnl_aCdb48c) return "aCdb48c";
    if (v == dnnl_aCdB48c2b) return "aCdB48c2b";
    if (v == dnnl_aCdB48c4b) return "aCdB48c4b";
    if (v == dnnl_aCdb64c) return "aCdb64c";
    if (v == dnnl_aCdB64c2b) return "aCdB64c2b";
    if (v == dnnl_aCdB64c4b) return "aCdB64c4b";
    if (v == dnnl_BcA16a16b2a) return "BcA16a16b2a";
    if (v == dnnl_BcA16a16b4a) return "BcA16a16b4a";
    if (v == dnnl_BcdA16a16b2a) return "BcdA16a16b2a";
    if (v == dnnl_BcdA16a16b4a) return "BcdA16a16b4a";
    if (v == dnnl_BcdeA16a16b2a) return "BcdeA16a16b2a";
    if (v == dnnl_BcdeA16a16b4a) return "BcdeA16a16b4a";
    if (v == dnnl_aCdB16b16c2b) return "aCdB16b16c2b";
    if (v == dnnl_aCdB16b16c4b) return "aCdB16b16c4b";
    if (v == dnnl_aCdeB16b16c2b) return "aCdeB16b16c2b";
    if (v == dnnl_aCdeB16b16c4b) return "aCdeB16b16c4b";
    if (v == dnnl_aCdefB16b16c2b) return "aCdefB16b16c2b";
    if (v == dnnl_aCdefB16b16c4b) return "aCdefB16b16c4b";
    if (v == dnnl_BcA16a32b2a) return "BcA16a32b2a";
    if (v == dnnl_BcA16a32b4a) return "BcA16a32b4a";
    if (v == dnnl_BcA16a48b2a) return "BcA16a48b2a";
    if (v == dnnl_BcA16a48b4a) return "BcA16a48b4a";
    if (v == dnnl_BcA16a64b2a) return "BcA16a64b2a";
    if (v == dnnl_BcA16a64b4a) return "BcA16a64b4a";
    if (v == dnnl_aCdB16b32c2b) return "aCdB16b32c2b";
    if (v == dnnl_aCdB16b32c4b) return "aCdB16b32c4b";
    if (v == dnnl_aCdB16b48c2b) return "aCdB16b48c2b";
    if (v == dnnl_aCdB16b48c4b) return "aCdB16b48c4b";
    if (v == dnnl_aCdB16b64c2b) return "aCdB16b64c2b";
    if (v == dnnl_aCdB16b64c4b) return "aCdB16b64c4b";
    if (v == dnnl_BcdA16a32b2a) return "BcdA16a32b2a";
    if (v == dnnl_BcdA16a32b4a) return "BcdA16a32b4a";
    if (v == dnnl_BcdA16a48b2a) return "BcdA16a48b2a";
    if (v == dnnl_BcdA16a48b4a) return "BcdA16a48b4a";
    if (v == dnnl_BcdA16a64b2a) return "BcdA16a64b2a";
    if (v == dnnl_BcdA16a64b4a) return "BcdA16a64b4a";
    if (v == dnnl_aCdeB16b32c2b) return "aCdeB16b32c2b";
    if (v == dnnl_aCdeB16b32c4b) return "aCdeB16b32c4b";
    if (v == dnnl_aCdeB16b48c2b) return "aCdeB16b48c2b";
    if (v == dnnl_aCdeB16b48c4b) return "aCdeB16b48c4b";
    if (v == dnnl_aCdeB16b64c2b) return "aCdeB16b64c2b";
    if (v == dnnl_aCdeB16b64c4b) return "aCdeB16b64c4b";
    if (v == dnnl_Bca16b) return "Bca16b";
    if (v == dnnl_BcA16b2a) return "BcA16b2a";
    if (v == dnnl_BcA16b4a) return "BcA16b4a";
    if (v == dnnl_Bcda16b) return "Bcda16b";
    if (v == dnnl_BcdA16b2a) return "BcdA16b2a";
    if (v == dnnl_BcdA16b4a) return "BcdA16b4a";
    if (v == dnnl_Bcdea16b) return "Bcdea16b";
    if (v == dnnl_BcdeA16b2a) return "BcdeA16b2a";
    if (v == dnnl_BcdeA16b4a) return "BcdeA16b4a";
    if (v == dnnl_aCdb16c) return "aCdb16c";
    if (v == dnnl_aCdB16c2b) return "aCdB16c2b";
    if (v == dnnl_aCdB16c4b) return "aCdB16c4b";
    if (v == dnnl_aCdeb16c) return "aCdeb16c";
    if (v == dnnl_aCdeB16c2b) return "aCdeB16c2b";
    if (v == dnnl_aCdeB16c4b) return "aCdeB16c4b";
    if (v == dnnl_aCdefb16c) return "aCdefb16c";
    if (v == dnnl_aCdefB16c2b) return "aCdefB16c2b";
    if (v == dnnl_aCdefB16c4b) return "aCdefB16c4b";
    if (v == dnnl_Bcda32b) return "Bcda32b";
    if (v == dnnl_BcdA32b2a) return "BcdA32b2a";
    if (v == dnnl_BcdA32b4a) return "BcdA32b4a";
    if (v == dnnl_Bcda48b) return "Bcda48b";
    if (v == dnnl_BcdA48b2a) return "BcdA48b2a";
    if (v == dnnl_BcdA48b4a) return "BcdA48b4a";
    if (v == dnnl_Bcda64b) return "Bcda64b";
    if (v == dnnl_BcdA64b2a) return "BcdA64b2a";
    if (v == dnnl_BcdA64b4a) return "BcdA64b4a";
    if (v == dnnl_aCdeb32c) return "aCdeb32c";
    if (v == dnnl_aCdeB32c2b) return "aCdeB32c2b";
    if (v == dnnl_aCdeB32c4b) return "aCdeB32c4b";
    if (v == dnnl_aCdeb48c) return "aCdeb48c";
    if (v == dnnl_aCdeB48c2b) return "aCdeB48c2b";
    if (v == dnnl_aCdeB48c4b) return "aCdeB48c4b";
    if (v == dnnl_aCdeb64c) return "aCdeb64c";
    if (v == dnnl_aCdeB64c2b) return "aCdeB64c2b";
    if (v == dnnl_aCdeB64c4b) return "aCdeB64c4b";
    if (v == dnnl_Acb24a) return "Acb24a";
    if (v == dnnl_Acdb24a) return "Acdb24a";
    if (v == dnnl_Acdeb24a) return "Acdeb24a";
    if (v == dnnl_aBdc24b) return "aBdc24b";
    if (v == dnnl_aBdec24b) return "aBdec24b";
    if (v == dnnl_aBdefc24b) return "aBdefc24b";
    if (v == dnnl_abDc16d) return "abDc16d";
    if (v == dnnl_abdEc16e) return "abdEc16e";
    if (v == dnnl_abdCe16c) return "abdCe16c";
    if (v == dnnl_AcB24a2b) return "AcB24a2b";
    if (v == dnnl_AcdB24a2b) return "AcdB24a2b";
    if (v == dnnl_AcdeB24a2b) return "AcdeB24a2b";
    if (v == dnnl_aBdC24b2c) return "aBdC24b2c";
    if (v == dnnl_aBdeC24b2c) return "aBdeC24b2c";
    if (v == dnnl_aBdefC24b2c) return "aBdefC24b2c";
    if (v == dnnl_AcB8a2b) return "AcB8a2b";
    if (v == dnnl_AcdB8a2b) return "AcdB8a2b";
    if (v == dnnl_AcdeB8a2b) return "AcdeB8a2b";
    if (v == dnnl_aBdC8b2c) return "aBdC8b2c";
    if (v == dnnl_aBdeC8b2c) return "aBdeC8b2c";
    if (v == dnnl_aBdefC8b2c) return "aBdefC8b2c";
    if (v == dnnl_AB8b32a) return "AB8b32a";
    if (v == dnnl_ABc8b32a) return "ABc8b32a";
    if (v == dnnl_ABcd8b32a) return "ABcd8b32a";
    if (v == dnnl_ABcde8b32a) return "ABcde8b32a";
    if (v == dnnl_AB8b24a) return "AB8b24a";
    if (v == dnnl_ABc8b24a) return "ABc8b24a";
    if (v == dnnl_ABcd8b24a) return "ABcd8b24a";
    if (v == dnnl_ABcde8b24a) return "ABcde8b24a";
    if (v == dnnl_AB8b16a) return "AB8b16a";
    if (v == dnnl_ABc8b16a) return "ABc8b16a";
    if (v == dnnl_ABcd8b16a) return "ABcd8b16a";
    if (v == dnnl_ABcde8b16a) return "ABcde8b16a";
    if (v == dnnl_AB8b8a) return "AB8b8a";
    if (v == dnnl_AB4b8a4b) return "AB4b8a4b";
    if (v == dnnl_AB4b24a4b) return "AB4b24a4b";
    if (v == dnnl_ABc4b8a4b) return "ABc4b8a4b";
    if (v == dnnl_ABc4b24a4b) return "ABc4b24a4b";
    if (v == dnnl_ABcd4b8a4b) return "ABcd4b8a4b";
    if (v == dnnl_ABcd4b24a4b) return "ABcd4b24a4b";
    if (v == dnnl_ABcde4b8a4b) return "ABcde4b8a4b";
    if (v == dnnl_ABcde4b24a4b) return "ABcde4b24a4b";
    if (v == dnnl_AB8b24a2b) return "AB8b24a2b";
    if (v == dnnl_ABc8b24a2b) return "ABc8b24a2b";
    if (v == dnnl_ABcd8b24a2b) return "ABcd8b24a2b";
    if (v == dnnl_ABcde8b24a2b) return "ABcde8b24a2b";
    if (v == dnnl_AB8b8a2b) return "AB8b8a2b";
    if (v == dnnl_ABc8b8a2b) return "ABc8b8a2b";
    if (v == dnnl_ABcd8b8a2b) return "ABcd8b8a2b";
    if (v == dnnl_ABcde8b8a2b) return "ABcde8b8a2b";
    if (v == dnnl_AcB24a4b) return "AcB24a4b";
    if (v == dnnl_AcdB24a4b) return "AcdB24a4b";
    if (v == dnnl_AcdeB24a4b) return "AcdeB24a4b";
    if (v == dnnl_aBdC24b4c) return "aBdC24b4c";
    if (v == dnnl_aBdeC24b4c) return "aBdeC24b4c";
    if (v == dnnl_aBdefC24b4c) return "aBdefC24b4c";
    if (v == dnnl_AcB8a4b) return "AcB8a4b";
    if (v == dnnl_AcdB8a4b) return "AcdB8a4b";
    if (v == dnnl_AcdeB8a4b) return "AcdeB8a4b";
    if (v == dnnl_aBdC8b4c) return "aBdC8b4c";
    if (v == dnnl_aBdeC8b4c) return "aBdeC8b4c";
    if (v == dnnl_aBdefC8b4c) return "aBdefC8b4c";
    if (v == dnnl_Bca8b) return "Bca8b";
    if (v == dnnl_BcA8b2a) return "BcA8b2a";
    if (v == dnnl_Bcda8b) return "Bcda8b";
    if (v == dnnl_BcdA8b2a) return "BcdA8b2a";
    if (v == dnnl_Bcdea8b) return "Bcdea8b";
    if (v == dnnl_BcdeA8b2a) return "BcdeA8b2a";
    if (v == dnnl_aCdb8c) return "aCdb8c";
    if (v == dnnl_aCdB8c2b) return "aCdB8c2b";
    if (v == dnnl_aCdeb8c) return "aCdeb8c";
    if (v == dnnl_aCdeB8c2b) return "aCdeB8c2b";
    if (v == dnnl_aCdefb8c) return "aCdefb8c";
    if (v == dnnl_aCdefB8c2b) return "aCdefB8c2b";
    if (v == dnnl_Bca24b) return "Bca24b";
    if (v == dnnl_BcA24b2a) return "BcA24b2a";
    if (v == dnnl_Bcda24b) return "Bcda24b";
    if (v == dnnl_BcdA24b2a) return "BcdA24b2a";
    if (v == dnnl_Bcdea24b) return "Bcdea24b";
    if (v == dnnl_BcdeA24b2a) return "BcdeA24b2a";
    if (v == dnnl_aCdb24c) return "aCdb24c";
    if (v == dnnl_aCdB24c2b) return "aCdB24c2b";
    if (v == dnnl_aCdeb24c) return "aCdeb24c";
    if (v == dnnl_aCdeB24c2b) return "aCdeB24c2b";
    if (v == dnnl_aCdefb24c) return "aCdefb24c";
    if (v == dnnl_aCdefB24c2b) return "aCdefB24c2b";
    if (v == dnnl_BcA8b4a) return "BcA8b4a";
    if (v == dnnl_BcdA8b4a) return "BcdA8b4a";
    if (v == dnnl_BcdeA8b4a) return "BcdeA8b4a";
    if (v == dnnl_aCdB8c4b) return "aCdB8c4b";
    if (v == dnnl_aCdeB8c4b) return "aCdeB8c4b";
    if (v == dnnl_aCdefB8c4b) return "aCdefB8c4b";
    if (v == dnnl_BcA24b4a) return "BcA24b4a";
    if (v == dnnl_BcdA24b4a) return "BcdA24b4a";
    if (v == dnnl_BcdeA24b4a) return "BcdeA24b4a";
    if (v == dnnl_aCdB24c4b) return "aCdB24c4b";
    if (v == dnnl_aCdeB24c4b) return "aCdeB24c4b";
    if (v == dnnl_aCdefB24c4b) return "aCdefB24c4b";
    if (v == dnnl_AB16b48a) return "AB16b48a";
    if (v == dnnl_ABc16b48a) return "ABc16b48a";
    if (v == dnnl_ABcd16b48a) return "ABcd16b48a";
    if (v == dnnl_ABcde16b48a) return "ABcde16b48a";
    if (v == dnnl_ABc16a4b) return "ABc16a4b";
    if (v == dnnl_ABcd16a4b) return "ABcd16a4b";
    if (v == dnnl_ABcde16a4b) return "ABcde16a4b";
    if (v == dnnl_defcbA16a) return "defcbA16a";
    if (v == dnnl_defcbA8a) return "defcbA8a";
    if (v == dnnl_AcB16b64a) return "AcB16b64a";
    if (v == dnnl_AcdB16b64a) return "AcdB16b64a";
    if (v == dnnl_AcdeB16b64a) return "AcdeB16b64a";
    if (v == dnnl_AcB16b48a) return "AcB16b48a";
    if (v == dnnl_AcdB16b48a) return "AcdB16b48a";
    if (v == dnnl_AcdeB16b48a) return "AcdeB16b48a";
    if (v == dnnl_AcB16b32a) return "AcB16b32a";
    if (v == dnnl_AcdB16b32a) return "AcdB16b32a";
    if (v == dnnl_AcdeB16b32a) return "AcdeB16b32a";
    if (v == dnnl_AcB16b16a) return "AcB16b16a";
    if (v == dnnl_AcdB16b16a) return "AcdB16b16a";
    if (v == dnnl_AcdeB16b16a) return "AcdeB16b16a";
    if (v == dnnl_AcB8b32a) return "AcB8b32a";
    if (v == dnnl_AcdB8b32a) return "AcdB8b32a";
    if (v == dnnl_AcdeB8b32a) return "AcdeB8b32a";
    if (v == dnnl_AcB8b24a) return "AcB8b24a";
    if (v == dnnl_AcdB8b24a) return "AcdB8b24a";
    if (v == dnnl_AcdeB8b24a) return "AcdeB8b24a";
    if (v == dnnl_AcB8b16a) return "AcB8b16a";
    if (v == dnnl_AcdB8b16a) return "AcdB8b16a";
    if (v == dnnl_AcdeB8b16a) return "AcdeB8b16a";
    if (v == dnnl_AcB8b8a) return "AcB8b8a";
    if (v == dnnl_AcdB8b8a) return "AcdB8b8a";
    if (v == dnnl_AcdeB8b8a) return "AcdeB8b8a";
    if (v == dnnl_AcB8b64a2b) return "AcB8b64a2b";
    if (v == dnnl_AcdB8b64a2b) return "AcdB8b64a2b";
    if (v == dnnl_AcdeB8b64a2b) return "AcdeB8b64a2b";
    if (v == dnnl_AcB8b32a2b) return "AcB8b32a2b";
    if (v == dnnl_AcdB8b32a2b) return "AcdB8b32a2b";
    if (v == dnnl_AcdeB8b32a2b) return "AcdeB8b32a2b";
    if (v == dnnl_AcB8b24a2b) return "AcB8b24a2b";
    if (v == dnnl_AcdB8b24a2b) return "AcdB8b24a2b";
    if (v == dnnl_AcdeB8b24a2b) return "AcdeB8b24a2b";
    if (v == dnnl_AcB8b16a2b) return "AcB8b16a2b";
    if (v == dnnl_AcdB8b16a2b) return "AcdB8b16a2b";
    if (v == dnnl_AcdeB8b16a2b) return "AcdeB8b16a2b";
    if (v == dnnl_AcB8b8a2b) return "AcB8b8a2b";
    if (v == dnnl_AcdB8b8a2b) return "AcdB8b8a2b";
    if (v == dnnl_AcdeB8b8a2b) return "AcdeB8b8a2b";
    if (v == dnnl_AcB4b64a4b) return "AcB4b64a4b";
    if (v == dnnl_AcdB4b64a4b) return "AcdB4b64a4b";
    if (v == dnnl_AcdeB4b64a4b) return "AcdeB4b64a4b";
    if (v == dnnl_AcB4b32a4b) return "AcB4b32a4b";
    if (v == dnnl_AcdB4b32a4b) return "AcdB4b32a4b";
    if (v == dnnl_AcdeB4b32a4b) return "AcdeB4b32a4b";
    if (v == dnnl_AcB4b24a4b) return "AcB4b24a4b";
    if (v == dnnl_AcdB4b24a4b) return "AcdB4b24a4b";
    if (v == dnnl_AcdeB4b24a4b) return "AcdeB4b24a4b";
    if (v == dnnl_AcB4b16a4b) return "AcB4b16a4b";
    if (v == dnnl_AcdB4b16a4b) return "AcdB4b16a4b";
    if (v == dnnl_AcdeB4b16a4b) return "AcdeB4b16a4b";
    if (v == dnnl_AcB4b8a4b) return "AcB4b8a4b";
    if (v == dnnl_AcdB4b8a4b) return "AcdB4b8a4b";
    if (v == dnnl_AcdeB4b8a4b) return "AcdeB4b8a4b";
    if (v == dnnl_Ab4a) return "Ab4a";
    if (v == dnnl_Ab8a) return "Ab8a";
    if (v == dnnl_BA4b4a) return "BA4b4a";
    if (v == dnnl_BA8b4a) return "BA8b4a";
    if (v == dnnl_BA2a24b) return "BA2a24b";
    if (v == dnnl_aCB2b24c) return "aCB2b24c";
    if (v == dnnl_BA2a8b) return "BA2a8b";
    if (v == dnnl_aCB2b8c) return "aCB2b8c";
    if (v == dnnl_BA8a24b) return "BA8a24b";
    if (v == dnnl_aCB8b24c) return "aCB8b24c";
    if (v == dnnl_BA8a16b) return "BA8a16b";
    if (v == dnnl_aCB8b16c) return "aCB8b16c";
    if (v == dnnl_BA8a8b) return "BA8a8b";
    if (v == dnnl_aCB8b8c) return "aCB8b8c";
    if (v == dnnl_bcad) return "bcad";
    if (v == dnnl_cabd) return "cabd";
    if (v == dnnl_dabc) return "dabc";
    if (v == dnnl_Ab32a) return "Ab32a";
    if (v == dnnl_aCBd8b8c) return "aCBd8b8c";
    if (v == dnnl_aCBde8b8c) return "aCBde8b8c";
    if (v == dnnl_BAc8a8b) return "BAc8a8b";
    if (v == dnnl_BAcd8a8b) return "BAcd8a8b";
    if (v == dnnl_BAcde8a8b) return "BAcde8a8b";
    if (v == dnnl_aCBdef8b8c) return "aCBdef8b8c";
    if (v == dnnl_format_tag_last) return "format_tag_last";
    if (v == dnnl_x) return "x";
    if (v == dnnl_nc) return "nc";
    if (v == dnnl_cn) return "cn";
    if (v == dnnl_tn) return "tn";
    if (v == dnnl_nt) return "nt";
    if (v == dnnl_ncw) return "ncw";
    if (v == dnnl_nwc) return "nwc";
    if (v == dnnl_nchw) return "nchw";
    if (v == dnnl_nhwc) return "nhwc";
    if (v == dnnl_chwn) return "chwn";
    if (v == dnnl_ncdhw) return "ncdhw";
    if (v == dnnl_ndhwc) return "ndhwc";
    if (v == dnnl_oi) return "oi";
    if (v == dnnl_io) return "io";
    if (v == dnnl_oiw) return "oiw";
    if (v == dnnl_owi) return "owi";
    if (v == dnnl_wio) return "wio";
    if (v == dnnl_woi) return "woi";
    if (v == dnnl_iwo) return "iwo";
    if (v == dnnl_oihw) return "oihw";
    if (v == dnnl_hwio) return "hwio";
    if (v == dnnl_hwoi) return "hwoi";
    if (v == dnnl_ohwi) return "ohwi";
    if (v == dnnl_ihwo) return "ihwo";
    if (v == dnnl_iohw) return "iohw";
    if (v == dnnl_oidhw) return "oidhw";
    if (v == dnnl_iodhw) return "iodhw";
    if (v == dnnl_dhwio) return "dhwio";
    if (v == dnnl_dhwoi) return "dhwoi";
    if (v == dnnl_odhwi) return "odhwi";
    if (v == dnnl_idhwo) return "idhwo";
    if (v == dnnl_goiw) return "goiw";
    if (v == dnnl_gowi) return "gowi";
    if (v == dnnl_wigo) return "wigo";
    if (v == dnnl_goihw) return "goihw";
    if (v == dnnl_gohwi) return "gohwi";
    if (v == dnnl_hwigo) return "hwigo";
    if (v == dnnl_giohw) return "giohw";
    if (v == dnnl_goidhw) return "goidhw";
    if (v == dnnl_godhwi) return "godhwi";
    if (v == dnnl_giodhw) return "giodhw";
    if (v == dnnl_dhwigo) return "dhwigo";
    if (v == dnnl_tnc) return "tnc";
    if (v == dnnl_ntc) return "ntc";
    if (v == dnnl_ldnc) return "ldnc";
    if (v == dnnl_ldigo) return "ldigo";
    if (v == dnnl_ldgoi) return "ldgoi";
    if (v == dnnl_ldio) return "ldio";
    if (v == dnnl_ldoi) return "ldoi";
    if (v == dnnl_ldgo) return "ldgo";
    if (v == dnnl_ldOi16o) return "ldOi16o";
    if (v == dnnl_ldOi32o) return "ldOi32o";
    if (v == dnnl_ldOI16o4i) return "ldOI16o4i";
    if (v == dnnl_ldOI32o4i) return "ldOI32o4i";
    if (v == dnnl_ldIo32i) return "ldIo32i";
    if (v == dnnl_ldgOi16o) return "ldgOi16o";
    if (v == dnnl_ldgOi32o) return "ldgOi32o";
    if (v == dnnl_ldgOI16o4i) return "ldgOI16o4i";
    if (v == dnnl_ldgOI32o2i) return "ldgOI32o2i";
    if (v == dnnl_ldgOI32o4i) return "ldgOI32o4i";
    if (v == dnnl_ldgOI64o2i) return "ldgOI64o2i";
    if (v == dnnl_ldgOI64o4i) return "ldgOI64o4i";
    if (v == dnnl_ldgIo16i) return "ldgIo16i";
    if (v == dnnl_ldgIo32i) return "ldgIo32i";
    if (v == dnnl_ldgIO32i2o) return "ldgIO32i2o";
    if (v == dnnl_nCdhw32c) return "nCdhw32c";
    if (v == dnnl_nCdhw16c) return "nCdhw16c";
    if (v == dnnl_nCdhw4c) return "nCdhw4c";
    if (v == dnnl_nCdhw8c) return "nCdhw8c";
    if (v == dnnl_nChw32c) return "nChw32c";
    if (v == dnnl_nChw16c) return "nChw16c";
    if (v == dnnl_nChw4c) return "nChw4c";
    if (v == dnnl_nChw8c) return "nChw8c";
    if (v == dnnl_nCw32c) return "nCw32c";
    if (v == dnnl_nCw16c) return "nCw16c";
    if (v == dnnl_nCw4c) return "nCw4c";
    if (v == dnnl_nCw8c) return "nCw8c";
    if (v == dnnl_NCw16n16c) return "NCw16n16c";
    if (v == dnnl_NCdhw16n16c) return "NCdhw16n16c";
    if (v == dnnl_NChw16n16c) return "NChw16n16c";
    if (v == dnnl_NCw32n16c) return "NCw32n16c";
    if (v == dnnl_NChw32n16c) return "NChw32n16c";
    if (v == dnnl_NChw16n32c) return "NChw16n32c";
    if (v == dnnl_NCdhw32n16c) return "NCdhw32n16c";
    if (v == dnnl_NCw32n32c) return "NCw32n32c";
    if (v == dnnl_NChw32n32c) return "NChw32n32c";
    if (v == dnnl_NCdhw32n32c) return "NCdhw32n32c";
    if (v == dnnl_OI16i16o) return "OI16i16o";
    if (v == dnnl_OI16i32o) return "OI16i32o";
    if (v == dnnl_OI16i48o) return "OI16i48o";
    if (v == dnnl_OI16i64o) return "OI16i64o";
    if (v == dnnl_OI8i8o2i) return "OI8i8o2i";
    if (v == dnnl_OI8i16o2i) return "OI8i16o2i";
    if (v == dnnl_OI8i24o2i) return "OI8i24o2i";
    if (v == dnnl_OI8i32o2i) return "OI8i32o2i";
    if (v == dnnl_OI8i64o2i) return "OI8i64o2i";
    if (v == dnnl_OI4i8o4i) return "OI4i8o4i";
    if (v == dnnl_OI4i16o4i) return "OI4i16o4i";
    if (v == dnnl_OI4i24o4i) return "OI4i24o4i";
    if (v == dnnl_OI4i32o4i) return "OI4i32o4i";
    if (v == dnnl_OI4i64o4i) return "OI4i64o4i";
    if (v == dnnl_OI16i16o4i) return "OI16i16o4i";
    if (v == dnnl_OI8i32o) return "OI8i32o";
    if (v == dnnl_OI8i24o) return "OI8i24o";
    if (v == dnnl_OI8i16o) return "OI8i16o";
    if (v == dnnl_OI8i8o) return "OI8i8o";
    if (v == dnnl_IOw8o8i) return "IOw8o8i";
    if (v == dnnl_IOw16o16i) return "IOw16o16i";
    if (v == dnnl_IOw16i16o) return "IOw16i16o";
    if (v == dnnl_OIw16i16o) return "OIw16i16o";
    if (v == dnnl_OwI16i16o) return "OwI16i16o";
    if (v == dnnl_OIw16i32o) return "OIw16i32o";
    if (v == dnnl_OwI16i32o) return "OwI16i32o";
    if (v == dnnl_OIw16i48o) return "OIw16i48o";
    if (v == dnnl_OwI16i48o) return "OwI16i48o";
    if (v == dnnl_OIw16i64o) return "OIw16i64o";
    if (v == dnnl_OwI16i64o) return "OwI16i64o";
    if (v == dnnl_OIw16o16i) return "OIw16o16i";
    if (v == dnnl_Oiw16o) return "Oiw16o";
    if (v == dnnl_OIw4i8o4i) return "OIw4i8o4i";
    if (v == dnnl_OwI4i8o4i) return "OwI4i8o4i";
    if (v == dnnl_OIw4i16o4i) return "OIw4i16o4i";
    if (v == dnnl_OwI4i16o4i) return "OwI4i16o4i";
    if (v == dnnl_OIw4i24o4i) return "OIw4i24o4i";
    if (v == dnnl_OwI4i24o4i) return "OwI4i24o4i";
    if (v == dnnl_OIw4i32o4i) return "OIw4i32o4i";
    if (v == dnnl_OwI4i32o4i) return "OwI4i32o4i";
    if (v == dnnl_OIw4i64o4i) return "OIw4i64o4i";
    if (v == dnnl_OwI4i64o4i) return "OwI4i64o4i";
    if (v == dnnl_OIw2i8o4i) return "OIw2i8o4i";
    if (v == dnnl_OIw16i16o4i) return "OIw16i16o4i";
    if (v == dnnl_OIw16i16o2i) return "OIw16i16o2i";
    if (v == dnnl_OIw16o16i2o) return "OIw16o16i2o";
    if (v == dnnl_OIw4i4o) return "OIw4i4o";
    if (v == dnnl_OIw4o4i) return "OIw4o4i";
    if (v == dnnl_Oiw4o) return "Oiw4o";
    if (v == dnnl_OIw8i8o2i) return "OIw8i8o2i";
    if (v == dnnl_OwI8i8o2i) return "OwI8i8o2i";
    if (v == dnnl_OIw8i16o2i) return "OIw8i16o2i";
    if (v == dnnl_OwI8i16o2i) return "OwI8i16o2i";
    if (v == dnnl_OIw8i24o2i) return "OIw8i24o2i";
    if (v == dnnl_OwI8i24o2i) return "OwI8i24o2i";
    if (v == dnnl_OIw8i32o2i) return "OIw8i32o2i";
    if (v == dnnl_OwI8i32o2i) return "OwI8i32o2i";
    if (v == dnnl_OIw8i64o2i) return "OIw8i64o2i";
    if (v == dnnl_OwI8i64o2i) return "OwI8i64o2i";
    if (v == dnnl_OIw8i8o) return "OIw8i8o";
    if (v == dnnl_OwI8i8o) return "OwI8i8o";
    if (v == dnnl_OIw8o16i2o) return "OIw8o16i2o";
    if (v == dnnl_IOw8o16i2o) return "IOw8o16i2o";
    if (v == dnnl_OIw8o8i) return "OIw8o8i";
    if (v == dnnl_OIw8o4i) return "OIw8o4i";
    if (v == dnnl_Owi16o) return "Owi16o";
    if (v == dnnl_OwI16o2i) return "OwI16o2i";
    if (v == dnnl_OwI16o4i) return "OwI16o4i";
    if (v == dnnl_Iwo8i) return "Iwo8i";
    if (v == dnnl_IwO8i2o) return "IwO8i2o";
    if (v == dnnl_IwO8i4o) return "IwO8i4o";
    if (v == dnnl_Iwo16i) return "Iwo16i";
    if (v == dnnl_IwO16i2o) return "IwO16i2o";
    if (v == dnnl_IwO16i4o) return "IwO16i4o";
    if (v == dnnl_Iwo24i) return "Iwo24i";
    if (v == dnnl_IwO24i2o) return "IwO24i2o";
    if (v == dnnl_IwO24i4o) return "IwO24i4o";
    if (v == dnnl_Owi4o) return "Owi4o";
    if (v == dnnl_Owi8o) return "Owi8o";
    if (v == dnnl_OwI8o2i) return "OwI8o2i";
    if (v == dnnl_OIw8i32o) return "OIw8i32o";
    if (v == dnnl_OwI8i32o) return "OwI8i32o";
    if (v == dnnl_OIw8i24o) return "OIw8i24o";
    if (v == dnnl_OwI8i24o) return "OwI8i24o";
    if (v == dnnl_OIw8i16o) return "OIw8i16o";
    if (v == dnnl_OwI8i16o) return "OwI8i16o";
    if (v == dnnl_OwI8o4i) return "OwI8o4i";
    if (v == dnnl_IOhw16i16o) return "IOhw16i16o";
    if (v == dnnl_IOhw8o8i) return "IOhw8o8i";
    if (v == dnnl_IOhw16o16i) return "IOhw16o16i";
    if (v == dnnl_Ohwi16o) return "Ohwi16o";
    if (v == dnnl_OhwI16o2i) return "OhwI16o2i";
    if (v == dnnl_OhwI16o4i) return "OhwI16o4i";
    if (v == dnnl_Ihwo8i) return "Ihwo8i";
    if (v == dnnl_IhwO8i2o) return "IhwO8i2o";
    if (v == dnnl_IhwO8i4o) return "IhwO8i4o";
    if (v == dnnl_Ihwo16i) return "Ihwo16i";
    if (v == dnnl_IhwO16i2o) return "IhwO16i2o";
    if (v == dnnl_IhwO16i4o) return "IhwO16i4o";
    if (v == dnnl_Ihwo24i) return "Ihwo24i";
    if (v == dnnl_IhwO24i2o) return "IhwO24i2o";
    if (v == dnnl_IhwO24i4o) return "IhwO24i4o";
    if (v == dnnl_Ohwi24o) return "Ohwi24o";
    if (v == dnnl_Ohwi32o) return "Ohwi32o";
    if (v == dnnl_Ohwi4o) return "Ohwi4o";
    if (v == dnnl_Ohwi8o) return "Ohwi8o";
    if (v == dnnl_OhwI8o2i) return "OhwI8o2i";
    if (v == dnnl_OhwI8o4i) return "OhwI8o4i";
    if (v == dnnl_OIhw16i16o) return "OIhw16i16o";
    if (v == dnnl_OhwI16i16o) return "OhwI16i16o";
    if (v == dnnl_OIhw16i32o) return "OIhw16i32o";
    if (v == dnnl_OhwI16i32o) return "OhwI16i32o";
    if (v == dnnl_OIhw16i48o) return "OIhw16i48o";
    if (v == dnnl_OhwI16i48o) return "OhwI16i48o";
    if (v == dnnl_OIhw16i64o) return "OIhw16i64o";
    if (v == dnnl_OhwI16i64o) return "OhwI16i64o";
    if (v == dnnl_OIhw16o16i) return "OIhw16o16i";
    if (v == dnnl_Oihw16o) return "Oihw16o";
    if (v == dnnl_OIhw4i8o4i) return "OIhw4i8o4i";
    if (v == dnnl_OhwI4i8o4i) return "OhwI4i8o4i";
    if (v == dnnl_OIhw4i16o4i) return "OIhw4i16o4i";
    if (v == dnnl_OhwI4i16o4i) return "OhwI4i16o4i";
    if (v == dnnl_OIhw4i24o4i) return "OIhw4i24o4i";
    if (v == dnnl_OhwI4i24o4i) return "OhwI4i24o4i";
    if (v == dnnl_OIhw4i32o4i) return "OIhw4i32o4i";
    if (v == dnnl_OhwI4i32o4i) return "OhwI4i32o4i";
    if (v == dnnl_OIhw4i64o4i) return "OIhw4i64o4i";
    if (v == dnnl_OhwI4i64o4i) return "OhwI4i64o4i";
    if (v == dnnl_OIhw16i16o4i) return "OIhw16i16o4i";
    if (v == dnnl_OIhw16i16o2i) return "OIhw16i16o2i";
    if (v == dnnl_OIhw16o16i2o) return "OIhw16o16i2o";
    if (v == dnnl_OIhw4i4o) return "OIhw4i4o";
    if (v == dnnl_OIhw4o4i) return "OIhw4o4i";
    if (v == dnnl_Oihw4o) return "Oihw4o";
    if (v == dnnl_OIhw8i8o2i) return "OIhw8i8o2i";
    if (v == dnnl_OhwI8i8o2i) return "OhwI8i8o2i";
    if (v == dnnl_OIhw8i16o2i) return "OIhw8i16o2i";
    if (v == dnnl_OhwI8i16o2i) return "OhwI8i16o2i";
    if (v == dnnl_OIhw8i32o2i) return "OIhw8i32o2i";
    if (v == dnnl_OhwI8i32o2i) return "OhwI8i32o2i";
    if (v == dnnl_OIhw8i24o2i) return "OIhw8i24o2i";
    if (v == dnnl_OhwI8i24o2i) return "OhwI8i24o2i";
    if (v == dnnl_OIhw8i64o2i) return "OIhw8i64o2i";
    if (v == dnnl_OhwI8i64o2i) return "OhwI8i64o2i";
    if (v == dnnl_OIhw8i8o) return "OIhw8i8o";
    if (v == dnnl_OhwI8i8o) return "OhwI8i8o";
    if (v == dnnl_OIhw8o16i2o) return "OIhw8o16i2o";
    if (v == dnnl_OIhw2i8o4i) return "OIhw2i8o4i";
    if (v == dnnl_IOhw8o16i2o) return "IOhw8o16i2o";
    if (v == dnnl_OIhw8o8i) return "OIhw8o8i";
    if (v == dnnl_OIhw8o4i) return "OIhw8o4i";
    if (v == dnnl_Owhi16o) return "Owhi16o";
    if (v == dnnl_OIhw8i32o) return "OIhw8i32o";
    if (v == dnnl_OhwI8i32o) return "OhwI8i32o";
    if (v == dnnl_OIhw8i24o) return "OIhw8i24o";
    if (v == dnnl_OhwI8i24o) return "OhwI8i24o";
    if (v == dnnl_OIhw8i16o) return "OIhw8i16o";
    if (v == dnnl_OhwI8i16o) return "OhwI8i16o";
    if (v == dnnl_Odhwi16o) return "Odhwi16o";
    if (v == dnnl_OdhwI16o2i) return "OdhwI16o2i";
    if (v == dnnl_OdhwI16o4i) return "OdhwI16o4i";
    if (v == dnnl_Idhwo8i) return "Idhwo8i";
    if (v == dnnl_IdhwO8i2o) return "IdhwO8i2o";
    if (v == dnnl_IdhwO8i4o) return "IdhwO8i4o";
    if (v == dnnl_Idhwo16i) return "Idhwo16i";
    if (v == dnnl_IdhwO16i2o) return "IdhwO16i2o";
    if (v == dnnl_IdhwO16i4o) return "IdhwO16i4o";
    if (v == dnnl_Idhwo24i) return "Idhwo24i";
    if (v == dnnl_IdhwO24i2o) return "IdhwO24i2o";
    if (v == dnnl_IdhwO24i4o) return "IdhwO24i4o";
    if (v == dnnl_Odhwi4o) return "Odhwi4o";
    if (v == dnnl_Odhwi8o) return "Odhwi8o";
    if (v == dnnl_OdhwI8o2i) return "OdhwI8o2i";
    if (v == dnnl_OdhwI8o4i) return "OdhwI8o4i";
    if (v == dnnl_Odwhi16o) return "Odwhi16o";
    if (v == dnnl_OIdhw16i16o) return "OIdhw16i16o";
    if (v == dnnl_OdhwI16i16o) return "OdhwI16i16o";
    if (v == dnnl_OIdhw16i32o) return "OIdhw16i32o";
    if (v == dnnl_OdhwI16i32o) return "OdhwI16i32o";
    if (v == dnnl_OIdhw16i48o) return "OIdhw16i48o";
    if (v == dnnl_OdhwI16i48o) return "OdhwI16i48o";
    if (v == dnnl_OIdhw16i64o) return "OIdhw16i64o";
    if (v == dnnl_OdhwI16i64o) return "OdhwI16i64o";
    if (v == dnnl_OIdhw16o16i) return "OIdhw16o16i";
    if (v == dnnl_Oidhw16o) return "Oidhw16o";
    if (v == dnnl_OIdhw4i4o) return "OIdhw4i4o";
    if (v == dnnl_OIdhw4o4i) return "OIdhw4o4i";
    if (v == dnnl_Oidhw4o) return "Oidhw4o";
    if (v == dnnl_OIdhw8i8o2i) return "OIdhw8i8o2i";
    if (v == dnnl_OdhwI8i8o2i) return "OdhwI8i8o2i";
    if (v == dnnl_OIdhw8i16o2i) return "OIdhw8i16o2i";
    if (v == dnnl_OdhwI8i16o2i) return "OdhwI8i16o2i";
    if (v == dnnl_OIdhw8i32o2i) return "OIdhw8i32o2i";
    if (v == dnnl_OdhwI8i32o2i) return "OdhwI8i32o2i";
    if (v == dnnl_OIdhw8i24o2i) return "OIdhw8i24o2i";
    if (v == dnnl_OdhwI8i24o2i) return "OdhwI8i24o2i";
    if (v == dnnl_OIdhw8i64o2i) return "OIdhw8i64o2i";
    if (v == dnnl_OdhwI8i64o2i) return "OdhwI8i64o2i";
    if (v == dnnl_OIdhw8i8o) return "OIdhw8i8o";
    if (v == dnnl_OdhwI8i8o) return "OdhwI8i8o";
    if (v == dnnl_OIdhw8o16i2o) return "OIdhw8o16i2o";
    if (v == dnnl_IOdhw8o16i2o) return "IOdhw8o16i2o";
    if (v == dnnl_OIdhw4i8o4i) return "OIdhw4i8o4i";
    if (v == dnnl_OdhwI4i8o4i) return "OdhwI4i8o4i";
    if (v == dnnl_OIdhw4i16o4i) return "OIdhw4i16o4i";
    if (v == dnnl_OdhwI4i16o4i) return "OdhwI4i16o4i";
    if (v == dnnl_OIdhw4i24o4i) return "OIdhw4i24o4i";
    if (v == dnnl_OdhwI4i24o4i) return "OdhwI4i24o4i";
    if (v == dnnl_OIdhw4i32o4i) return "OIdhw4i32o4i";
    if (v == dnnl_OdhwI4i32o4i) return "OdhwI4i32o4i";
    if (v == dnnl_OIdhw4i64o4i) return "OIdhw4i64o4i";
    if (v == dnnl_OdhwI4i64o4i) return "OdhwI4i64o4i";
    if (v == dnnl_OIdhw16i16o4i) return "OIdhw16i16o4i";
    if (v == dnnl_OIdhw16i16o2i) return "OIdhw16i16o2i";
    if (v == dnnl_OIdhw2i8o4i) return "OIdhw2i8o4i";
    if (v == dnnl_OIdhw8o8i) return "OIdhw8o8i";
    if (v == dnnl_OIdhw8o4i) return "OIdhw8o4i";
    if (v == dnnl_IOdhw16i16o) return "IOdhw16i16o";
    if (v == dnnl_OIdhw4o8i8o4i) return "OIdhw4o8i8o4i";
    if (v == dnnl_IOdhw8o8i) return "IOdhw8o8i";
    if (v == dnnl_IOdhw16o16i) return "IOdhw16o16i";
    if (v == dnnl_OIdhw16o16i2o) return "OIdhw16o16i2o";
    if (v == dnnl_OIdhw8i32o) return "OIdhw8i32o";
    if (v == dnnl_OdhwI8i32o) return "OdhwI8i32o";
    if (v == dnnl_OIdhw8i24o) return "OIdhw8i24o";
    if (v == dnnl_OdhwI8i24o) return "OdhwI8i24o";
    if (v == dnnl_OIdhw8i16o) return "OIdhw8i16o";
    if (v == dnnl_OdhwI8i16o) return "OdhwI8i16o";
    if (v == dnnl_Goiw16g) return "Goiw16g";
    if (v == dnnl_Goiw8g) return "Goiw8g";
    if (v == dnnl_Goiw4g) return "Goiw4g";
    if (v == dnnl_gIOw8o8i) return "gIOw8o8i";
    if (v == dnnl_gIOw16o16i) return "gIOw16o16i";
    if (v == dnnl_gIOw16i16o) return "gIOw16i16o";
    if (v == dnnl_gOIw16i16o) return "gOIw16i16o";
    if (v == dnnl_gOIw16o16i) return "gOIw16o16i";
    if (v == dnnl_gOiw16o) return "gOiw16o";
    if (v == dnnl_gOIw4i16o4i) return "gOIw4i16o4i";
    if (v == dnnl_gOIw2i8o4i) return "gOIw2i8o4i";
    if (v == dnnl_gOIw16i16o4i) return "gOIw16i16o4i";
    if (v == dnnl_gOIw16i16o2i) return "gOIw16i16o2i";
    if (v == dnnl_gOIw16o16i2o) return "gOIw16o16i2o";
    if (v == dnnl_gOIw4i4o) return "gOIw4i4o";
    if (v == dnnl_gOIw4o4i) return "gOIw4o4i";
    if (v == dnnl_gOiw4o) return "gOiw4o";
    if (v == dnnl_gOIw8i16o2i) return "gOIw8i16o2i";
    if (v == dnnl_gOIw8i8o) return "gOIw8i8o";
    if (v == dnnl_gOIw8o16i2o) return "gOIw8o16i2o";
    if (v == dnnl_gIOw8o16i2o) return "gIOw8o16i2o";
    if (v == dnnl_gOIw8o8i) return "gOIw8o8i";
    if (v == dnnl_gOIw8o4i) return "gOIw8o4i";
    if (v == dnnl_gOwi16o) return "gOwi16o";
    if (v == dnnl_gOwI16o2i) return "gOwI16o2i";
    if (v == dnnl_gOwI16o4i) return "gOwI16o4i";
    if (v == dnnl_gIwo8i) return "gIwo8i";
    if (v == dnnl_gIwO8i2o) return "gIwO8i2o";
    if (v == dnnl_gIwO8i4o) return "gIwO8i4o";
    if (v == dnnl_gIwo16i) return "gIwo16i";
    if (v == dnnl_gIwO16i2o) return "gIwO16i2o";
    if (v == dnnl_gIwO16i4o) return "gIwO16i4o";
    if (v == dnnl_gIwo24i) return "gIwo24i";
    if (v == dnnl_gIwO24i2o) return "gIwO24i2o";
    if (v == dnnl_gIwO24i4o) return "gIwO24i4o";
    if (v == dnnl_gOwi4o) return "gOwi4o";
    if (v == dnnl_gOwi8o) return "gOwi8o";
    if (v == dnnl_gOwI8o2i) return "gOwI8o2i";
    if (v == dnnl_gOwI8o4i) return "gOwI8o4i";
    if (v == dnnl_Goiw32g) return "Goiw32g";
    if (v == dnnl_gOIw2i4o2i) return "gOIw2i4o2i";
    if (v == dnnl_gOIw2o4i2o) return "gOIw2o4i2o";
    if (v == dnnl_gOIw4i8o2i) return "gOIw4i8o2i";
    if (v == dnnl_gOIw4o8i2o) return "gOIw4o8i2o";
    if (v == dnnl_goIw4i) return "goIw4i";
    if (v == dnnl_goIw32i) return "goIw32i";
    if (v == dnnl_gIOhw16i16o) return "gIOhw16i16o";
    if (v == dnnl_gIOhw8o8i) return "gIOhw8o8i";
    if (v == dnnl_gIOhw16o16i) return "gIOhw16o16i";
    if (v == dnnl_gOhwi16o) return "gOhwi16o";
    if (v == dnnl_gOhwI16o2i) return "gOhwI16o2i";
    if (v == dnnl_gOhwI16o4i) return "gOhwI16o4i";
    if (v == dnnl_gIhwo8i) return "gIhwo8i";
    if (v == dnnl_gIhwO8i2o) return "gIhwO8i2o";
    if (v == dnnl_gIhwO8i4o) return "gIhwO8i4o";
    if (v == dnnl_gIhwo16i) return "gIhwo16i";
    if (v == dnnl_gIhwO16i2o) return "gIhwO16i2o";
    if (v == dnnl_gIhwO16i4o) return "gIhwO16i4o";
    if (v == dnnl_gIhwo24i) return "gIhwo24i";
    if (v == dnnl_gIhwO24i2o) return "gIhwO24i2o";
    if (v == dnnl_gIhwO24i4o) return "gIhwO24i4o";
    if (v == dnnl_gOhwi32o) return "gOhwi32o";
    if (v == dnnl_gOhwi24o) return "gOhwi24o";
    if (v == dnnl_gOhwI24o2i) return "gOhwI24o2i";
    if (v == dnnl_gOhwI24o4i) return "gOhwI24o4i";
    if (v == dnnl_gOhwi4o) return "gOhwi4o";
    if (v == dnnl_gOhwi8o) return "gOhwi8o";
    if (v == dnnl_gOhwI8o2i) return "gOhwI8o2i";
    if (v == dnnl_gOhwI8o4i) return "gOhwI8o4i";
    if (v == dnnl_Goihw16g) return "Goihw16g";
    if (v == dnnl_gOIhw16i16o) return "gOIhw16i16o";
    if (v == dnnl_gOIhw16o16i) return "gOIhw16o16i";
    if (v == dnnl_gOihw16o) return "gOihw16o";
    if (v == dnnl_gOIhw2i8o4i) return "gOIhw2i8o4i";
    if (v == dnnl_gOIhw4i16o4i) return "gOIhw4i16o4i";
    if (v == dnnl_gOIhw16i16o4i) return "gOIhw16i16o4i";
    if (v == dnnl_gOIhw16i16o2i) return "gOIhw16i16o2i";
    if (v == dnnl_gOIhw16o16i2o) return "gOIhw16o16i2o";
    if (v == dnnl_gOIhw4i4o) return "gOIhw4i4o";
    if (v == dnnl_gOIhw4o4i) return "gOIhw4o4i";
    if (v == dnnl_gOihw4o) return "gOihw4o";
    if (v == dnnl_Goihw8g) return "Goihw8g";
    if (v == dnnl_Goihw4g) return "Goihw4g";
    if (v == dnnl_gOIhw8i16o2i) return "gOIhw8i16o2i";
    if (v == dnnl_gOIhw8i8o) return "gOIhw8i8o";
    if (v == dnnl_gOIhw8o16i2o) return "gOIhw8o16i2o";
    if (v == dnnl_gIOhw8o16i2o) return "gIOhw8o16i2o";
    if (v == dnnl_gOIhw8o8i) return "gOIhw8o8i";
    if (v == dnnl_gOIhw8o4i) return "gOIhw8o4i";
    if (v == dnnl_Goihw32g) return "Goihw32g";
    if (v == dnnl_gOwhi16o) return "gOwhi16o";
    if (v == dnnl_goIhw4i) return "goIhw4i";
    if (v == dnnl_goIhw32i) return "goIhw32i";
    if (v == dnnl_OIw4o8i8o4i) return "OIw4o8i8o4i";
    if (v == dnnl_OIhw4o8i8o4i) return "OIhw4o8i8o4i";
    if (v == dnnl_IOw4i8o8i4o) return "IOw4i8o8i4o";
    if (v == dnnl_IOhw4i8o8i4o) return "IOhw4i8o8i4o";
    if (v == dnnl_IOdhw4i8o8i4o) return "IOdhw4i8o8i4o";
    if (v == dnnl_OIhw2o8i8o2i) return "OIhw2o8i8o2i";
    if (v == dnnl_gOIw4o8i8o4i) return "gOIw4o8i8o4i";
    if (v == dnnl_gOIhw4o8i8o4i) return "gOIhw4o8i8o4i";
    if (v == dnnl_gOIdhw4o8i8o4i) return "gOIdhw4o8i8o4i";
    if (v == dnnl_gIOw4i8o8i4o) return "gIOw4i8o8i4o";
    if (v == dnnl_gIOhw4i8o8i4o) return "gIOhw4i8o8i4o";
    if (v == dnnl_gIOdhw4i8o8i4o) return "gIOdhw4i8o8i4o";
    if (v == dnnl_gOIhw2o8i8o2i) return "gOIhw2o8i8o2i";
    if (v == dnnl_gOIhw2i4o2i) return "gOIhw2i4o2i";
    if (v == dnnl_gOIhw2o4i2o) return "gOIhw2o4i2o";
    if (v == dnnl_gOIhw4i8o2i) return "gOIhw4i8o2i";
    if (v == dnnl_gOIhw4o8i2o) return "gOIhw4o8i2o";
    if (v == dnnl_gIOdhw16i16o) return "gIOdhw16i16o";
    if (v == dnnl_gIOdhw8o8i) return "gIOdhw8o8i";
    if (v == dnnl_gIOdhw16o16i) return "gIOdhw16o16i";
    if (v == dnnl_gOdhwi16o) return "gOdhwi16o";
    if (v == dnnl_gOdhwI16o2i) return "gOdhwI16o2i";
    if (v == dnnl_gOdhwI16o4i) return "gOdhwI16o4i";
    if (v == dnnl_gIdhwo8i) return "gIdhwo8i";
    if (v == dnnl_gIdhwO8i2o) return "gIdhwO8i2o";
    if (v == dnnl_gIdhwO8i4o) return "gIdhwO8i4o";
    if (v == dnnl_gIdhwo16i) return "gIdhwo16i";
    if (v == dnnl_gIdhwO16i2o) return "gIdhwO16i2o";
    if (v == dnnl_gIdhwO16i4o) return "gIdhwO16i4o";
    if (v == dnnl_gIdhwo24i) return "gIdhwo24i";
    if (v == dnnl_gIdhwO24i2o) return "gIdhwO24i2o";
    if (v == dnnl_gIdhwO24i4o) return "gIdhwO24i4o";
    if (v == dnnl_gOdhwi4o) return "gOdhwi4o";
    if (v == dnnl_gOdhwi8o) return "gOdhwi8o";
    if (v == dnnl_gOdhwI8o2i) return "gOdhwI8o2i";
    if (v == dnnl_gOdhwI8o4i) return "gOdhwI8o4i";
    if (v == dnnl_gOdwhi16o) return "gOdwhi16o";
    if (v == dnnl_gOIdhw16i16o) return "gOIdhw16i16o";
    if (v == dnnl_gOIdhw4i16o4i) return "gOIdhw4i16o4i";
    if (v == dnnl_gOIdhw16i16o4i) return "gOIdhw16i16o4i";
    if (v == dnnl_gOIdhw2i8o4i) return "gOIdhw2i8o4i";
    if (v == dnnl_gOIdhw16i16o2i) return "gOIdhw16i16o2i";
    if (v == dnnl_gOIdhw16o16i) return "gOIdhw16o16i";
    if (v == dnnl_gOIdhw16o16i2o) return "gOIdhw16o16i2o";
    if (v == dnnl_gOidhw16o) return "gOidhw16o";
    if (v == dnnl_gOIdhw4i4o) return "gOIdhw4i4o";
    if (v == dnnl_gOIdhw4o4i) return "gOIdhw4o4i";
    if (v == dnnl_gOidhw4o) return "gOidhw4o";
    if (v == dnnl_gOIdhw8i16o2i) return "gOIdhw8i16o2i";
    if (v == dnnl_gOIdhw8i8o) return "gOIdhw8i8o";
    if (v == dnnl_gOIdhw8o16i2o) return "gOIdhw8o16i2o";
    if (v == dnnl_gIOdhw8o16i2o) return "gIOdhw8o16i2o";
    if (v == dnnl_gOIdhw8o8i) return "gOIdhw8o8i";
    if (v == dnnl_gOIdhw8o4i) return "gOIdhw8o4i";
    if (v == dnnl_Goidhw16g) return "Goidhw16g";
    if (v == dnnl_Goidhw32g) return "Goidhw32g";
    if (v == dnnl_gOIdhw2i4o2i) return "gOIdhw2i4o2i";
    if (v == dnnl_gOIdhw4i8o2i) return "gOIdhw4i8o2i";
    if (v == dnnl_gOIdhw2o4i2o) return "gOIdhw2o4i2o";
    if (v == dnnl_gOIdhw4o8i2o) return "gOIdhw4o8i2o";
    if (v == dnnl_goIdhw4i) return "goIdhw4i";
    if (v == dnnl_goIdhw32i) return "goIdhw32i";
    if (v == dnnl_Owi24o) return "Owi24o";
    if (v == dnnl_OwI24o2i) return "OwI24o2i";
    if (v == dnnl_OwI24o4i) return "OwI24o4i";
    if (v == dnnl_Owi32o) return "Owi32o";
    if (v == dnnl_OwI32o2i) return "OwI32o2i";
    if (v == dnnl_OwI32o4i) return "OwI32o4i";
    if (v == dnnl_Owi48o) return "Owi48o";
    if (v == dnnl_OwI48o2i) return "OwI48o2i";
    if (v == dnnl_OwI48o4i) return "OwI48o4i";
    if (v == dnnl_Owi64o) return "Owi64o";
    if (v == dnnl_OwI64o2i) return "OwI64o2i";
    if (v == dnnl_OwI64o4i) return "OwI64o4i";
    if (v == dnnl_Iwo32i) return "Iwo32i";
    if (v == dnnl_IwO32i2o) return "IwO32i2o";
    if (v == dnnl_IwO32i4o) return "IwO32i4o";
    if (v == dnnl_Iwo48i) return "Iwo48i";
    if (v == dnnl_IwO48i2o) return "IwO48i2o";
    if (v == dnnl_IwO48i4o) return "IwO48i4o";
    if (v == dnnl_Iwo64i) return "Iwo64i";
    if (v == dnnl_IwO64i2o) return "IwO64i2o";
    if (v == dnnl_IwO64i4o) return "IwO64i4o";
    if (v == dnnl_wIo2i) return "wIo2i";
    if (v == dnnl_wIo4i) return "wIo4i";
    if (v == dnnl_gOwi24o) return "gOwi24o";
    if (v == dnnl_gOwI24o2i) return "gOwI24o2i";
    if (v == dnnl_gOwI24o4i) return "gOwI24o4i";
    if (v == dnnl_gOwi32o) return "gOwi32o";
    if (v == dnnl_gOwI32o2i) return "gOwI32o2i";
    if (v == dnnl_gOwI32o4i) return "gOwI32o4i";
    if (v == dnnl_gOwi48o) return "gOwi48o";
    if (v == dnnl_gOwI48o2i) return "gOwI48o2i";
    if (v == dnnl_gOwI48o4i) return "gOwI48o4i";
    if (v == dnnl_gOwi64o) return "gOwi64o";
    if (v == dnnl_gOwI64o2i) return "gOwI64o2i";
    if (v == dnnl_gOwI64o4i) return "gOwI64o4i";
    if (v == dnnl_gIwo32i) return "gIwo32i";
    if (v == dnnl_gIwO32i2o) return "gIwO32i2o";
    if (v == dnnl_gIwO32i4o) return "gIwO32i4o";
    if (v == dnnl_gIwo48i) return "gIwo48i";
    if (v == dnnl_gIwO48i2o) return "gIwO48i2o";
    if (v == dnnl_gIwO48i4o) return "gIwO48i4o";
    if (v == dnnl_gIwo64i) return "gIwo64i";
    if (v == dnnl_gIwO64i2o) return "gIwO64i2o";
    if (v == dnnl_gIwO64i4o) return "gIwO64i4o";
    if (v == dnnl_gwio) return "gwio";
    if (v == dnnl_gwIo2i) return "gwIo2i";
    if (v == dnnl_gwIo4i) return "gwIo4i";
    if (v == dnnl_OhwI24o) return "OhwI24o";
    if (v == dnnl_OhwI24o2i) return "OhwI24o2i";
    if (v == dnnl_OhwI24o4i) return "OhwI24o4i";
    if (v == dnnl_OhwI32o) return "OhwI32o";
    if (v == dnnl_OhwI32o2i) return "OhwI32o2i";
    if (v == dnnl_OhwI32o4i) return "OhwI32o4i";
    if (v == dnnl_Ohwi48o) return "Ohwi48o";
    if (v == dnnl_OhwI48o2i) return "OhwI48o2i";
    if (v == dnnl_OhwI48o4i) return "OhwI48o4i";
    if (v == dnnl_Ohwi64o) return "Ohwi64o";
    if (v == dnnl_OhwI64o2i) return "OhwI64o2i";
    if (v == dnnl_OhwI64o4i) return "OhwI64o4i";
    if (v == dnnl_Ihwo32i) return "Ihwo32i";
    if (v == dnnl_IhwO32i2o) return "IhwO32i2o";
    if (v == dnnl_IhwO32i4o) return "IhwO32i4o";
    if (v == dnnl_Ihwo48i) return "Ihwo48i";
    if (v == dnnl_IhwO48i2o) return "IhwO48i2o";
    if (v == dnnl_IhwO48i4o) return "IhwO48i4o";
    if (v == dnnl_Ihwo64i) return "Ihwo64i";
    if (v == dnnl_IhwO64i2o) return "IhwO64i2o";
    if (v == dnnl_IhwO64i4o) return "IhwO64i4o";
    if (v == dnnl_hwIo2i) return "hwIo2i";
    if (v == dnnl_hwIo4i) return "hwIo4i";
    if (v == dnnl_gOhwI24o) return "gOhwI24o";
    if (v == dnnl_gOhwI32o) return "gOhwI32o";
    if (v == dnnl_gOhwI32o2i) return "gOhwI32o2i";
    if (v == dnnl_gOhwI32o4i) return "gOhwI32o4i";
    if (v == dnnl_gOhwi48o) return "gOhwi48o";
    if (v == dnnl_gOhwI48o2i) return "gOhwI48o2i";
    if (v == dnnl_gOhwI48o4i) return "gOhwI48o4i";
    if (v == dnnl_gOhwi64o) return "gOhwi64o";
    if (v == dnnl_gOhwI64o2i) return "gOhwI64o2i";
    if (v == dnnl_gOhwI64o4i) return "gOhwI64o4i";
    if (v == dnnl_gIhwo32i) return "gIhwo32i";
    if (v == dnnl_gIhwO32i2o) return "gIhwO32i2o";
    if (v == dnnl_gIhwO32i4o) return "gIhwO32i4o";
    if (v == dnnl_gIhwo48i) return "gIhwo48i";
    if (v == dnnl_gIhwO48i2o) return "gIhwO48i2o";
    if (v == dnnl_gIhwO48i4o) return "gIhwO48i4o";
    if (v == dnnl_gIhwo64i) return "gIhwo64i";
    if (v == dnnl_gIhwO64i2o) return "gIhwO64i2o";
    if (v == dnnl_gIhwO64i4o) return "gIhwO64i4o";
    if (v == dnnl_ghwio) return "ghwio";
    if (v == dnnl_ghwIo2i) return "ghwIo2i";
    if (v == dnnl_ghwIo4i) return "ghwIo4i";
    if (v == dnnl_Odhwi24o) return "Odhwi24o";
    if (v == dnnl_OdhwI24o2i) return "OdhwI24o2i";
    if (v == dnnl_OdhwI24o4i) return "OdhwI24o4i";
    if (v == dnnl_Odhwi32o) return "Odhwi32o";
    if (v == dnnl_OdhwI32o2i) return "OdhwI32o2i";
    if (v == dnnl_OdhwI32o4i) return "OdhwI32o4i";
    if (v == dnnl_Odhwi48o) return "Odhwi48o";
    if (v == dnnl_OdhwI48o2i) return "OdhwI48o2i";
    if (v == dnnl_OdhwI48o4i) return "OdhwI48o4i";
    if (v == dnnl_Odhwi64o) return "Odhwi64o";
    if (v == dnnl_OdhwI64o2i) return "OdhwI64o2i";
    if (v == dnnl_OdhwI64o4i) return "OdhwI64o4i";
    if (v == dnnl_Idhwo32i) return "Idhwo32i";
    if (v == dnnl_IdhwO32i2o) return "IdhwO32i2o";
    if (v == dnnl_IdhwO32i4o) return "IdhwO32i4o";
    if (v == dnnl_Idhwo48i) return "Idhwo48i";
    if (v == dnnl_IdhwO48i2o) return "IdhwO48i2o";
    if (v == dnnl_IdhwO48i4o) return "IdhwO48i4o";
    if (v == dnnl_Idhwo64i) return "Idhwo64i";
    if (v == dnnl_IdhwO64i2o) return "IdhwO64i2o";
    if (v == dnnl_IdhwO64i4o) return "IdhwO64i4o";
    if (v == dnnl_dhwIo2i) return "dhwIo2i";
    if (v == dnnl_dhwIo4i) return "dhwIo4i";
    if (v == dnnl_gOdhwi24o) return "gOdhwi24o";
    if (v == dnnl_gOdhwI24o2i) return "gOdhwI24o2i";
    if (v == dnnl_gOdhwI24o4i) return "gOdhwI24o4i";
    if (v == dnnl_gOdhwi32o) return "gOdhwi32o";
    if (v == dnnl_gOdhwI32o2i) return "gOdhwI32o2i";
    if (v == dnnl_gOdhwI32o4i) return "gOdhwI32o4i";
    if (v == dnnl_gOdhwi48o) return "gOdhwi48o";
    if (v == dnnl_gOdhwI48o2i) return "gOdhwI48o2i";
    if (v == dnnl_gOdhwI48o4i) return "gOdhwI48o4i";
    if (v == dnnl_gOdhwi64o) return "gOdhwi64o";
    if (v == dnnl_gOdhwI64o2i) return "gOdhwI64o2i";
    if (v == dnnl_gOdhwI64o4i) return "gOdhwI64o4i";
    if (v == dnnl_gIdhwo32i) return "gIdhwo32i";
    if (v == dnnl_gIdhwO32i2o) return "gIdhwO32i2o";
    if (v == dnnl_gIdhwO32i4o) return "gIdhwO32i4o";
    if (v == dnnl_gIdhwo48i) return "gIdhwo48i";
    if (v == dnnl_gIdhwO48i2o) return "gIdhwO48i2o";
    if (v == dnnl_gIdhwO48i4o) return "gIdhwO48i4o";
    if (v == dnnl_gIdhwo64i) return "gIdhwo64i";
    if (v == dnnl_gIdhwO64i2o) return "gIdhwO64i2o";
    if (v == dnnl_gIdhwO64i4o) return "gIdhwO64i4o";
    if (v == dnnl_gdhwio) return "gdhwio";
    if (v == dnnl_gdhwIo2i) return "gdhwIo2i";
    if (v == dnnl_gdhwIo4i) return "gdhwIo4i";
    if (v == dnnl_OI16i32o4i) return "OI16i32o4i";
    if (v == dnnl_OI16i48o4i) return "OI16i48o4i";
    if (v == dnnl_OI16i64o4i) return "OI16i64o4i";
    if (v == dnnl_OI16i16o2i) return "OI16i16o2i";
    if (v == dnnl_OI16i32o2i) return "OI16i32o2i";
    if (v == dnnl_OI16i48o2i) return "OI16i48o2i";
    if (v == dnnl_OI16i64o2i) return "OI16i64o2i";
    if (v == dnnl_OIw16i32o4i) return "OIw16i32o4i";
    if (v == dnnl_OIw16i48o4i) return "OIw16i48o4i";
    if (v == dnnl_OIw16i64o4i) return "OIw16i64o4i";
    if (v == dnnl_OIw16i32o2i) return "OIw16i32o2i";
    if (v == dnnl_OIw16i48o2i) return "OIw16i48o2i";
    if (v == dnnl_OIw16i64o2i) return "OIw16i64o2i";
    if (v == dnnl_OIhw16i32o4i) return "OIhw16i32o4i";
    if (v == dnnl_OIhw16i48o4i) return "OIhw16i48o4i";
    if (v == dnnl_OIhw16i64o4i) return "OIhw16i64o4i";
    if (v == dnnl_OIhw16i32o2i) return "OIhw16i32o2i";
    if (v == dnnl_OIhw16i48o2i) return "OIhw16i48o2i";
    if (v == dnnl_OIhw16i64o2i) return "OIhw16i64o2i";
    if (v == dnnl_OIdhw16i32o4i) return "OIdhw16i32o4i";
    if (v == dnnl_OIdhw16i48o4i) return "OIdhw16i48o4i";
    if (v == dnnl_OIdhw16i64o4i) return "OIdhw16i64o4i";
    if (v == dnnl_OIdhw16i32o2i) return "OIdhw16i32o2i";
    if (v == dnnl_OIdhw16i48o2i) return "OIdhw16i48o2i";
    if (v == dnnl_OIdhw16i64o2i) return "OIdhw16i64o2i";
    if (v == dnnl_OwI16i16o2i) return "OwI16i16o2i";
    if (v == dnnl_OwI16i16o4i) return "OwI16i16o4i";
    if (v == dnnl_OhwI16i16o2i) return "OhwI16i16o2i";
    if (v == dnnl_OhwI16i16o4i) return "OhwI16i16o4i";
    if (v == dnnl_OdhwI16i16o2i) return "OdhwI16i16o2i";
    if (v == dnnl_OdhwI16i16o4i) return "OdhwI16i16o4i";
    if (v == dnnl_IwO16o16i2o) return "IwO16o16i2o";
    if (v == dnnl_IwO16o16i4o) return "IwO16o16i4o";
    if (v == dnnl_IhwO16o16i2o) return "IhwO16o16i2o";
    if (v == dnnl_IhwO16o16i4o) return "IhwO16o16i4o";
    if (v == dnnl_IdhwO16o16i2o) return "IdhwO16o16i2o";
    if (v == dnnl_IdhwO16o16i4o) return "IdhwO16o16i4o";
    if (v == dnnl_gOwI16i16o2i) return "gOwI16i16o2i";
    if (v == dnnl_gOwI16i16o4i) return "gOwI16i16o4i";
    if (v == dnnl_gOhwI16i16o2i) return "gOhwI16i16o2i";
    if (v == dnnl_gOhwI16i16o4i) return "gOhwI16i16o4i";
    if (v == dnnl_gOdhwI16i16o2i) return "gOdhwI16i16o2i";
    if (v == dnnl_gOdhwI16i16o4i) return "gOdhwI16i16o4i";
    if (v == dnnl_gIwO16o16i2o) return "gIwO16o16i2o";
    if (v == dnnl_gIwO16o16i4o) return "gIwO16o16i4o";
    if (v == dnnl_gIhwO16o16i2o) return "gIhwO16o16i2o";
    if (v == dnnl_gIhwO16o16i4o) return "gIhwO16o16i4o";
    if (v == dnnl_gIdhwO16o16i2o) return "gIdhwO16o16i2o";
    if (v == dnnl_gIdhwO16o16i4o) return "gIdhwO16o16i4o";
    if (v == dnnl_OwI16i32o2i) return "OwI16i32o2i";
    if (v == dnnl_OwI16i32o4i) return "OwI16i32o4i";
    if (v == dnnl_OwI16i48o2i) return "OwI16i48o2i";
    if (v == dnnl_OwI16i48o4i) return "OwI16i48o4i";
    if (v == dnnl_OwI16i64o2i) return "OwI16i64o2i";
    if (v == dnnl_OwI16i64o4i) return "OwI16i64o4i";
    if (v == dnnl_IwO16o32i2o) return "IwO16o32i2o";
    if (v == dnnl_IwO16o32i4o) return "IwO16o32i4o";
    if (v == dnnl_IwO16o48i2o) return "IwO16o48i2o";
    if (v == dnnl_IwO16o48i4o) return "IwO16o48i4o";
    if (v == dnnl_IwO16o64i2o) return "IwO16o64i2o";
    if (v == dnnl_IwO16o64i4o) return "IwO16o64i4o";
    if (v == dnnl_gOwI16i32o2i) return "gOwI16i32o2i";
    if (v == dnnl_gOwI16i32o4i) return "gOwI16i32o4i";
    if (v == dnnl_gOwI16i48o2i) return "gOwI16i48o2i";
    if (v == dnnl_gOwI16i48o4i) return "gOwI16i48o4i";
    if (v == dnnl_gOwI16i64o2i) return "gOwI16i64o2i";
    if (v == dnnl_gOwI16i64o4i) return "gOwI16i64o4i";
    if (v == dnnl_gIwO16o32i2o) return "gIwO16o32i2o";
    if (v == dnnl_gIwO16o32i4o) return "gIwO16o32i4o";
    if (v == dnnl_gIwO16o48i2o) return "gIwO16o48i2o";
    if (v == dnnl_gIwO16o48i4o) return "gIwO16o48i4o";
    if (v == dnnl_gIwO16o64i2o) return "gIwO16o64i2o";
    if (v == dnnl_gIwO16o64i4o) return "gIwO16o64i4o";
    if (v == dnnl_OhwI16i32o2i) return "OhwI16i32o2i";
    if (v == dnnl_OhwI16i32o4i) return "OhwI16i32o4i";
    if (v == dnnl_OhwI16i48o2i) return "OhwI16i48o2i";
    if (v == dnnl_OhwI16i48o4i) return "OhwI16i48o4i";
    if (v == dnnl_OhwI16i64o2i) return "OhwI16i64o2i";
    if (v == dnnl_OhwI16i64o4i) return "OhwI16i64o4i";
    if (v == dnnl_IhwO16o32i2o) return "IhwO16o32i2o";
    if (v == dnnl_IhwO16o32i4o) return "IhwO16o32i4o";
    if (v == dnnl_IhwO16o48i2o) return "IhwO16o48i2o";
    if (v == dnnl_IhwO16o48i4o) return "IhwO16o48i4o";
    if (v == dnnl_IhwO16o64i2o) return "IhwO16o64i2o";
    if (v == dnnl_IhwO16o64i4o) return "IhwO16o64i4o";
    if (v == dnnl_gOhwI16i32o2i) return "gOhwI16i32o2i";
    if (v == dnnl_gOhwI16i32o4i) return "gOhwI16i32o4i";
    if (v == dnnl_gOhwI16i48o2i) return "gOhwI16i48o2i";
    if (v == dnnl_gOhwI16i48o4i) return "gOhwI16i48o4i";
    if (v == dnnl_gOhwI16i64o2i) return "gOhwI16i64o2i";
    if (v == dnnl_gOhwI16i64o4i) return "gOhwI16i64o4i";
    if (v == dnnl_gIhwO16o32i2o) return "gIhwO16o32i2o";
    if (v == dnnl_gIhwO16o32i4o) return "gIhwO16o32i4o";
    if (v == dnnl_gIhwO16o48i2o) return "gIhwO16o48i2o";
    if (v == dnnl_gIhwO16o48i4o) return "gIhwO16o48i4o";
    if (v == dnnl_gIhwO16o64i2o) return "gIhwO16o64i2o";
    if (v == dnnl_gIhwO16o64i4o) return "gIhwO16o64i4o";
    if (v == dnnl_OdhwI16i32o2i) return "OdhwI16i32o2i";
    if (v == dnnl_OdhwI16i32o4i) return "OdhwI16i32o4i";
    if (v == dnnl_OdhwI16i48o2i) return "OdhwI16i48o2i";
    if (v == dnnl_OdhwI16i48o4i) return "OdhwI16i48o4i";
    if (v == dnnl_OdhwI16i64o2i) return "OdhwI16i64o2i";
    if (v == dnnl_OdhwI16i64o4i) return "OdhwI16i64o4i";
    if (v == dnnl_IdhwO16o32i2o) return "IdhwO16o32i2o";
    if (v == dnnl_IdhwO16o32i4o) return "IdhwO16o32i4o";
    if (v == dnnl_IdhwO16o48i2o) return "IdhwO16o48i2o";
    if (v == dnnl_IdhwO16o48i4o) return "IdhwO16o48i4o";
    if (v == dnnl_IdhwO16o64i2o) return "IdhwO16o64i2o";
    if (v == dnnl_IdhwO16o64i4o) return "IdhwO16o64i4o";
    if (v == dnnl_gOdhwI16i32o2i) return "gOdhwI16i32o2i";
    if (v == dnnl_gOdhwI16i32o4i) return "gOdhwI16i32o4i";
    if (v == dnnl_gOdhwI16i48o2i) return "gOdhwI16i48o2i";
    if (v == dnnl_gOdhwI16i48o4i) return "gOdhwI16i48o4i";
    if (v == dnnl_gOdhwI16i64o2i) return "gOdhwI16i64o2i";
    if (v == dnnl_gOdhwI16i64o4i) return "gOdhwI16i64o4i";
    if (v == dnnl_gIdhwO16o32i2o) return "gIdhwO16o32i2o";
    if (v == dnnl_gIdhwO16o32i4o) return "gIdhwO16o32i4o";
    if (v == dnnl_gIdhwO16o48i2o) return "gIdhwO16o48i2o";
    if (v == dnnl_gIdhwO16o48i4o) return "gIdhwO16o48i4o";
    if (v == dnnl_gIdhwO16o64i2o) return "gIdhwO16o64i2o";
    if (v == dnnl_gIdhwO16o64i4o) return "gIdhwO16o64i4o";
    if (v == dnnl_hwioG16g) return "hwioG16g";
    if (v == dnnl_hwioG8g) return "hwioG8g";
    if (v == dnnl_dhwioG16g) return "dhwioG16g";
    if (v == dnnl_dhwioG8g) return "dhwioG8g";
    if (v == dnnl_NCdhw40n16c) return "NCdhw40n16c";
    if (v == dnnl_NCw40n16c) return "NCw40n16c";
    if (v == dnnl_NChw40n16c) return "NChw40n16c";
    if (v == dnnl_NCw40n32c) return "NCw40n32c";
    if (v == dnnl_NChw40n32c) return "NChw40n32c";
    if (v == dnnl_NCdhw40n32c) return "NCdhw40n32c";
    if (v == dnnl_OIdhw4o8i8o2i) return "OIdhw4o8i8o2i";
    if (v == dnnl_OIhw4o8i8o2i) return "OIhw4o8i8o2i";
    if (v == dnnl_OIw4o8i8o2i) return "OIw4o8i8o2i";
    if (v == dnnl_gOIdhw4o8i8o2i) return "gOIdhw4o8i8o2i";
    if (v == dnnl_gOIhw4o8i8o2i) return "gOIhw4o8i8o2i";
    if (v == dnnl_gOIw4o8i8o2i) return "gOIw4o8i8o2i";
    if (v == dnnl_IOdhw4i8o8i2o) return "IOdhw4i8o8i2o";
    if (v == dnnl_IOhw4i8o8i2o) return "IOhw4i8o8i2o";
    if (v == dnnl_IOw4i8o8i2o) return "IOw4i8o8i2o";
    if (v == dnnl_gIOdhw4i8o8i2o) return "gIOdhw4i8o8i2o";
    if (v == dnnl_gIOhw4i8o8i2o) return "gIOhw4i8o8i2o";
    if (v == dnnl_gIOw4i8o8i2o) return "gIOw4i8o8i2o";
    if (v == dnnl_NCw2c32n8c) return "NCw2c32n8c";
    if (v == dnnl_NChw2c32n8c) return "NChw2c32n8c";
    if (v == dnnl_NCdhw2c32n8c) return "NCdhw2c32n8c";
    if (v == dnnl_OIw2i8o16i4o) return "OIw2i8o16i4o";
    if (v == dnnl_OIhw2i8o16i4o) return "OIhw2i8o16i4o";
    if (v == dnnl_OIdhw2i8o16i4o) return "OIdhw2i8o16i4o";
    if (v == dnnl_OIw2o8i16o4i) return "OIw2o8i16o4i";
    if (v == dnnl_OIw2o8i16o2i) return "OIw2o8i16o2i";
    if (v == dnnl_IOw2i8o16i4o) return "IOw2i8o16i4o";
    if (v == dnnl_IOw2i8o16i2o) return "IOw2i8o16i2o";
    if (v == dnnl_OIhw2o8i16o4i) return "OIhw2o8i16o4i";
    if (v == dnnl_OIhw2o8i16o2i) return "OIhw2o8i16o2i";
    if (v == dnnl_IOhw2i8o16i4o) return "IOhw2i8o16i4o";
    if (v == dnnl_IOhw2i8o16i2o) return "IOhw2i8o16i2o";
    if (v == dnnl_OIdhw2o8i16o4i) return "OIdhw2o8i16o4i";
    if (v == dnnl_OIdhw2o8i16o2i) return "OIdhw2o8i16o2i";
    if (v == dnnl_IOdhw2i8o16i4o) return "IOdhw2i8o16i4o";
    if (v == dnnl_IOdhw2i8o16i2o) return "IOdhw2i8o16i2o";
    if (v == dnnl_gOIw2o8i16o2i) return "gOIw2o8i16o2i";
    if (v == dnnl_gIOw2i8o16i2o) return "gIOw2i8o16i2o";
    if (v == dnnl_gIOhw2i8o16i2o) return "gIOhw2i8o16i2o";
    if (v == dnnl_gIOdhw2i8o16i2o) return "gIOdhw2i8o16i2o";
    if (v == dnnl_gOIhw2o8i16o2i) return "gOIhw2o8i16o2i";
    if (v == dnnl_gOIdhw2o8i16o2i) return "gOIdhw2o8i16o2i";
    if (v == dnnl_gOIw2o8i16o4i) return "gOIw2o8i16o4i";
    if (v == dnnl_gOIhw2o8i16o4i) return "gOIhw2o8i16o4i";
    assert(!"unknown fmt_tag");
    return "unknown fmt_tag";
}

const char *dnnl_prop_kind2str(dnnl_prop_kind_t v) {
    if (v == dnnl_prop_kind_undef) return "undef";
    if (v == dnnl_forward_training) return "forward_training";
    if (v == dnnl_forward_inference) return "forward_inference";
    if (v == dnnl_forward) return "forward";
    if (v == dnnl_backward) return "backward";
    if (v == dnnl_backward_data) return "backward_data";
    if (v == dnnl_backward_weights) return "backward_weights";
    if (v == dnnl_backward_bias) return "backward_bias";
    assert(!"unknown prop_kind");
    return "unknown prop_kind";
}

const char *dnnl_prim_kind2str(dnnl_primitive_kind_t v) {
    if (v == dnnl_undefined_primitive) return "undef";
    if (v == dnnl_reorder) return "reorder";
    if (v == dnnl_shuffle) return "shuffle";
    if (v == dnnl_concat) return "concat";
    if (v == dnnl_sum) return "sum";
    if (v == dnnl_convolution) return "convolution";
    if (v == dnnl_deconvolution) return "deconvolution";
    if (v == dnnl_eltwise) return "eltwise";
    if (v == dnnl_lrn) return "lrn";
    if (v == dnnl_batch_normalization) return "batch_normalization";
    if (v == dnnl_inner_product) return "inner_product";
    if (v == dnnl_rnn) return "rnn";
    if (v == dnnl_gemm) return "gemm";
    if (v == dnnl_binary) return "binary";
    if (v == dnnl_matmul) return "matmul";
    if (v == dnnl_resampling) return "resampling";
    if (v == dnnl_pooling) return "pooling";
    if (v == dnnl_reduction) return "reduction";
    if (v == dnnl_prelu) return "prelu";
    if (v == dnnl_softmax) return "softmax";
    if (v == dnnl_layer_normalization) return "layer_normalization";
    if (v == dnnl_group_normalization) return "group_normalization";
    if (v == dnnl_primitive_kind_max) return "primitive_kind_max";
    if (v == dnnl::impl::primitive_kind::sdpa) return "sdpa";
    assert(!"unknown prim_kind");
    return "unknown prim_kind";
}

const char *dnnl_alg_kind2str(dnnl_alg_kind_t v) {
    if (v == dnnl_alg_kind_undef) return "undef";
    if (v == dnnl_convolution_direct) return "convolution_direct";
    if (v == dnnl_convolution_winograd) return "convolution_winograd";
    if (v == dnnl_convolution_auto) return "convolution_auto";
    if (v == dnnl_deconvolution_direct) return "deconvolution_direct";
    if (v == dnnl_deconvolution_winograd) return "deconvolution_winograd";
    if (v == dnnl_eltwise_relu) return "eltwise_relu";
    if (v == dnnl_eltwise_tanh) return "eltwise_tanh";
    if (v == dnnl_eltwise_elu) return "eltwise_elu";
    if (v == dnnl_eltwise_square) return "eltwise_square";
    if (v == dnnl_eltwise_abs) return "eltwise_abs";
    if (v == dnnl_eltwise_sqrt) return "eltwise_sqrt";
    if (v == dnnl_eltwise_linear) return "eltwise_linear";
    if (v == dnnl_eltwise_soft_relu) return "eltwise_soft_relu";
    if (v == dnnl_eltwise_hardsigmoid) return "eltwise_hardsigmoid";
    if (v == dnnl_eltwise_logistic) return "eltwise_logistic";
    if (v == dnnl_eltwise_exp) return "eltwise_exp";
    if (v == dnnl_eltwise_gelu_tanh) return "eltwise_gelu_tanh";
    if (v == dnnl_eltwise_swish) return "eltwise_swish";
    if (v == dnnl_eltwise_log) return "eltwise_log";
    if (v == dnnl_eltwise_clip) return "eltwise_clip";
    if (v == dnnl_eltwise_clip_v2) return "eltwise_clip_v2";
    if (v == dnnl_eltwise_pow) return "eltwise_pow";
    if (v == dnnl_eltwise_gelu_erf) return "eltwise_gelu_erf";
    if (v == dnnl_eltwise_round) return "eltwise_round";
    if (v == dnnl_eltwise_mish) return "eltwise_mish";
    if (v == dnnl_eltwise_hardswish) return "eltwise_hardswish";
    if (v == dnnl_eltwise_relu_use_dst_for_bwd) return "eltwise_relu_use_dst_for_bwd";
    if (v == dnnl_eltwise_tanh_use_dst_for_bwd) return "eltwise_tanh_use_dst_for_bwd";
    if (v == dnnl_eltwise_elu_use_dst_for_bwd) return "eltwise_elu_use_dst_for_bwd";
    if (v == dnnl_eltwise_sqrt_use_dst_for_bwd) return "eltwise_sqrt_use_dst_for_bwd";
    if (v == dnnl_eltwise_logistic_use_dst_for_bwd) return "eltwise_logistic_use_dst_for_bwd";
    if (v == dnnl_eltwise_exp_use_dst_for_bwd) return "eltwise_exp_use_dst_for_bwd";
    if (v == dnnl_eltwise_clip_v2_use_dst_for_bwd) return "eltwise_clip_v2_use_dst_for_bwd";
    if (v == dnnl_pooling_max) return "pooling_max";
    if (v == dnnl_pooling_avg_include_padding) return "pooling_avg_include_padding";
    if (v == dnnl_pooling_avg_exclude_padding) return "pooling_avg_exclude_padding";
    if (v == dnnl_lrn_across_channels) return "lrn_across_channels";
    if (v == dnnl_lrn_within_channel) return "lrn_within_channel";
    if (v == dnnl_vanilla_rnn) return "vanilla_rnn";
    if (v == dnnl_vanilla_lstm) return "vanilla_lstm";
    if (v == dnnl_vanilla_gru) return "vanilla_gru";
    if (v == dnnl_lbr_gru) return "lbr_gru";
    if (v == dnnl_vanilla_augru) return "vanilla_augru";
    if (v == dnnl_lbr_augru) return "lbr_augru";
    if (v == dnnl_binary_add) return "binary_add";
    if (v == dnnl_binary_mul) return "binary_mul";
    if (v == dnnl_binary_max) return "binary_max";
    if (v == dnnl_binary_min) return "binary_min";
    if (v == dnnl_binary_div) return "binary_div";
    if (v == dnnl_binary_sub) return "binary_sub";
    if (v == dnnl_binary_ge) return "binary_ge";
    if (v == dnnl_binary_gt) return "binary_gt";
    if (v == dnnl_binary_le) return "binary_le";
    if (v == dnnl_binary_lt) return "binary_lt";
    if (v == dnnl_binary_eq) return "binary_eq";
    if (v == dnnl_binary_ne) return "binary_ne";
    if (v == dnnl_binary_select) return "binary_select";
    if (v == dnnl_resampling_nearest) return "resampling_nearest";
    if (v == dnnl_resampling_linear) return "resampling_linear";
    if (v == dnnl_reduction_max) return "reduction_max";
    if (v == dnnl_reduction_min) return "reduction_min";
    if (v == dnnl_reduction_sum) return "reduction_sum";
    if (v == dnnl_reduction_mul) return "reduction_mul";
    if (v == dnnl_reduction_mean) return "reduction_mean";
    if (v == dnnl_reduction_norm_lp_max) return "reduction_norm_lp_max";
    if (v == dnnl_reduction_norm_lp_sum) return "reduction_norm_lp_sum";
    if (v == dnnl_reduction_norm_lp_power_p_max) return "reduction_norm_lp_power_p_max";
    if (v == dnnl_reduction_norm_lp_power_p_sum) return "reduction_norm_lp_power_p_sum";
    if (v == dnnl_softmax_accurate) return "softmax_accurate";
    if (v == dnnl_softmax_log) return "softmax_log";
    assert(!"unknown alg_kind");
    return "unknown alg_kind";
}

const char *dnnl_rnn_flags2str(dnnl_rnn_flags_t v) {
    if (v == dnnl_rnn_flags_undef) return "undef";
    if (v == dnnl_rnn_flags_diff_weights_overwrite) return "rnn_flags_diff_weights_overwrite";
    assert(!"unknown rnn_flags");
    return "unknown rnn_flags";
}

const char *dnnl_rnn_direction2str(dnnl_rnn_direction_t v) {
    if (v == dnnl_rnn_direction_undef) return "undef";
    if (v == dnnl_unidirectional_left2right) return "unidirectional_left2right";
    if (v == dnnl_unidirectional_right2left) return "unidirectional_right2left";
    if (v == dnnl_bidirectional_concat) return "bidirectional_concat";
    if (v == dnnl_bidirectional_sum) return "bidirectional_sum";
    assert(!"unknown rnn_direction");
    return "unknown rnn_direction";
}

const char *dnnl_scratchpad_mode2str(dnnl_scratchpad_mode_t v) {
    if (v == dnnl_scratchpad_mode_library) return "library";
    if (v == dnnl_scratchpad_mode_user) return "user";
    assert(!"unknown scratchpad_mode");
    return "unknown scratchpad_mode";
}

const char *dnnl_rounding_mode2str(dnnl_rounding_mode_t v) {
    if (v == dnnl_rounding_mode_environment) return "environment";
    if (v == dnnl_rounding_mode_stochastic) return "stochastic";
    assert(!"unknown rounding_mode");
    return "unknown rounding_mode";
}

const char *dnnl_cpu_isa2str(dnnl_cpu_isa_t v) {
    if (v == dnnl_cpu_isa_default) return "cpu_isa_default";
    if (v == dnnl_cpu_isa_sse41) return "cpu_isa_sse41";
    if (v == dnnl_cpu_isa_avx) return "cpu_isa_avx";
    if (v == dnnl_cpu_isa_avx2) return "cpu_isa_avx2";
    if (v == dnnl_cpu_isa_avx2_vnni) return "cpu_isa_avx2_vnni";
    if (v == dnnl_cpu_isa_avx2_vnni_2) return "cpu_isa_avx2_vnni_2";
    if (v == dnnl_cpu_isa_avx512_core) return "cpu_isa_avx512_core";
    if (v == dnnl_cpu_isa_avx512_core_vnni) return "cpu_isa_avx512_core_vnni";
    if (v == dnnl_cpu_isa_avx512_core_bf16) return "cpu_isa_avx512_core_bf16";
    if (v == dnnl_cpu_isa_avx10_1_512) return "cpu_isa_avx10_1_512";
    if (v == dnnl_cpu_isa_avx512_core_fp16) return "cpu_isa_avx512_core_fp16";
    if (v == dnnl_cpu_isa_avx10_1_512_amx) return "cpu_isa_avx10_1_512_amx";
    if (v == dnnl_cpu_isa_avx512_core_amx) return "cpu_isa_avx512_core_amx";
    if (v == dnnl_cpu_isa_avx10_1_512_amx_fp16) return "cpu_isa_avx10_1_512_amx_fp16";
    if (v == dnnl_cpu_isa_avx512_core_amx_fp16) return "cpu_isa_avx512_core_amx_fp16";
    assert(!"unknown cpu_isa");
    return "unknown cpu_isa";
}

const char *dnnl_cpu_isa_hints2str(dnnl_cpu_isa_hints_t v) {
    if (v == dnnl_cpu_isa_no_hints) return "cpu_isa_no_hints";
    if (v == dnnl_cpu_isa_prefer_ymm) return "cpu_isa_prefer_ymm";
    assert(!"unknown cpu_isa_hints");
    return "unknown cpu_isa_hints";
}


