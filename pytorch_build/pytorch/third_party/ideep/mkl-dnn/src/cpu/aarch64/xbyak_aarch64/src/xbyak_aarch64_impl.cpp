/*******************************************************************************
 * Copyright 2020-2023 FUJITSU LIMITED
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/
#define XBYAK_AARCH64_MAKE_INSTANCE
#include "xbyak_aarch64.h"
#include <memory.h>
#include <stdio.h>
#ifdef _WIN32
#include <intrin.h>
#include <processthreadsapi.h> // FlushInstructionCache
#endif

namespace Xbyak_aarch64 {

#include "err_impl.h"
#include "xbyak_aarch64_impl.h"
#include "xbyak_aarch64_mnemonic.h"

} // namespace Xbyak_aarch64
