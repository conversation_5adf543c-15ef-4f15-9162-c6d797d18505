/*******************************************************************************
 * Copyright 2019-2023 FUJITSU LIMITED
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/
void adr(const XReg &xd, const Label &label);
void adr(const XReg &xd, const int64_t label);
void adrp(const XReg &xd, const Label &label);
void adrp(const XReg &xd, const int64_t label);
void add(const WReg &rd, const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void add(const XReg &rd, const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void adds(const WReg &rd, const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void adds(const XReg &rd, const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void cmn(const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void cmn(const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void sub(const WReg &rd, const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void sub(const XReg &rd, const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void subs(const WReg &rd, const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void subs(const XReg &rd, const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void cmp(const WReg &rn, const uint32_t imm, const uint32_t sh = 0);
void cmp(const XReg &rn, const uint32_t imm, const uint32_t sh = 0);
void and_(const WReg &rd, const WReg &rn, const uint64_t imm);
void and_(const XReg &rd, const XReg &rn, const uint64_t imm);
void orr(const WReg &rd, const WReg &rn, const uint64_t imm);
void orr(const XReg &rd, const XReg &rn, const uint64_t imm);
void eor(const WReg &rd, const WReg &rn, const uint64_t imm);
void eor(const XReg &rd, const XReg &rn, const uint64_t imm);
void ands(const WReg &rd, const WReg &rn, const uint64_t imm);
void ands(const XReg &rd, const XReg &rn, const uint64_t imm);
void tst(const WReg &rn, const uint64_t imm);
void tst(const XReg &rn, const uint64_t imm);
void movn(const WReg &rd, const uint32_t imm, const uint32_t sh = 0);
void movn(const XReg &rd, const uint32_t imm, const uint32_t sh = 0);
void movz(const WReg &rd, const uint32_t imm, const uint32_t sh = 0);
void movz(const XReg &rd, const uint32_t imm, const uint32_t sh = 0);
void movk(const WReg &rd, const uint32_t imm, const uint32_t sh = 0);
void movk(const XReg &rd, const uint32_t imm, const uint32_t sh = 0);
void mov(const WReg &rd, const uint64_t imm);
void mov(const XReg &rd, const uint64_t imm);
void sbfm(const WReg &rd, const WReg &rn, const uint32_t immr, const uint32_t imms);
void sbfm(const XReg &rd, const XReg &rn, const uint32_t immr, const uint32_t imms);
void sbfiz(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void sbfiz(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void sbfx(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void sbfx(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void sxtb(const WReg &rd, const WReg &rn);
void sxtb(const XReg &rd, const WReg &rn);
void sxth(const WReg &rd, const WReg &rn);
void sxth(const XReg &rd, const WReg &rn);
void sxtw(const WReg &rd, const WReg &rn);
void sxtw(const XReg &rd, const WReg &rn);
void asr(const WReg &rd, const WReg &rn, const uint32_t immr);
void asr(const XReg &rd, const XReg &rn, const uint32_t immr);
void bfm(const WReg &rd, const WReg &rn, const uint32_t immr, const uint32_t imms);
void bfm(const XReg &rd, const XReg &rn, const uint32_t immr, const uint32_t imms);
void bfc(const WReg &rd, const uint32_t lsb, const uint32_t width);
void bfc(const XReg &rd, const uint32_t lsb, const uint32_t width);
void bfi(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void bfi(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void bfxil(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void bfxil(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void ubfm(const WReg &rd, const WReg &rn, const uint32_t immr, const uint32_t imms);
void ubfm(const XReg &rd, const XReg &rn, const uint32_t immr, const uint32_t imms);
void ubfiz(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void ubfiz(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void ubfx(const WReg &rd, const WReg &rn, const uint32_t lsb, const uint32_t width);
void ubfx(const XReg &rd, const XReg &rn, const uint32_t lsb, const uint32_t width);
void lsl(const WReg &rd, const WReg &rn, const uint32_t sh);
void lsl(const XReg &rd, const XReg &rn, const uint32_t sh);
void lsr(const WReg &rd, const WReg &rn, const uint32_t sh);
void lsr(const XReg &rd, const XReg &rn, const uint32_t sh);
void uxtb(const WReg &rd, const WReg &rn);
void uxtb(const XReg &rd, const XReg &rn);
void uxth(const WReg &rd, const WReg &rn);
void uxth(const XReg &rd, const XReg &rn);
void extr(const WReg &rd, const WReg &rn, const WReg &rm, const uint32_t imm);
void extr(const XReg &rd, const XReg &rn, const XReg &rm, const uint32_t imm);
void ror(const WReg &rd, const WReg &rn, const uint32_t imm);
void ror(const XReg &rd, const XReg &rn, const uint32_t imm);
void b(const Cond cond, const Label &label);
void b(const Cond cond, const int64_t label);
void svc(const uint32_t imm);
void hvc(const uint32_t imm);
void smc(const uint32_t imm);
void brk(const uint32_t imm);
void hlt(const uint32_t imm);
void tcancel(const uint32_t imm);
void dcps1(const uint32_t imm);
void dcps2(const uint32_t imm);
void dcps3(const uint32_t imm);
void wfet(const XReg &rt);
void wfit(const XReg &rt);
void hint(const uint32_t imm);
void nop();
void yield();
void wfe();
void wfi();
void sev();
void sevl();
void xpaclri();
void pacia1716();
void pacib1716();
void autia1716();
void autib1716();
void esb();
void psb_csync();
void tsb_csync();
void csdb();
void paciaz();
void paciasp();
void pacibz();
void pacibsp();
void autiaz();
void autiasp();
void autibz();
void autibsp();
void dsb(const BarOpt opt);
void dmb(const BarOpt opt);
void isb(const BarOpt opt);
void tcommit();
void clrex(const uint32_t imm);
void ssbb();
void pssbb();
void msr(const PStateField psfield, const uint32_t imm);
void cfinv();
void tstart(const XReg &rt);
void ttest(const XReg &rt);
void sys(const uint32_t op1, const uint32_t CRn, const uint32_t CRm, const uint32_t op2, const XReg &rt = XReg(31));
void sysl(const XReg &rt, const uint32_t op1, const uint32_t CRn, const uint32_t CRm, const uint32_t op2);
void msr(const uint32_t op0, const uint32_t op1, const uint32_t CRn, const uint32_t CRm, const uint32_t op2, const XReg &rt);
void mrs(const XReg &rt, const uint32_t op0, const uint32_t op1, const uint32_t CRn, const uint32_t CRm, const uint32_t op2);
void ret();
void retaa();
void retab();
void eret();
void eretaa();
void eretab();
void drps();
void br(const XReg &rn);
void braaz(const XReg &rn);
void brabz(const XReg &rn);
void blr(const XReg &rn);
void blraaz(const XReg &rn);
void blrabz(const XReg &rn);
void ret(const XReg &rn);
void braa(const XReg &rn, const XReg &rm);
void brab(const XReg &rn, const XReg &rm);
void blraa(const XReg &rn, const XReg &rm);
void blrab(const XReg &rn, const XReg &rm);
void b(const Label &label);
void b(const int64_t label);
void bl(const Label &label);
void bl(const int64_t label);
void cbz(const WReg &rt, const Label &label);
void cbz(const XReg &rt, const Label &label);
void cbz(const WReg &rt, const int64_t label);
void cbz(const XReg &rt, const int64_t label);
void cbnz(const WReg &rt, const Label &label);
void cbnz(const XReg &rt, const Label &label);
void cbnz(const WReg &rt, const int64_t label);
void cbnz(const XReg &rt, const int64_t label);
void tbz(const WReg &rt, const uint32_t imm, const Label &label);
void tbz(const XReg &rt, const uint32_t imm, const Label &label);
void tbz(const WReg &rt, const uint32_t imm, const int64_t label);
void tbz(const XReg &rt, const uint32_t imm, const int64_t label);
void tbnz(const WReg &rt, const uint32_t imm, const Label &label);
void tbnz(const XReg &rt, const uint32_t imm, const Label &label);
void tbnz(const WReg &rt, const uint32_t imm, const int64_t label);
void tbnz(const XReg &rt, const uint32_t imm, const int64_t label);
void st1(const VReg8BList &vt, const AdrNoOfs &adr);
void st1(const VReg4HList &vt, const AdrNoOfs &adr);
void st1(const VReg2SList &vt, const AdrNoOfs &adr);
void st1(const VReg1DList &vt, const AdrNoOfs &adr);
void st1(const VReg16BList &vt, const AdrNoOfs &adr);
void st1(const VReg8HList &vt, const AdrNoOfs &adr);
void st1(const VReg4SList &vt, const AdrNoOfs &adr);
void st1(const VReg2DList &vt, const AdrNoOfs &adr);
void ld1(const VReg8BList &vt, const AdrNoOfs &adr);
void ld1(const VReg4HList &vt, const AdrNoOfs &adr);
void ld1(const VReg2SList &vt, const AdrNoOfs &adr);
void ld1(const VReg1DList &vt, const AdrNoOfs &adr);
void ld1(const VReg16BList &vt, const AdrNoOfs &adr);
void ld1(const VReg8HList &vt, const AdrNoOfs &adr);
void ld1(const VReg4SList &vt, const AdrNoOfs &adr);
void ld1(const VReg2DList &vt, const AdrNoOfs &adr);
void st4(const VReg8BList &vt, const AdrNoOfs &adr);
void st4(const VReg4HList &vt, const AdrNoOfs &adr);
void st4(const VReg2SList &vt, const AdrNoOfs &adr);
void st4(const VReg16BList &vt, const AdrNoOfs &adr);
void st4(const VReg8HList &vt, const AdrNoOfs &adr);
void st4(const VReg4SList &vt, const AdrNoOfs &adr);
void st4(const VReg2DList &vt, const AdrNoOfs &adr);
void st3(const VReg8BList &vt, const AdrNoOfs &adr);
void st3(const VReg4HList &vt, const AdrNoOfs &adr);
void st3(const VReg2SList &vt, const AdrNoOfs &adr);
void st3(const VReg16BList &vt, const AdrNoOfs &adr);
void st3(const VReg8HList &vt, const AdrNoOfs &adr);
void st3(const VReg4SList &vt, const AdrNoOfs &adr);
void st3(const VReg2DList &vt, const AdrNoOfs &adr);
void st2(const VReg8BList &vt, const AdrNoOfs &adr);
void st2(const VReg4HList &vt, const AdrNoOfs &adr);
void st2(const VReg2SList &vt, const AdrNoOfs &adr);
void st2(const VReg16BList &vt, const AdrNoOfs &adr);
void st2(const VReg8HList &vt, const AdrNoOfs &adr);
void st2(const VReg4SList &vt, const AdrNoOfs &adr);
void st2(const VReg2DList &vt, const AdrNoOfs &adr);
void ld4(const VReg8BList &vt, const AdrNoOfs &adr);
void ld4(const VReg4HList &vt, const AdrNoOfs &adr);
void ld4(const VReg2SList &vt, const AdrNoOfs &adr);
void ld4(const VReg16BList &vt, const AdrNoOfs &adr);
void ld4(const VReg8HList &vt, const AdrNoOfs &adr);
void ld4(const VReg4SList &vt, const AdrNoOfs &adr);
void ld4(const VReg2DList &vt, const AdrNoOfs &adr);
void ld3(const VReg8BList &vt, const AdrNoOfs &adr);
void ld3(const VReg4HList &vt, const AdrNoOfs &adr);
void ld3(const VReg2SList &vt, const AdrNoOfs &adr);
void ld3(const VReg16BList &vt, const AdrNoOfs &adr);
void ld3(const VReg8HList &vt, const AdrNoOfs &adr);
void ld3(const VReg4SList &vt, const AdrNoOfs &adr);
void ld3(const VReg2DList &vt, const AdrNoOfs &adr);
void ld2(const VReg8BList &vt, const AdrNoOfs &adr);
void ld2(const VReg4HList &vt, const AdrNoOfs &adr);
void ld2(const VReg2SList &vt, const AdrNoOfs &adr);
void ld2(const VReg16BList &vt, const AdrNoOfs &adr);
void ld2(const VReg8HList &vt, const AdrNoOfs &adr);
void ld2(const VReg4SList &vt, const AdrNoOfs &adr);
void ld2(const VReg2DList &vt, const AdrNoOfs &adr);
void st1(const VReg8BList &vt, const AdrPostReg &adr);
void st1(const VReg4HList &vt, const AdrPostReg &adr);
void st1(const VReg2SList &vt, const AdrPostReg &adr);
void st1(const VReg1DList &vt, const AdrPostReg &adr);
void st1(const VReg16BList &vt, const AdrPostReg &adr);
void st1(const VReg8HList &vt, const AdrPostReg &adr);
void st1(const VReg4SList &vt, const AdrPostReg &adr);
void st1(const VReg2DList &vt, const AdrPostReg &adr);
void ld1(const VReg8BList &vt, const AdrPostReg &adr);
void ld1(const VReg4HList &vt, const AdrPostReg &adr);
void ld1(const VReg2SList &vt, const AdrPostReg &adr);
void ld1(const VReg1DList &vt, const AdrPostReg &adr);
void ld1(const VReg16BList &vt, const AdrPostReg &adr);
void ld1(const VReg8HList &vt, const AdrPostReg &adr);
void ld1(const VReg4SList &vt, const AdrPostReg &adr);
void ld1(const VReg2DList &vt, const AdrPostReg &adr);
void st4(const VReg8BList &vt, const AdrPostReg &adr);
void st4(const VReg4HList &vt, const AdrPostReg &adr);
void st4(const VReg2SList &vt, const AdrPostReg &adr);
void st4(const VReg16BList &vt, const AdrPostReg &adr);
void st4(const VReg8HList &vt, const AdrPostReg &adr);
void st4(const VReg4SList &vt, const AdrPostReg &adr);
void st4(const VReg2DList &vt, const AdrPostReg &adr);
void st3(const VReg8BList &vt, const AdrPostReg &adr);
void st3(const VReg4HList &vt, const AdrPostReg &adr);
void st3(const VReg2SList &vt, const AdrPostReg &adr);
void st3(const VReg16BList &vt, const AdrPostReg &adr);
void st3(const VReg8HList &vt, const AdrPostReg &adr);
void st3(const VReg4SList &vt, const AdrPostReg &adr);
void st3(const VReg2DList &vt, const AdrPostReg &adr);
void st2(const VReg8BList &vt, const AdrPostReg &adr);
void st2(const VReg4HList &vt, const AdrPostReg &adr);
void st2(const VReg2SList &vt, const AdrPostReg &adr);
void st2(const VReg16BList &vt, const AdrPostReg &adr);
void st2(const VReg8HList &vt, const AdrPostReg &adr);
void st2(const VReg4SList &vt, const AdrPostReg &adr);
void st2(const VReg2DList &vt, const AdrPostReg &adr);
void ld4(const VReg8BList &vt, const AdrPostReg &adr);
void ld4(const VReg4HList &vt, const AdrPostReg &adr);
void ld4(const VReg2SList &vt, const AdrPostReg &adr);
void ld4(const VReg16BList &vt, const AdrPostReg &adr);
void ld4(const VReg8HList &vt, const AdrPostReg &adr);
void ld4(const VReg4SList &vt, const AdrPostReg &adr);
void ld4(const VReg2DList &vt, const AdrPostReg &adr);
void ld3(const VReg8BList &vt, const AdrPostReg &adr);
void ld3(const VReg4HList &vt, const AdrPostReg &adr);
void ld3(const VReg2SList &vt, const AdrPostReg &adr);
void ld3(const VReg16BList &vt, const AdrPostReg &adr);
void ld3(const VReg8HList &vt, const AdrPostReg &adr);
void ld3(const VReg4SList &vt, const AdrPostReg &adr);
void ld3(const VReg2DList &vt, const AdrPostReg &adr);
void ld2(const VReg8BList &vt, const AdrPostReg &adr);
void ld2(const VReg4HList &vt, const AdrPostReg &adr);
void ld2(const VReg2SList &vt, const AdrPostReg &adr);
void ld2(const VReg16BList &vt, const AdrPostReg &adr);
void ld2(const VReg8HList &vt, const AdrPostReg &adr);
void ld2(const VReg4SList &vt, const AdrPostReg &adr);
void ld2(const VReg2DList &vt, const AdrPostReg &adr);
void st1(const VReg8BList &vt, const AdrPostImm &adr);
void st1(const VReg4HList &vt, const AdrPostImm &adr);
void st1(const VReg2SList &vt, const AdrPostImm &adr);
void st1(const VReg1DList &vt, const AdrPostImm &adr);
void st1(const VReg16BList &vt, const AdrPostImm &adr);
void st1(const VReg8HList &vt, const AdrPostImm &adr);
void st1(const VReg4SList &vt, const AdrPostImm &adr);
void st1(const VReg2DList &vt, const AdrPostImm &adr);
void ld1(const VReg8BList &vt, const AdrPostImm &adr);
void ld1(const VReg4HList &vt, const AdrPostImm &adr);
void ld1(const VReg2SList &vt, const AdrPostImm &adr);
void ld1(const VReg1DList &vt, const AdrPostImm &adr);
void ld1(const VReg16BList &vt, const AdrPostImm &adr);
void ld1(const VReg8HList &vt, const AdrPostImm &adr);
void ld1(const VReg4SList &vt, const AdrPostImm &adr);
void ld1(const VReg2DList &vt, const AdrPostImm &adr);
void st4(const VReg8BList &vt, const AdrPostImm &adr);
void st4(const VReg4HList &vt, const AdrPostImm &adr);
void st4(const VReg2SList &vt, const AdrPostImm &adr);
void st4(const VReg16BList &vt, const AdrPostImm &adr);
void st4(const VReg8HList &vt, const AdrPostImm &adr);
void st4(const VReg4SList &vt, const AdrPostImm &adr);
void st4(const VReg2DList &vt, const AdrPostImm &adr);
void st3(const VReg8BList &vt, const AdrPostImm &adr);
void st3(const VReg4HList &vt, const AdrPostImm &adr);
void st3(const VReg2SList &vt, const AdrPostImm &adr);
void st3(const VReg16BList &vt, const AdrPostImm &adr);
void st3(const VReg8HList &vt, const AdrPostImm &adr);
void st3(const VReg4SList &vt, const AdrPostImm &adr);
void st3(const VReg2DList &vt, const AdrPostImm &adr);
void st2(const VReg8BList &vt, const AdrPostImm &adr);
void st2(const VReg4HList &vt, const AdrPostImm &adr);
void st2(const VReg2SList &vt, const AdrPostImm &adr);
void st2(const VReg16BList &vt, const AdrPostImm &adr);
void st2(const VReg8HList &vt, const AdrPostImm &adr);
void st2(const VReg4SList &vt, const AdrPostImm &adr);
void st2(const VReg2DList &vt, const AdrPostImm &adr);
void ld4(const VReg8BList &vt, const AdrPostImm &adr);
void ld4(const VReg4HList &vt, const AdrPostImm &adr);
void ld4(const VReg2SList &vt, const AdrPostImm &adr);
void ld4(const VReg16BList &vt, const AdrPostImm &adr);
void ld4(const VReg8HList &vt, const AdrPostImm &adr);
void ld4(const VReg4SList &vt, const AdrPostImm &adr);
void ld4(const VReg2DList &vt, const AdrPostImm &adr);
void ld3(const VReg8BList &vt, const AdrPostImm &adr);
void ld3(const VReg4HList &vt, const AdrPostImm &adr);
void ld3(const VReg2SList &vt, const AdrPostImm &adr);
void ld3(const VReg16BList &vt, const AdrPostImm &adr);
void ld3(const VReg8HList &vt, const AdrPostImm &adr);
void ld3(const VReg4SList &vt, const AdrPostImm &adr);
void ld3(const VReg2DList &vt, const AdrPostImm &adr);
void ld2(const VReg8BList &vt, const AdrPostImm &adr);
void ld2(const VReg4HList &vt, const AdrPostImm &adr);
void ld2(const VReg2SList &vt, const AdrPostImm &adr);
void ld2(const VReg16BList &vt, const AdrPostImm &adr);
void ld2(const VReg8HList &vt, const AdrPostImm &adr);
void ld2(const VReg4SList &vt, const AdrPostImm &adr);
void ld2(const VReg2DList &vt, const AdrPostImm &adr);
void st4(const VRegBElem &vt, const AdrNoOfs &adr);
void st4(const VRegHElem &vt, const AdrNoOfs &adr);
void st4(const VRegSElem &vt, const AdrNoOfs &adr);
void st4(const VRegDElem &vt, const AdrNoOfs &adr);
void st3(const VRegBElem &vt, const AdrNoOfs &adr);
void st3(const VRegHElem &vt, const AdrNoOfs &adr);
void st3(const VRegSElem &vt, const AdrNoOfs &adr);
void st3(const VRegDElem &vt, const AdrNoOfs &adr);
void st2(const VRegBElem &vt, const AdrNoOfs &adr);
void st2(const VRegHElem &vt, const AdrNoOfs &adr);
void st2(const VRegSElem &vt, const AdrNoOfs &adr);
void st2(const VRegDElem &vt, const AdrNoOfs &adr);
void st1(const VRegBElem &vt, const AdrNoOfs &adr);
void st1(const VRegHElem &vt, const AdrNoOfs &adr);
void st1(const VRegSElem &vt, const AdrNoOfs &adr);
void st1(const VRegDElem &vt, const AdrNoOfs &adr);
void ld4(const VRegBElem &vt, const AdrNoOfs &adr);
void ld4(const VRegHElem &vt, const AdrNoOfs &adr);
void ld4(const VRegSElem &vt, const AdrNoOfs &adr);
void ld4(const VRegDElem &vt, const AdrNoOfs &adr);
void ld3(const VRegBElem &vt, const AdrNoOfs &adr);
void ld3(const VRegHElem &vt, const AdrNoOfs &adr);
void ld3(const VRegSElem &vt, const AdrNoOfs &adr);
void ld3(const VRegDElem &vt, const AdrNoOfs &adr);
void ld2(const VRegBElem &vt, const AdrNoOfs &adr);
void ld2(const VRegHElem &vt, const AdrNoOfs &adr);
void ld2(const VRegSElem &vt, const AdrNoOfs &adr);
void ld2(const VRegDElem &vt, const AdrNoOfs &adr);
void ld1(const VRegBElem &vt, const AdrNoOfs &adr);
void ld1(const VRegHElem &vt, const AdrNoOfs &adr);
void ld1(const VRegSElem &vt, const AdrNoOfs &adr);
void ld1(const VRegDElem &vt, const AdrNoOfs &adr);
void ld4r(const VReg8BList &vt, const AdrNoOfs &adr);
void ld4r(const VReg4HList &vt, const AdrNoOfs &adr);
void ld4r(const VReg2SList &vt, const AdrNoOfs &adr);
void ld4r(const VReg1DList &vt, const AdrNoOfs &adr);
void ld4r(const VReg16BList &vt, const AdrNoOfs &adr);
void ld4r(const VReg8HList &vt, const AdrNoOfs &adr);
void ld4r(const VReg4SList &vt, const AdrNoOfs &adr);
void ld4r(const VReg2DList &vt, const AdrNoOfs &adr);
void ld3r(const VReg8BList &vt, const AdrNoOfs &adr);
void ld3r(const VReg4HList &vt, const AdrNoOfs &adr);
void ld3r(const VReg2SList &vt, const AdrNoOfs &adr);
void ld3r(const VReg1DList &vt, const AdrNoOfs &adr);
void ld3r(const VReg16BList &vt, const AdrNoOfs &adr);
void ld3r(const VReg8HList &vt, const AdrNoOfs &adr);
void ld3r(const VReg4SList &vt, const AdrNoOfs &adr);
void ld3r(const VReg2DList &vt, const AdrNoOfs &adr);
void ld2r(const VReg8BList &vt, const AdrNoOfs &adr);
void ld2r(const VReg4HList &vt, const AdrNoOfs &adr);
void ld2r(const VReg2SList &vt, const AdrNoOfs &adr);
void ld2r(const VReg1DList &vt, const AdrNoOfs &adr);
void ld2r(const VReg16BList &vt, const AdrNoOfs &adr);
void ld2r(const VReg8HList &vt, const AdrNoOfs &adr);
void ld2r(const VReg4SList &vt, const AdrNoOfs &adr);
void ld2r(const VReg2DList &vt, const AdrNoOfs &adr);
void ld1r(const VReg8BList &vt, const AdrNoOfs &adr);
void ld1r(const VReg4HList &vt, const AdrNoOfs &adr);
void ld1r(const VReg2SList &vt, const AdrNoOfs &adr);
void ld1r(const VReg1DList &vt, const AdrNoOfs &adr);
void ld1r(const VReg16BList &vt, const AdrNoOfs &adr);
void ld1r(const VReg8HList &vt, const AdrNoOfs &adr);
void ld1r(const VReg4SList &vt, const AdrNoOfs &adr);
void ld1r(const VReg2DList &vt, const AdrNoOfs &adr);
void st4(const VRegBElem &vt, const AdrPostReg &adr);
void st4(const VRegHElem &vt, const AdrPostReg &adr);
void st4(const VRegSElem &vt, const AdrPostReg &adr);
void st4(const VRegDElem &vt, const AdrPostReg &adr);
void st3(const VRegBElem &vt, const AdrPostReg &adr);
void st3(const VRegHElem &vt, const AdrPostReg &adr);
void st3(const VRegSElem &vt, const AdrPostReg &adr);
void st3(const VRegDElem &vt, const AdrPostReg &adr);
void st2(const VRegBElem &vt, const AdrPostReg &adr);
void st2(const VRegHElem &vt, const AdrPostReg &adr);
void st2(const VRegSElem &vt, const AdrPostReg &adr);
void st2(const VRegDElem &vt, const AdrPostReg &adr);
void st1(const VRegBElem &vt, const AdrPostReg &adr);
void st1(const VRegHElem &vt, const AdrPostReg &adr);
void st1(const VRegSElem &vt, const AdrPostReg &adr);
void st1(const VRegDElem &vt, const AdrPostReg &adr);
void ld4(const VRegBElem &vt, const AdrPostReg &adr);
void ld4(const VRegHElem &vt, const AdrPostReg &adr);
void ld4(const VRegSElem &vt, const AdrPostReg &adr);
void ld4(const VRegDElem &vt, const AdrPostReg &adr);
void ld3(const VRegBElem &vt, const AdrPostReg &adr);
void ld3(const VRegHElem &vt, const AdrPostReg &adr);
void ld3(const VRegSElem &vt, const AdrPostReg &adr);
void ld3(const VRegDElem &vt, const AdrPostReg &adr);
void ld2(const VRegBElem &vt, const AdrPostReg &adr);
void ld2(const VRegHElem &vt, const AdrPostReg &adr);
void ld2(const VRegSElem &vt, const AdrPostReg &adr);
void ld2(const VRegDElem &vt, const AdrPostReg &adr);
void ld1(const VRegBElem &vt, const AdrPostReg &adr);
void ld1(const VRegHElem &vt, const AdrPostReg &adr);
void ld1(const VRegSElem &vt, const AdrPostReg &adr);
void ld1(const VRegDElem &vt, const AdrPostReg &adr);
void ld4r(const VReg8BList &vt, const AdrPostReg &adr);
void ld4r(const VReg4HList &vt, const AdrPostReg &adr);
void ld4r(const VReg2SList &vt, const AdrPostReg &adr);
void ld4r(const VReg1DList &vt, const AdrPostReg &adr);
void ld4r(const VReg16BList &vt, const AdrPostReg &adr);
void ld4r(const VReg8HList &vt, const AdrPostReg &adr);
void ld4r(const VReg4SList &vt, const AdrPostReg &adr);
void ld4r(const VReg2DList &vt, const AdrPostReg &adr);
void ld3r(const VReg8BList &vt, const AdrPostReg &adr);
void ld3r(const VReg4HList &vt, const AdrPostReg &adr);
void ld3r(const VReg2SList &vt, const AdrPostReg &adr);
void ld3r(const VReg1DList &vt, const AdrPostReg &adr);
void ld3r(const VReg16BList &vt, const AdrPostReg &adr);
void ld3r(const VReg8HList &vt, const AdrPostReg &adr);
void ld3r(const VReg4SList &vt, const AdrPostReg &adr);
void ld3r(const VReg2DList &vt, const AdrPostReg &adr);
void ld2r(const VReg8BList &vt, const AdrPostReg &adr);
void ld2r(const VReg4HList &vt, const AdrPostReg &adr);
void ld2r(const VReg2SList &vt, const AdrPostReg &adr);
void ld2r(const VReg1DList &vt, const AdrPostReg &adr);
void ld2r(const VReg16BList &vt, const AdrPostReg &adr);
void ld2r(const VReg8HList &vt, const AdrPostReg &adr);
void ld2r(const VReg4SList &vt, const AdrPostReg &adr);
void ld2r(const VReg2DList &vt, const AdrPostReg &adr);
void ld1r(const VReg8BList &vt, const AdrPostReg &adr);
void ld1r(const VReg4HList &vt, const AdrPostReg &adr);
void ld1r(const VReg2SList &vt, const AdrPostReg &adr);
void ld1r(const VReg1DList &vt, const AdrPostReg &adr);
void ld1r(const VReg16BList &vt, const AdrPostReg &adr);
void ld1r(const VReg8HList &vt, const AdrPostReg &adr);
void ld1r(const VReg4SList &vt, const AdrPostReg &adr);
void ld1r(const VReg2DList &vt, const AdrPostReg &adr);
void st4(const VRegBElem &vt, const AdrPostImm &adr);
void st4(const VRegHElem &vt, const AdrPostImm &adr);
void st4(const VRegSElem &vt, const AdrPostImm &adr);
void st4(const VRegDElem &vt, const AdrPostImm &adr);
void st3(const VRegBElem &vt, const AdrPostImm &adr);
void st3(const VRegHElem &vt, const AdrPostImm &adr);
void st3(const VRegSElem &vt, const AdrPostImm &adr);
void st3(const VRegDElem &vt, const AdrPostImm &adr);
void st2(const VRegBElem &vt, const AdrPostImm &adr);
void st2(const VRegHElem &vt, const AdrPostImm &adr);
void st2(const VRegSElem &vt, const AdrPostImm &adr);
void st2(const VRegDElem &vt, const AdrPostImm &adr);
void st1(const VRegBElem &vt, const AdrPostImm &adr);
void st1(const VRegHElem &vt, const AdrPostImm &adr);
void st1(const VRegSElem &vt, const AdrPostImm &adr);
void st1(const VRegDElem &vt, const AdrPostImm &adr);
void ld4(const VRegBElem &vt, const AdrPostImm &adr);
void ld4(const VRegHElem &vt, const AdrPostImm &adr);
void ld4(const VRegSElem &vt, const AdrPostImm &adr);
void ld4(const VRegDElem &vt, const AdrPostImm &adr);
void ld3(const VRegBElem &vt, const AdrPostImm &adr);
void ld3(const VRegHElem &vt, const AdrPostImm &adr);
void ld3(const VRegSElem &vt, const AdrPostImm &adr);
void ld3(const VRegDElem &vt, const AdrPostImm &adr);
void ld2(const VRegBElem &vt, const AdrPostImm &adr);
void ld2(const VRegHElem &vt, const AdrPostImm &adr);
void ld2(const VRegSElem &vt, const AdrPostImm &adr);
void ld2(const VRegDElem &vt, const AdrPostImm &adr);
void ld1(const VRegBElem &vt, const AdrPostImm &adr);
void ld1(const VRegHElem &vt, const AdrPostImm &adr);
void ld1(const VRegSElem &vt, const AdrPostImm &adr);
void ld1(const VRegDElem &vt, const AdrPostImm &adr);
void ld4r(const VReg8BList &vt, const AdrPostImm &adr);
void ld4r(const VReg4HList &vt, const AdrPostImm &adr);
void ld4r(const VReg2SList &vt, const AdrPostImm &adr);
void ld4r(const VReg1DList &vt, const AdrPostImm &adr);
void ld4r(const VReg16BList &vt, const AdrPostImm &adr);
void ld4r(const VReg8HList &vt, const AdrPostImm &adr);
void ld4r(const VReg4SList &vt, const AdrPostImm &adr);
void ld4r(const VReg2DList &vt, const AdrPostImm &adr);
void ld3r(const VReg8BList &vt, const AdrPostImm &adr);
void ld3r(const VReg4HList &vt, const AdrPostImm &adr);
void ld3r(const VReg2SList &vt, const AdrPostImm &adr);
void ld3r(const VReg1DList &vt, const AdrPostImm &adr);
void ld3r(const VReg16BList &vt, const AdrPostImm &adr);
void ld3r(const VReg8HList &vt, const AdrPostImm &adr);
void ld3r(const VReg4SList &vt, const AdrPostImm &adr);
void ld3r(const VReg2DList &vt, const AdrPostImm &adr);
void ld2r(const VReg8BList &vt, const AdrPostImm &adr);
void ld2r(const VReg4HList &vt, const AdrPostImm &adr);
void ld2r(const VReg2SList &vt, const AdrPostImm &adr);
void ld2r(const VReg1DList &vt, const AdrPostImm &adr);
void ld2r(const VReg16BList &vt, const AdrPostImm &adr);
void ld2r(const VReg8HList &vt, const AdrPostImm &adr);
void ld2r(const VReg4SList &vt, const AdrPostImm &adr);
void ld2r(const VReg2DList &vt, const AdrPostImm &adr);
void ld1r(const VReg8BList &vt, const AdrPostImm &adr);
void ld1r(const VReg4HList &vt, const AdrPostImm &adr);
void ld1r(const VReg2SList &vt, const AdrPostImm &adr);
void ld1r(const VReg1DList &vt, const AdrPostImm &adr);
void ld1r(const VReg16BList &vt, const AdrPostImm &adr);
void ld1r(const VReg8HList &vt, const AdrPostImm &adr);
void ld1r(const VReg4SList &vt, const AdrPostImm &adr);
void ld1r(const VReg2DList &vt, const AdrPostImm &adr);
void stxrb(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stlxrb(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stxrh(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stlxrh(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stxr(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stlxr(const WReg &ws, const WReg &rt, const AdrImm &adr);
void stxr(const WReg &ws, const XReg &rt, const AdrImm &adr);
void stlxr(const WReg &ws, const XReg &rt, const AdrImm &adr);
void ldxrb(const WReg &rt, const AdrImm &adr);
void ldaxrb(const WReg &rt, const AdrImm &adr);
void ldxrh(const WReg &rt, const AdrImm &adr);
void ldaxrh(const WReg &rt, const AdrImm &adr);
void ldxr(const WReg &rt, const AdrImm &adr);
void ldaxr(const WReg &rt, const AdrImm &adr);
void ldxr(const XReg &rt, const AdrImm &adr);
void ldaxr(const XReg &rt, const AdrImm &adr);
void stllrb(const WReg &rt, const AdrImm &adr);
void stlrb(const WReg &rt, const AdrImm &adr);
void stllrh(const WReg &rt, const AdrImm &adr);
void stlrh(const WReg &rt, const AdrImm &adr);
void stllr(const WReg &rt, const AdrImm &adr);
void stlr(const WReg &rt, const AdrImm &adr);
void stllr(const XReg &rt, const AdrImm &adr);
void stlr(const XReg &rt, const AdrImm &adr);
void ldlarb(const WReg &rt, const AdrImm &adr);
void ldarb(const WReg &rt, const AdrImm &adr);
void ldlarh(const WReg &rt, const AdrImm &adr);
void ldarh(const WReg &rt, const AdrImm &adr);
void ldlar(const WReg &rt, const AdrImm &adr);
void ldar(const WReg &rt, const AdrImm &adr);
void ldlar(const XReg &rt, const AdrImm &adr);
void ldar(const XReg &rt, const AdrImm &adr);
void casb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void caslb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void cash(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void caslh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void cas(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casa(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void cas(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void casl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void casa(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void casal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void stxp(const WReg &ws, const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void stxp(const WReg &ws, const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void stlxp(const WReg &ws, const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void stlxp(const WReg &ws, const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void ldxp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void ldxp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void ldaxp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void ldaxp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void casp(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void casp(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void caspl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void caspl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void caspa(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void caspa(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void caspal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void caspal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void stlurb(const WReg &rt, const AdrImm &adr);
void ldapurb(const WReg &rt, const AdrImm &adr);
void ldapursb(const XReg &rt, const AdrImm &adr);
void ldapursb(const WReg &rt, const AdrImm &adr);
void stlurh(const WReg &rt, const AdrImm &adr);
void ldapurh(const WReg &rt, const AdrImm &adr);
void ldapursh(const XReg &rt, const AdrImm &adr);
void ldapursh(const WReg &rt, const AdrImm &adr);
void stlur(const WReg &rt, const AdrImm &adr);
void ldapur(const WReg &rt, const AdrImm &adr);
void ldapursw(const XReg &rt, const AdrImm &adr);
void stlur(const XReg &rt, const AdrImm &adr);
void ldapur(const XReg &rt, const AdrImm &adr);
void ldr(const WReg &rt, const Label &label);
void ldr(const XReg &rt, const Label &label);
void ldr(const WReg &rt, const int64_t label);
void ldr(const XReg &rt, const int64_t label);
void ldrsw(const WReg &rt, const Label &label);
void ldrsw(const XReg &rt, const Label &label);
void ldrsw(const WReg &rt, const int64_t label);
void ldrsw(const XReg &rt, const int64_t label);
void ldr(const SReg &vt, const Label &label);
void ldr(const DReg &vt, const Label &label);
void ldr(const QReg &vt, const Label &label);
void ldr(const SReg &vt, const int64_t label);
void ldr(const DReg &vt, const int64_t label);
void ldr(const QReg &vt, const int64_t label);
void prfm(const Prfop prfop, const Label &label);
void prfm(const Prfop prfop, const int64_t label);
void stnp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void stnp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void ldnp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void ldnp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void stnp(const SReg &vt1, const SReg &vt2, const AdrImm &adr);
void stnp(const DReg &vt1, const DReg &vt2, const AdrImm &adr);
void stnp(const QReg &vt1, const QReg &vt2, const AdrImm &adr);
void ldnp(const SReg &vt1, const SReg &vt2, const AdrImm &adr);
void ldnp(const DReg &vt1, const DReg &vt2, const AdrImm &adr);
void ldnp(const QReg &vt1, const QReg &vt2, const AdrImm &adr);
void stp(const WReg &rt1, const WReg &rt2, const AdrPostImm &adr);
void stp(const XReg &rt1, const XReg &rt2, const AdrPostImm &adr);
void ldp(const WReg &rt1, const WReg &rt2, const AdrPostImm &adr);
void ldp(const XReg &rt1, const XReg &rt2, const AdrPostImm &adr);
void ldpsw(const XReg &rt1, const XReg &rt2, const AdrPostImm &adr);
void stp(const SReg &vt1, const SReg &vt2, const AdrPostImm &adr);
void stp(const DReg &vt1, const DReg &vt2, const AdrPostImm &adr);
void stp(const QReg &vt1, const QReg &vt2, const AdrPostImm &adr);
void ldp(const SReg &vt1, const SReg &vt2, const AdrPostImm &adr);
void ldp(const DReg &vt1, const DReg &vt2, const AdrPostImm &adr);
void ldp(const QReg &vt1, const QReg &vt2, const AdrPostImm &adr);
void stp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void stp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void ldp(const WReg &rt1, const WReg &rt2, const AdrImm &adr);
void ldp(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void ldpsw(const XReg &rt1, const XReg &rt2, const AdrImm &adr);
void stp(const SReg &vt1, const SReg &vt2, const AdrImm &adr);
void stp(const DReg &vt1, const DReg &vt2, const AdrImm &adr);
void stp(const QReg &vt1, const QReg &vt2, const AdrImm &adr);
void ldp(const SReg &vt1, const SReg &vt2, const AdrImm &adr);
void ldp(const DReg &vt1, const DReg &vt2, const AdrImm &adr);
void ldp(const QReg &vt1, const QReg &vt2, const AdrImm &adr);
void stp(const WReg &rt1, const WReg &rt2, const AdrPreImm &adr);
void stp(const XReg &rt1, const XReg &rt2, const AdrPreImm &adr);
void ldp(const WReg &rt1, const WReg &rt2, const AdrPreImm &adr);
void ldp(const XReg &rt1, const XReg &rt2, const AdrPreImm &adr);
void ldpsw(const XReg &rt1, const XReg &rt2, const AdrPreImm &adr);
void stp(const SReg &vt1, const SReg &vt2, const AdrPreImm &adr);
void stp(const DReg &vt1, const DReg &vt2, const AdrPreImm &adr);
void stp(const QReg &vt1, const QReg &vt2, const AdrPreImm &adr);
void ldp(const SReg &vt1, const SReg &vt2, const AdrPreImm &adr);
void ldp(const DReg &vt1, const DReg &vt2, const AdrPreImm &adr);
void ldp(const QReg &vt1, const QReg &vt2, const AdrPreImm &adr);
void sturb(const WReg &rt, const AdrImm &adr);
void ldurb(const WReg &rt, const AdrImm &adr);
void ldursb(const WReg &rt, const AdrImm &adr);
void sturh(const WReg &rt, const AdrImm &adr);
void ldurh(const WReg &rt, const AdrImm &adr);
void ldursh(const WReg &rt, const AdrImm &adr);
void stur(const WReg &rt, const AdrImm &adr);
void ldur(const WReg &rt, const AdrImm &adr);
void ldursb(const XReg &rt, const AdrImm &adr);
void ldursh(const XReg &rt, const AdrImm &adr);
void ldursw(const XReg &rt, const AdrImm &adr);
void stur(const XReg &rt, const AdrImm &adr);
void ldur(const XReg &rt, const AdrImm &adr);
void stur(const BReg &vt, const AdrImm &adr);
void stur(const HReg &vt, const AdrImm &adr);
void stur(const SReg &vt, const AdrImm &adr);
void stur(const DReg &vt, const AdrImm &adr);
void stur(const QReg &vt, const AdrImm &adr);
void ldur(const BReg &vt, const AdrImm &adr);
void ldur(const HReg &vt, const AdrImm &adr);
void ldur(const SReg &vt, const AdrImm &adr);
void ldur(const DReg &vt, const AdrImm &adr);
void ldur(const QReg &vt, const AdrImm &adr);
void prfum(const Prfop prfop, const AdrImm &adr);
void strb(const WReg &rt, const AdrPostImm &adr);
void ldrb(const WReg &rt, const AdrPostImm &adr);
void ldrsb(const WReg &rt, const AdrPostImm &adr);
void strh(const WReg &rt, const AdrPostImm &adr);
void ldrh(const WReg &rt, const AdrPostImm &adr);
void ldrsh(const WReg &rt, const AdrPostImm &adr);
void str(const WReg &rt, const AdrPostImm &adr);
void ldr(const WReg &rt, const AdrPostImm &adr);
void ldrsb(const XReg &rt, const AdrPostImm &adr);
void ldrsh(const XReg &rt, const AdrPostImm &adr);
void ldrsw(const XReg &rt, const AdrPostImm &adr);
void str(const XReg &rt, const AdrPostImm &adr);
void ldr(const XReg &rt, const AdrPostImm &adr);
void str(const BReg &vt, const AdrPostImm &adr);
void str(const HReg &vt, const AdrPostImm &adr);
void str(const SReg &vt, const AdrPostImm &adr);
void str(const DReg &vt, const AdrPostImm &adr);
void str(const QReg &vt, const AdrPostImm &adr);
void ldr(const BReg &vt, const AdrPostImm &adr);
void ldr(const HReg &vt, const AdrPostImm &adr);
void ldr(const SReg &vt, const AdrPostImm &adr);
void ldr(const DReg &vt, const AdrPostImm &adr);
void ldr(const QReg &vt, const AdrPostImm &adr);
void sttrb(const WReg &rt, const AdrImm &adr);
void ldtrb(const WReg &rt, const AdrImm &adr);
void ldtrsb(const WReg &rt, const AdrImm &adr);
void sttrh(const WReg &rt, const AdrImm &adr);
void ldtrh(const WReg &rt, const AdrImm &adr);
void ldtrsh(const WReg &rt, const AdrImm &adr);
void sttr(const WReg &rt, const AdrImm &adr);
void ldtr(const WReg &rt, const AdrImm &adr);
void ldtrsb(const XReg &rt, const AdrImm &adr);
void ldtrsh(const XReg &rt, const AdrImm &adr);
void ldtrsw(const XReg &rt, const AdrImm &adr);
void sttr(const XReg &rt, const AdrImm &adr);
void ldtr(const XReg &rt, const AdrImm &adr);
void strb(const WReg &rt, const AdrPreImm &adr);
void ldrb(const WReg &rt, const AdrPreImm &adr);
void ldrsb(const WReg &rt, const AdrPreImm &adr);
void strh(const WReg &rt, const AdrPreImm &adr);
void ldrh(const WReg &rt, const AdrPreImm &adr);
void ldrsh(const WReg &rt, const AdrPreImm &adr);
void str(const WReg &rt, const AdrPreImm &adr);
void ldr(const WReg &rt, const AdrPreImm &adr);
void ldrsb(const XReg &rt, const AdrPreImm &adr);
void ldrsh(const XReg &rt, const AdrPreImm &adr);
void ldrsw(const XReg &rt, const AdrPreImm &adr);
void str(const XReg &rt, const AdrPreImm &adr);
void ldr(const XReg &rt, const AdrPreImm &adr);
void str(const BReg &vt, const AdrPreImm &adr);
void str(const HReg &vt, const AdrPreImm &adr);
void str(const SReg &vt, const AdrPreImm &adr);
void str(const DReg &vt, const AdrPreImm &adr);
void str(const QReg &vt, const AdrPreImm &adr);
void ldr(const BReg &vt, const AdrPreImm &adr);
void ldr(const HReg &vt, const AdrPreImm &adr);
void ldr(const SReg &vt, const AdrPreImm &adr);
void ldr(const DReg &vt, const AdrPreImm &adr);
void ldr(const QReg &vt, const AdrPreImm &adr);
void st64b(const XReg &rt, const AdrNoOfs &adr);
void st64bv(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void st64bv0(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ld64b(const XReg &rt, const AdrNoOfs &adr);
void ldaddb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void stumaxb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminlb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swplb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpab(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaprb(const WReg &rt, const AdrImm &adr);
void ldaddalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclralb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeoralb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpalb(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldseth(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swph(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminlh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swplh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpah(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaprh(const WReg &rt, const AdrImm &adr);
void ldaddalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclralh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeoralh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpalh(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldadd(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclr(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeor(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldset(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmax(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmin(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumax(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumin(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swp(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldaddl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclrl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeorl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpl(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldadda(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclra(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeora(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldseta(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxa(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmina(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxa(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumina(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpa(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldapr(const WReg &rt, const AdrImm &adr);
void ldaddal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldclral(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldeoral(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsetal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsmaxal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldsminal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldumaxal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void lduminal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void swpal(const WReg &rs, const WReg &rt, const AdrNoOfs &adr);
void ldadd(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldclr(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldeor(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldset(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmax(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmin(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumax(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumin(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void swp(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldaddl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldclrl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldeorl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsetl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmaxl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsminl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumaxl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void lduminl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void swpl(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldadda(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldclra(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldeora(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldseta(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmaxa(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmina(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumaxa(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumina(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void swpa(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldapr(const XReg &rt, const AdrImm &adr);
void ldaddal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldclral(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldeoral(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsetal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsmaxal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldsminal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void ldumaxal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void lduminal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void swpal(const XReg &rs, const XReg &rt, const AdrNoOfs &adr);
void staddb(const WReg &rs, const AdrNoOfs &adr);
void stclrb(const WReg &rs, const AdrNoOfs &adr);
void steorb(const WReg &rs, const AdrNoOfs &adr);
void stsetb(const WReg &rs, const AdrNoOfs &adr);
void stsmaxb(const WReg &rs, const AdrNoOfs &adr);
void stsminb(const WReg &rs, const AdrNoOfs &adr);
void stumaxb(const WReg &rs, const AdrNoOfs &adr);
void stuminb(const WReg &rs, const AdrNoOfs &adr);
void staddlb(const WReg &rs, const AdrNoOfs &adr);
void stclrlb(const WReg &rs, const AdrNoOfs &adr);
void steorlb(const WReg &rs, const AdrNoOfs &adr);
void stsetlb(const WReg &rs, const AdrNoOfs &adr);
void stsmaxlb(const WReg &rs, const AdrNoOfs &adr);
void stsminlb(const WReg &rs, const AdrNoOfs &adr);
void stumaxlb(const WReg &rs, const AdrNoOfs &adr);
void stuminlb(const WReg &rs, const AdrNoOfs &adr);
void staddh(const WReg &rs, const AdrNoOfs &adr);
void stclrh(const WReg &rs, const AdrNoOfs &adr);
void steorh(const WReg &rs, const AdrNoOfs &adr);
void stseth(const WReg &rs, const AdrNoOfs &adr);
void stsmaxh(const WReg &rs, const AdrNoOfs &adr);
void stsminh(const WReg &rs, const AdrNoOfs &adr);
void stumaxh(const WReg &rs, const AdrNoOfs &adr);
void stuminh(const WReg &rs, const AdrNoOfs &adr);
void staddlh(const WReg &rs, const AdrNoOfs &adr);
void stclrlh(const WReg &rs, const AdrNoOfs &adr);
void steorlh(const WReg &rs, const AdrNoOfs &adr);
void stsetlh(const WReg &rs, const AdrNoOfs &adr);
void stsmaxlh(const WReg &rs, const AdrNoOfs &adr);
void stsminlh(const WReg &rs, const AdrNoOfs &adr);
void stumaxlh(const WReg &rs, const AdrNoOfs &adr);
void stuminlh(const WReg &rs, const AdrNoOfs &adr);
void stadd(const WReg &rs, const AdrNoOfs &adr);
void stclr(const WReg &rs, const AdrNoOfs &adr);
void steor(const WReg &rs, const AdrNoOfs &adr);
void stset(const WReg &rs, const AdrNoOfs &adr);
void stsmax(const WReg &rs, const AdrNoOfs &adr);
void stsmin(const WReg &rs, const AdrNoOfs &adr);
void stumax(const WReg &rs, const AdrNoOfs &adr);
void stumin(const WReg &rs, const AdrNoOfs &adr);
void staddl(const WReg &rs, const AdrNoOfs &adr);
void stclrl(const WReg &rs, const AdrNoOfs &adr);
void steorl(const WReg &rs, const AdrNoOfs &adr);
void stsetl(const WReg &rs, const AdrNoOfs &adr);
void stsmaxl(const WReg &rs, const AdrNoOfs &adr);
void stsminl(const WReg &rs, const AdrNoOfs &adr);
void stumaxl(const WReg &rs, const AdrNoOfs &adr);
void stuminl(const WReg &rs, const AdrNoOfs &adr);
void stadd(const XReg &rs, const AdrNoOfs &adr);
void stclr(const XReg &rs, const AdrNoOfs &adr);
void steor(const XReg &rs, const AdrNoOfs &adr);
void stset(const XReg &rs, const AdrNoOfs &adr);
void stsmax(const XReg &rs, const AdrNoOfs &adr);
void stsmin(const XReg &rs, const AdrNoOfs &adr);
void stumax(const XReg &rs, const AdrNoOfs &adr);
void stumin(const XReg &rs, const AdrNoOfs &adr);
void staddl(const XReg &rs, const AdrNoOfs &adr);
void stclrl(const XReg &rs, const AdrNoOfs &adr);
void steorl(const XReg &rs, const AdrNoOfs &adr);
void stsetl(const XReg &rs, const AdrNoOfs &adr);
void stsmaxl(const XReg &rs, const AdrNoOfs &adr);
void stsminl(const XReg &rs, const AdrNoOfs &adr);
void stumaxl(const XReg &rs, const AdrNoOfs &adr);
void stuminl(const XReg &rs, const AdrNoOfs &adr);
void strb(const WReg &rt, const AdrReg &adr);
void strb(const WReg &rt, const AdrExt &adr);
void ldrb(const WReg &rt, const AdrReg &adr);
void ldrb(const WReg &rt, const AdrExt &adr);
void ldrsb(const XReg &rt, const AdrReg &adr);
void ldrsb(const XReg &rt, const AdrExt &adr);
void ldrsb(const WReg &rt, const AdrReg &adr);
void ldrsb(const WReg &rt, const AdrExt &adr);
void strh(const WReg &rt, const AdrReg &adr);
void strh(const WReg &rt, const AdrExt &adr);
void ldrh(const WReg &rt, const AdrReg &adr);
void ldrh(const WReg &rt, const AdrExt &adr);
void ldrsh(const XReg &rt, const AdrReg &adr);
void ldrsh(const XReg &rt, const AdrExt &adr);
void ldrsh(const WReg &rt, const AdrReg &adr);
void ldrsh(const WReg &rt, const AdrExt &adr);
void str(const WReg &rt, const AdrReg &adr);
void str(const WReg &rt, const AdrExt &adr);
void ldr(const WReg &rt, const AdrReg &adr);
void ldr(const WReg &rt, const AdrExt &adr);
void ldrsw(const XReg &rt, const AdrReg &adr);
void ldrsw(const XReg &rt, const AdrExt &adr);
void str(const XReg &rt, const AdrReg &adr);
void str(const XReg &rt, const AdrExt &adr);
void ldr(const XReg &rt, const AdrReg &adr);
void ldr(const XReg &rt, const AdrExt &adr);
void str(const BReg &vt, const AdrReg &adr);
void str(const HReg &vt, const AdrReg &adr);
void str(const SReg &vt, const AdrReg &adr);
void str(const DReg &vt, const AdrReg &adr);
void str(const QReg &vt, const AdrReg &adr);
void str(const BReg &vt, const AdrExt &adr);
void str(const HReg &vt, const AdrExt &adr);
void str(const SReg &vt, const AdrExt &adr);
void str(const DReg &vt, const AdrExt &adr);
void str(const QReg &vt, const AdrExt &adr);
void ldr(const BReg &vt, const AdrReg &adr);
void ldr(const HReg &vt, const AdrReg &adr);
void ldr(const SReg &vt, const AdrReg &adr);
void ldr(const DReg &vt, const AdrReg &adr);
void ldr(const QReg &vt, const AdrReg &adr);
void ldr(const BReg &vt, const AdrExt &adr);
void ldr(const HReg &vt, const AdrExt &adr);
void ldr(const SReg &vt, const AdrExt &adr);
void ldr(const DReg &vt, const AdrExt &adr);
void ldr(const QReg &vt, const AdrExt &adr);
void prfm(const Prfop prfop, const AdrReg &adr);
void prfm(const Prfop prfop, const AdrExt &adr);
void ldraa(const XReg &xt, const AdrImm &adr);
void ldrab(const XReg &xt, const AdrImm &adr);
void ldraa(const XReg &xt, const AdrPreImm &adr);
void ldrab(const XReg &xt, const AdrPreImm &adr);
void strb(const WReg &rt, const AdrUimm &adr);
void ldrb(const WReg &rt, const AdrUimm &adr);
void ldrsb(const XReg &rt, const AdrUimm &adr);
void ldrsb(const WReg &rt, const AdrUimm &adr);
void strh(const WReg &rt, const AdrUimm &adr);
void ldrh(const WReg &rt, const AdrUimm &adr);
void ldrsh(const XReg &rt, const AdrUimm &adr);
void ldrsh(const WReg &rt, const AdrUimm &adr);
void str(const WReg &rt, const AdrUimm &adr);
void ldr(const WReg &rt, const AdrUimm &adr);
void ldrsw(const XReg &rt, const AdrUimm &adr);
void str(const XReg &rt, const AdrUimm &adr);
void ldr(const XReg &rt, const AdrUimm &adr);
void str(const BReg &vt, const AdrUimm &adr);
void str(const HReg &vt, const AdrUimm &adr);
void str(const SReg &vt, const AdrUimm &adr);
void str(const DReg &vt, const AdrUimm &adr);
void str(const QReg &vt, const AdrUimm &adr);
void ldr(const BReg &vt, const AdrUimm &adr);
void ldr(const HReg &vt, const AdrUimm &adr);
void ldr(const SReg &vt, const AdrUimm &adr);
void ldr(const DReg &vt, const AdrUimm &adr);
void ldr(const QReg &vt, const AdrUimm &adr);
void prfm(const Prfop prfop, const AdrUimm &adr);
void udiv(const WReg &rd, const WReg &rn, const WReg &rm);
void sdiv(const WReg &rd, const WReg &rn, const WReg &rm);
void lslv(const WReg &rd, const WReg &rn, const WReg &rm);
void lsl(const WReg &rd, const WReg &rn, const WReg &rm);
void lsrv(const WReg &rd, const WReg &rn, const WReg &rm);
void lsr(const WReg &rd, const WReg &rn, const WReg &rm);
void asrv(const WReg &rd, const WReg &rn, const WReg &rm);
void asr(const WReg &rd, const WReg &rn, const WReg &rm);
void rorv(const WReg &rd, const WReg &rn, const WReg &rm);
void ror(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32b(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32h(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32w(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32cb(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32ch(const WReg &rd, const WReg &rn, const WReg &rm);
void crc32cw(const WReg &rd, const WReg &rn, const WReg &rm);
void udiv(const XReg &rd, const XReg &rn, const XReg &rm);
void sdiv(const XReg &rd, const XReg &rn, const XReg &rm);
void lslv(const XReg &rd, const XReg &rn, const XReg &rm);
void lsl(const XReg &rd, const XReg &rn, const XReg &rm);
void lsrv(const XReg &rd, const XReg &rn, const XReg &rm);
void lsr(const XReg &rd, const XReg &rn, const XReg &rm);
void asrv(const XReg &rd, const XReg &rn, const XReg &rm);
void asr(const XReg &rd, const XReg &rn, const XReg &rm);
void rorv(const XReg &rd, const XReg &rn, const XReg &rm);
void ror(const XReg &rd, const XReg &rn, const XReg &rm);
void pacga(const XReg &rd, const XReg &rn, const XReg &rm);
void crc32x(const WReg &rd, const WReg &rn, const XReg &rm);
void crc32cx(const WReg &rd, const WReg &rn, const XReg &rm);
void rbit(const WReg &rd, const WReg &rn);
void rev16(const WReg &rd, const WReg &rn);
void rev(const WReg &rd, const WReg &rn);
void clz(const WReg &rd, const WReg &rn);
void cls(const WReg &rd, const WReg &rn);
void rbit(const XReg &rd, const XReg &rn);
void rev16(const XReg &rd, const XReg &rn);
void rev32(const XReg &rd, const XReg &rn);
void rev(const XReg &rd, const XReg &rn);
void rev64(const XReg &rd, const XReg &rn);
void clz(const XReg &rd, const XReg &rn);
void cls(const XReg &rd, const XReg &rn);
void pacia(const XReg &rd, const XReg &rn);
void pacib(const XReg &rd, const XReg &rn);
void pacda(const XReg &rd, const XReg &rn);
void pacdb(const XReg &rd, const XReg &rn);
void autia(const XReg &rd, const XReg &rn);
void autib(const XReg &rd, const XReg &rn);
void autda(const XReg &rd, const XReg &rn);
void autdb(const XReg &rd, const XReg &rn);
void paciza(const XReg &rd);
void pacizb(const XReg &rd);
void pacdza(const XReg &rd);
void pacdzb(const XReg &rd);
void autiza(const XReg &rd);
void autizb(const XReg &rd);
void autdza(const XReg &rd);
void autdzb(const XReg &rd);
void xpaci(const XReg &rd);
void xpacd(const XReg &rd);
void and_(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void and_(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void bic(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void bic(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void orr(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void orr(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void orn(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void orn(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void mvn(const WReg &rd, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void mvn(const XReg &rd, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void eor(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void eor(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void eon(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void eon(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void ands(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void ands(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void tst(const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void tst(const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void bics(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void bics(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = LSL, const uint32_t sh = 0);
void mov(const WReg &rd, const WReg &rn);
void mov(const XReg &rd, const XReg &rn);
void add(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void add(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void adds(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void adds(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void cmn(const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void cmn(const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void sub(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void sub(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void neg(const WReg &rd, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void neg(const XReg &rd, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void subs(const WReg &rd, const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void subs(const XReg &rd, const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void negs(const WReg &rd, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void negs(const XReg &rd, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void cmp(const WReg &rn, const WReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void cmp(const XReg &rn, const XReg &rm, const ShMod shmod = NONE, const uint32_t sh = 0);
void add(const WReg &rd, const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void add(const XReg &rd, const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void add(const XReg &rd, const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void adds(const WReg &rd, const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void adds(const XReg &rd, const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void adds(const XReg &rd, const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmn(const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmn(const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmn(const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void sub(const WReg &rd, const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void sub(const XReg &rd, const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void sub(const XReg &rd, const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void subs(const WReg &rd, const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void subs(const XReg &rd, const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void subs(const XReg &rd, const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmp(const WReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmp(const XReg &rn, const XReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void cmp(const XReg &rn, const WReg &rm, const ExtMod extmod, const uint32_t sh = 0);
void adc(const WReg &rd, const WReg &rn, const WReg &rm);
void adc(const XReg &rd, const XReg &rn, const XReg &rm);
void adcs(const WReg &rd, const WReg &rn, const WReg &rm);
void adcs(const XReg &rd, const XReg &rn, const XReg &rm);
void sbc(const WReg &rd, const WReg &rn, const WReg &rm);
void sbc(const XReg &rd, const XReg &rn, const XReg &rm);
void ngc(const WReg &rd, const WReg &rm);
void ngc(const XReg &rd, const XReg &rm);
void sbcs(const WReg &rd, const WReg &rn, const WReg &rm);
void sbcs(const XReg &rd, const XReg &rn, const XReg &rm);
void ngcs(const WReg &rd, const WReg &rm);
void ngcs(const XReg &rd, const XReg &rm);
void rmif(const XReg &xn, const uint32_t sh, const uint32_t mask);
void setf8(const WReg &wn);
void setf16(const WReg &wn);
void ccmn(const WReg &rn, const WReg &rm, const uint32_t nczv, const Cond cond);
void ccmn(const XReg &rn, const XReg &rm, const uint32_t nczv, const Cond cond);
void ccmp(const WReg &rn, const WReg &rm, const uint32_t nczv, const Cond cond);
void ccmp(const XReg &rn, const XReg &rm, const uint32_t nczv, const Cond cond);
void ccmn(const WReg &rn, const uint32_t imm, const uint32_t nczv, const Cond cond);
void ccmn(const XReg &rn, const uint32_t imm, const uint32_t nczv, const Cond cond);
void ccmp(const WReg &rn, const uint32_t imm, const uint32_t nczv, const Cond cond);
void ccmp(const XReg &rn, const uint32_t imm, const uint32_t nczv, const Cond cond);
void csel(const WReg &rd, const WReg &rn, const WReg &rm, const Cond cond);
void csel(const XReg &rd, const XReg &rn, const XReg &rm, const Cond cond);
void csinc(const WReg &rd, const WReg &rn, const WReg &rm, const Cond cond);
void csinc(const XReg &rd, const XReg &rn, const XReg &rm, const Cond cond);
void cinc(const WReg &rd, const WReg &rn, const Cond cond);
void cinc(const XReg &rd, const XReg &rn, const Cond cond);
void cset(const WReg &rd, const Cond cond);
void cset(const XReg &rd, const Cond cond);
void csinv(const WReg &rd, const WReg &rn, const WReg &rm, const Cond cond);
void csinv(const XReg &rd, const XReg &rn, const XReg &rm, const Cond cond);
void cinv(const WReg &rd, const WReg &rn, const Cond cond);
void cinv(const XReg &rd, const XReg &rn, const Cond cond);
void csetm(const WReg &rd, const Cond cond);
void csetm(const XReg &rd, const Cond cond);
void csneg(const WReg &rd, const WReg &rn, const WReg &rm, const Cond cond);
void csneg(const XReg &rd, const XReg &rn, const XReg &rm, const Cond cond);
void cneg(const WReg &rd, const WReg &rn, const Cond cond);
void cneg(const XReg &rd, const XReg &rn, const Cond cond);
void madd(const WReg &rd, const WReg &rn, const WReg &rm, const WReg &ra);
void madd(const XReg &rd, const XReg &rn, const XReg &rm, const XReg &ra);
void mul(const WReg &rd, const WReg &rn, const WReg &rm);
void mul(const XReg &rd, const XReg &rn, const XReg &rm);
void msub(const WReg &rd, const WReg &rn, const WReg &rm, const WReg &ra);
void msub(const XReg &rd, const XReg &rn, const XReg &rm, const XReg &ra);
void mneg(const WReg &rd, const WReg &rn, const WReg &rm);
void mneg(const XReg &rd, const XReg &rn, const XReg &rm);
void smaddl(const XReg &rd, const WReg &rn, const WReg &rm, const XReg &ra);
void smull(const XReg &rd, const WReg &rn, const WReg &rm);
void smsubl(const XReg &rd, const WReg &rn, const WReg &rm, const XReg &ra);
void smnegl(const XReg &rd, const WReg &rn, const WReg &rm);
void smulh(const XReg &rd, const XReg &rn, const XReg &rm);
void umaddl(const XReg &rd, const WReg &rn, const WReg &rm, const XReg &ra);
void umull(const XReg &rd, const WReg &rn, const WReg &rm);
void umsubl(const XReg &rd, const WReg &rn, const WReg &rm, const XReg &ra);
void umnegl(const XReg &rd, const WReg &rn, const WReg &rm);
void umulh(const XReg &rd, const XReg &rn, const XReg &rm);
void aese(const VReg16B &vd, const VReg16B &vn);
void aesd(const VReg16B &vd, const VReg16B &vn);
void aesmc(const VReg16B &vd, const VReg16B &vn);
void aesimc(const VReg16B &vd, const VReg16B &vn);
void sha1c(const QReg &qd, const SReg &sn, const VReg4S &vm);
void sha1p(const QReg &qd, const SReg &sn, const VReg4S &vm);
void sha1m(const QReg &qd, const SReg &sn, const VReg4S &vm);
void sha1su0(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sha256h(const QReg &qd, const QReg &qn, const VReg4S &vm);
void sha256h2(const QReg &qd, const QReg &qn, const VReg4S &vm);
void sha256su1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sha1h(const SReg &sd, const SReg &sn);
void sha1su1(const VReg4S &vd, const VReg4S &vn);
void sha256su0(const VReg4S &vd, const VReg4S &vn);
void dup(const BReg &vd, const VRegBElem &vn);
void dup(const HReg &vd, const VRegHElem &vn);
void dup(const SReg &vd, const VRegSElem &vn);
void dup(const DReg &vd, const VRegDElem &vn);
void mov(const BReg &vd, const VRegBElem &vn);
void mov(const HReg &vd, const VRegHElem &vn);
void mov(const SReg &vd, const VRegSElem &vn);
void mov(const DReg &vd, const VRegDElem &vn);
void fmulx(const HReg &hd, const HReg &hn, const HReg &hm);
void fcmeq(const HReg &hd, const HReg &hn, const HReg &hm);
void frecps(const HReg &hd, const HReg &hn, const HReg &hm);
void frsqrts(const HReg &hd, const HReg &hn, const HReg &hm);
void fcmge(const HReg &hd, const HReg &hn, const HReg &hm);
void facge(const HReg &hd, const HReg &hn, const HReg &hm);
void fabd(const HReg &hd, const HReg &hn, const HReg &hm);
void fcmgt(const HReg &hd, const HReg &hn, const HReg &hm);
void facgt(const HReg &hd, const HReg &hn, const HReg &hm);
void fcvtns(const HReg &hd, const HReg &hn);
void fcvtms(const HReg &hd, const HReg &hn);
void fcvtas(const HReg &hd, const HReg &hn);
void scvtf(const HReg &hd, const HReg &hn);
void fcmgt(const HReg &hd, const HReg &hn, const double zero);
void fcmeq(const HReg &hd, const HReg &hn, const double zero);
void fcmlt(const HReg &hd, const HReg &hn, const double zero);
void fcvtps(const HReg &hd, const HReg &hn);
void fcvtzs(const HReg &hd, const HReg &hn);
void frecpe(const HReg &hd, const HReg &hn);
void frecpx(const HReg &hd, const HReg &hn);
void fcvtnu(const HReg &hd, const HReg &hn);
void fcvtmu(const HReg &hd, const HReg &hn);
void fcvtau(const HReg &hd, const HReg &hn);
void ucvtf(const HReg &hd, const HReg &hn);
void fcmge(const HReg &hd, const HReg &hn, const double zero);
void fcmle(const HReg &hd, const HReg &hn, const double zero);
void fcvtpu(const HReg &hd, const HReg &hn);
void fcvtzu(const HReg &hd, const HReg &hn);
void frsqrte(const HReg &hd, const HReg &hn);
void sqrdmlah(const HReg &vd, const HReg &vn, const HReg &vm);
void sqrdmlah(const SReg &vd, const SReg &vn, const SReg &vm);
void sqrdmlsh(const HReg &vd, const HReg &vn, const HReg &vm);
void sqrdmlsh(const SReg &vd, const SReg &vn, const SReg &vm);
void suqadd(const BReg &vd, const BReg &vn);
void suqadd(const HReg &vd, const HReg &vn);
void suqadd(const SReg &vd, const SReg &vn);
void suqadd(const DReg &vd, const DReg &vn);
void sqabs(const BReg &vd, const BReg &vn);
void sqabs(const HReg &vd, const HReg &vn);
void sqabs(const SReg &vd, const SReg &vn);
void sqabs(const DReg &vd, const DReg &vn);
void cmgt(const DReg &vd, const DReg &vn, const uint32_t zero);
void cmeq(const DReg &vd, const DReg &vn, const uint32_t zero);
void cmlt(const DReg &vd, const DReg &vn, const uint32_t zero);
void abs(const DReg &vd, const DReg &vn);
void sqxtn(const BReg &vd, const HReg &vn);
void sqxtn(const HReg &vd, const SReg &vn);
void sqxtn(const SReg &vd, const DReg &vn);
void usqadd(const BReg &vd, const BReg &vn);
void usqadd(const HReg &vd, const HReg &vn);
void usqadd(const SReg &vd, const SReg &vn);
void usqadd(const DReg &vd, const DReg &vn);
void sqneg(const BReg &vd, const BReg &vn);
void sqneg(const HReg &vd, const HReg &vn);
void sqneg(const SReg &vd, const SReg &vn);
void sqneg(const DReg &vd, const DReg &vn);
void cmge(const DReg &vd, const DReg &vn, const uint32_t zero);
void cmle(const DReg &vd, const DReg &vn, const uint32_t zero);
void neg(const DReg &vd, const DReg &vn);
void sqxtun(const BReg &vd, const HReg &vn);
void sqxtun(const HReg &vd, const SReg &vn);
void sqxtun(const SReg &vd, const DReg &vn);
void uqxtn(const BReg &vd, const HReg &vn);
void uqxtn(const HReg &vd, const SReg &vn);
void uqxtn(const SReg &vd, const DReg &vn);
void fcvtns(const SReg &vd, const SReg &vn);
void fcvtns(const DReg &vd, const DReg &vn);
void fcvtms(const SReg &vd, const SReg &vn);
void fcvtms(const DReg &vd, const DReg &vn);
void fcvtas(const SReg &vd, const SReg &vn);
void fcvtas(const DReg &vd, const DReg &vn);
void scvtf(const SReg &vd, const SReg &vn);
void scvtf(const DReg &vd, const DReg &vn);
void fcvtxn(const SReg &vd, const DReg &vn);
void fcvtnu(const SReg &vd, const SReg &vn);
void fcvtnu(const DReg &vd, const DReg &vn);
void fcvtmu(const SReg &vd, const SReg &vn);
void fcvtmu(const DReg &vd, const DReg &vn);
void fcvtau(const SReg &vd, const SReg &vn);
void fcvtau(const DReg &vd, const DReg &vn);
void ucvtf(const SReg &vd, const SReg &vn);
void ucvtf(const DReg &vd, const DReg &vn);
void fcmgt(const SReg &vd, const SReg &vn, const double zero);
void fcmgt(const DReg &vd, const DReg &vn, const double zero);
void fcmeq(const SReg &vd, const SReg &vn, const double zero);
void fcmeq(const DReg &vd, const DReg &vn, const double zero);
void fcmlt(const SReg &vd, const SReg &vn, const double zero);
void fcmlt(const DReg &vd, const DReg &vn, const double zero);
void fcvtps(const SReg &vd, const SReg &vn);
void fcvtps(const DReg &vd, const DReg &vn);
void fcvtzs(const SReg &vd, const SReg &vn);
void fcvtzs(const DReg &vd, const DReg &vn);
void frecpe(const SReg &vd, const SReg &vn);
void frecpe(const DReg &vd, const DReg &vn);
void frecpx(const SReg &vd, const SReg &vn);
void frecpx(const DReg &vd, const DReg &vn);
void fcmge(const SReg &vd, const SReg &vn, const double zero);
void fcmge(const DReg &vd, const DReg &vn, const double zero);
void fcmle(const SReg &vd, const SReg &vn, const double zero);
void fcmle(const DReg &vd, const DReg &vn, const double zero);
void fcvtpu(const SReg &vd, const SReg &vn);
void fcvtpu(const DReg &vd, const DReg &vn);
void fcvtzu(const SReg &vd, const SReg &vn);
void fcvtzu(const DReg &vd, const DReg &vn);
void frsqrte(const SReg &vd, const SReg &vn);
void frsqrte(const DReg &vd, const DReg &vn);
void addp(const DReg &vd, const VReg2D &vn);
void fmaxnmp(const HReg &vd, const VReg2H &vn);
void faddp(const HReg &vd, const VReg2H &vn);
void fmaxp(const HReg &vd, const VReg2H &vn);
void fminnmp(const HReg &vd, const VReg2H &vn);
void fminp(const HReg &vd, const VReg2H &vn);
void fmaxnmp(const SReg &vd, const VReg2S &vn);
void fmaxnmp(const DReg &vd, const VReg2D &vn);
void faddp(const SReg &vd, const VReg2S &vn);
void faddp(const DReg &vd, const VReg2D &vn);
void fmaxp(const SReg &vd, const VReg2S &vn);
void fmaxp(const DReg &vd, const VReg2D &vn);
void fminnmp(const SReg &vd, const VReg2S &vn);
void fminnmp(const DReg &vd, const VReg2D &vn);
void fminp(const SReg &vd, const VReg2S &vn);
void fminp(const DReg &vd, const VReg2D &vn);
void sqdmlal(const SReg &vd, const HReg &vn, const HReg &vm);
void sqdmlal(const DReg &vd, const SReg &vn, const SReg &vm);
void sqdmlsl(const SReg &vd, const HReg &vn, const HReg &vm);
void sqdmlsl(const DReg &vd, const SReg &vn, const SReg &vm);
void sqdmull(const SReg &vd, const HReg &vn, const HReg &vm);
void sqdmull(const DReg &vd, const SReg &vn, const SReg &vm);
void sqadd(const BReg &vd, const BReg &vn, const BReg &vm);
void sqadd(const HReg &vd, const HReg &vn, const HReg &vm);
void sqadd(const SReg &vd, const SReg &vn, const SReg &vm);
void sqadd(const DReg &vd, const DReg &vn, const DReg &vm);
void sqsub(const BReg &vd, const BReg &vn, const BReg &vm);
void sqsub(const HReg &vd, const HReg &vn, const HReg &vm);
void sqsub(const SReg &vd, const SReg &vn, const SReg &vm);
void sqsub(const DReg &vd, const DReg &vn, const DReg &vm);
void cmgt(const DReg &vd, const DReg &vn, const DReg &vm);
void cmge(const DReg &vd, const DReg &vn, const DReg &vm);
void sshl(const DReg &vd, const DReg &vn, const DReg &vm);
void sqshl(const BReg &vd, const BReg &vn, const BReg &vm);
void sqshl(const HReg &vd, const HReg &vn, const HReg &vm);
void sqshl(const SReg &vd, const SReg &vn, const SReg &vm);
void sqshl(const DReg &vd, const DReg &vn, const DReg &vm);
void srshl(const DReg &vd, const DReg &vn, const DReg &vm);
void sqrshl(const BReg &vd, const BReg &vn, const BReg &vm);
void sqrshl(const HReg &vd, const HReg &vn, const HReg &vm);
void sqrshl(const SReg &vd, const SReg &vn, const SReg &vm);
void sqrshl(const DReg &vd, const DReg &vn, const DReg &vm);
void add(const DReg &vd, const DReg &vn, const DReg &vm);
void cmtst(const DReg &vd, const DReg &vn, const DReg &vm);
void sqdmulh(const HReg &vd, const HReg &vn, const HReg &vm);
void sqdmulh(const SReg &vd, const SReg &vn, const SReg &vm);
void uqadd(const BReg &vd, const BReg &vn, const BReg &vm);
void uqadd(const HReg &vd, const HReg &vn, const HReg &vm);
void uqadd(const SReg &vd, const SReg &vn, const SReg &vm);
void uqadd(const DReg &vd, const DReg &vn, const DReg &vm);
void uqsub(const BReg &vd, const BReg &vn, const BReg &vm);
void uqsub(const HReg &vd, const HReg &vn, const HReg &vm);
void uqsub(const SReg &vd, const SReg &vn, const SReg &vm);
void uqsub(const DReg &vd, const DReg &vn, const DReg &vm);
void cmhi(const DReg &vd, const DReg &vn, const DReg &vm);
void cmhs(const DReg &vd, const DReg &vn, const DReg &vm);
void ushl(const DReg &vd, const DReg &vn, const DReg &vm);
void uqshl(const BReg &vd, const BReg &vn, const BReg &vm);
void uqshl(const HReg &vd, const HReg &vn, const HReg &vm);
void uqshl(const SReg &vd, const SReg &vn, const SReg &vm);
void uqshl(const DReg &vd, const DReg &vn, const DReg &vm);
void urshl(const DReg &vd, const DReg &vn, const DReg &vm);
void uqrshl(const BReg &vd, const BReg &vn, const BReg &vm);
void uqrshl(const HReg &vd, const HReg &vn, const HReg &vm);
void uqrshl(const SReg &vd, const SReg &vn, const SReg &vm);
void uqrshl(const DReg &vd, const DReg &vn, const DReg &vm);
void sub(const DReg &vd, const DReg &vn, const DReg &vm);
void cmeq(const DReg &vd, const DReg &vn, const DReg &vm);
void sqrdmulh(const HReg &vd, const HReg &vn, const HReg &vm);
void sqrdmulh(const SReg &vd, const SReg &vn, const SReg &vm);
void fmulx(const SReg &vd, const SReg &vn, const SReg &vm);
void fmulx(const DReg &vd, const DReg &vn, const DReg &vm);
void fcmeq(const SReg &vd, const SReg &vn, const SReg &vm);
void fcmeq(const DReg &vd, const DReg &vn, const DReg &vm);
void frecps(const SReg &vd, const SReg &vn, const SReg &vm);
void frecps(const DReg &vd, const DReg &vn, const DReg &vm);
void fcmge(const SReg &vd, const SReg &vn, const SReg &vm);
void fcmge(const DReg &vd, const DReg &vn, const DReg &vm);
void facge(const SReg &vd, const SReg &vn, const SReg &vm);
void facge(const DReg &vd, const DReg &vn, const DReg &vm);
void frsqrts(const SReg &vd, const SReg &vn, const SReg &vm);
void frsqrts(const DReg &vd, const DReg &vn, const DReg &vm);
void fabd(const SReg &vd, const SReg &vn, const SReg &vm);
void fabd(const DReg &vd, const DReg &vn, const DReg &vm);
void fcmgt(const SReg &vd, const SReg &vn, const SReg &vm);
void fcmgt(const DReg &vd, const DReg &vn, const DReg &vm);
void facgt(const SReg &vd, const SReg &vn, const SReg &vm);
void facgt(const DReg &vd, const DReg &vn, const DReg &vm);
void sshr(const DReg &vd, const DReg &vn, const uint32_t imm);
void ssra(const DReg &vd, const DReg &vn, const uint32_t imm);
void srshr(const DReg &vd, const DReg &vn, const uint32_t imm);
void srsra(const DReg &vd, const DReg &vn, const uint32_t imm);
void shl(const DReg &vd, const DReg &vn, const uint32_t imm);
void sqshl(const BReg &vd, const BReg &vn, const uint32_t imm);
void sqshl(const HReg &vd, const HReg &vn, const uint32_t imm);
void sqshl(const SReg &vd, const SReg &vn, const uint32_t imm);
void sqshl(const DReg &vd, const DReg &vn, const uint32_t imm);
void sqshl(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqshl(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqshl(const SReg &vd, const DReg &vn, const uint32_t imm);
void sqshrn(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqshrn(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqshrn(const SReg &vd, const DReg &vn, const uint32_t imm);
void sqrshrn(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqrshrn(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqrshrn(const SReg &vd, const DReg &vn, const uint32_t imm);
void scvtf(const HReg &vd, const HReg &vn, const uint32_t imm);
void scvtf(const SReg &vd, const SReg &vn, const uint32_t imm);
void scvtf(const DReg &vd, const DReg &vn, const uint32_t imm);
void fcvtzs(const HReg &vd, const HReg &vn, const uint32_t imm);
void fcvtzs(const SReg &vd, const SReg &vn, const uint32_t imm);
void fcvtzs(const DReg &vd, const DReg &vn, const uint32_t imm);
void ushr(const DReg &vd, const DReg &vn, const uint32_t imm);
void usra(const DReg &vd, const DReg &vn, const uint32_t imm);
void urshr(const DReg &vd, const DReg &vn, const uint32_t imm);
void ursra(const DReg &vd, const DReg &vn, const uint32_t imm);
void sri(const DReg &vd, const DReg &vn, const uint32_t imm);
void sli(const DReg &vd, const DReg &vn, const uint32_t imm);
void sqshlu(const BReg &vd, const BReg &vn, const uint32_t imm);
void sqshlu(const HReg &vd, const HReg &vn, const uint32_t imm);
void sqshlu(const SReg &vd, const SReg &vn, const uint32_t imm);
void sqshlu(const DReg &vd, const DReg &vn, const uint32_t imm);
void sqshlu(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqshlu(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqshlu(const SReg &vd, const DReg &vn, const uint32_t imm);
void uqshl(const BReg &vd, const BReg &vn, const uint32_t imm);
void uqshl(const HReg &vd, const HReg &vn, const uint32_t imm);
void uqshl(const SReg &vd, const SReg &vn, const uint32_t imm);
void uqshl(const DReg &vd, const DReg &vn, const uint32_t imm);
void uqshl(const BReg &vd, const HReg &vn, const uint32_t imm);
void uqshl(const HReg &vd, const SReg &vn, const uint32_t imm);
void uqshl(const SReg &vd, const DReg &vn, const uint32_t imm);
void sqshrun(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqshrun(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqshrun(const SReg &vd, const DReg &vn, const uint32_t imm);
void sqrshrun(const BReg &vd, const HReg &vn, const uint32_t imm);
void sqrshrun(const HReg &vd, const SReg &vn, const uint32_t imm);
void sqrshrun(const SReg &vd, const DReg &vn, const uint32_t imm);
void uqshrn(const BReg &vd, const HReg &vn, const uint32_t imm);
void uqshrn(const HReg &vd, const SReg &vn, const uint32_t imm);
void uqshrn(const SReg &vd, const DReg &vn, const uint32_t imm);
void uqrshrn(const BReg &vd, const HReg &vn, const uint32_t imm);
void uqrshrn(const HReg &vd, const SReg &vn, const uint32_t imm);
void uqrshrn(const SReg &vd, const DReg &vn, const uint32_t imm);
void ucvtf(const HReg &vd, const HReg &vn, const uint32_t imm);
void ucvtf(const SReg &vd, const SReg &vn, const uint32_t imm);
void ucvtf(const DReg &vd, const DReg &vn, const uint32_t imm);
void fcvtzu(const HReg &vd, const HReg &vn, const uint32_t imm);
void fcvtzu(const SReg &vd, const SReg &vn, const uint32_t imm);
void fcvtzu(const DReg &vd, const DReg &vn, const uint32_t imm);
void sqdmlal(const SReg &vd, const HReg &vn, const VRegHElem &vm);
void sqdmlal(const DReg &vd, const SReg &vn, const VRegSElem &vm);
void sqdmlsl(const SReg &vd, const HReg &vn, const VRegHElem &vm);
void sqdmlsl(const DReg &vd, const SReg &vn, const VRegSElem &vm);
void sqdmull(const SReg &vd, const HReg &vn, const VRegHElem &vm);
void sqdmull(const DReg &vd, const SReg &vn, const VRegSElem &vm);
void sqdmulh(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void sqdmulh(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void sqrdmulh(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void sqrdmulh(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void sqrdmlah(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void sqrdmlah(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void sqrdmlsh(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void sqrdmlsh(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void fmla(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void fmls(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void fmul(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void fmla(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void fmls(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void fmul(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void fmla(const DReg &vd, const DReg &vn, const VRegDElem &vm);
void fmls(const DReg &vd, const DReg &vn, const VRegDElem &vm);
void fmul(const DReg &vd, const DReg &vn, const VRegDElem &vm);
void fmulx(const HReg &vd, const HReg &vn, const VRegHElem &vm);
void fmulx(const SReg &vd, const SReg &vn, const VRegSElem &vm);
void fmulx(const DReg &vd, const DReg &vn, const VRegDElem &vm);
void tbl(const VReg8B &vd, const VReg16B &vn, const uint32_t len, const VReg8B &vm);
void tbl(const VReg16B &vd, const VReg16B &vn, const uint32_t len, const VReg16B &vm);
void tbx(const VReg8B &vd, const VReg16B &vn, const uint32_t len, const VReg8B &vm);
void tbx(const VReg16B &vd, const VReg16B &vn, const uint32_t len, const VReg16B &vm);
void tbl(const VReg8B &vd, const VReg16BList &vn, const VReg8B &vm);
void tbl(const VReg16B &vd, const VReg16BList &vn, const VReg16B &vm);
void tbx(const VReg8B &vd, const VReg16BList &vn, const VReg8B &vm);
void tbx(const VReg16B &vd, const VReg16BList &vn, const VReg16B &vm);
void uzp1(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uzp1(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uzp1(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uzp1(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uzp1(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uzp1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uzp1(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void trn1(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void trn1(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void trn1(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void trn1(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void trn1(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void trn1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void trn1(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void zip1(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void zip1(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void zip1(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void zip1(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void zip1(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void zip1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void zip1(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void uzp2(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uzp2(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uzp2(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uzp2(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uzp2(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uzp2(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uzp2(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void trn2(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void trn2(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void trn2(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void trn2(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void trn2(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void trn2(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void trn2(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void zip2(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void zip2(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void zip2(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void zip2(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void zip2(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void zip2(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void zip2(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void ext(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm, const uint32_t index);
void ext(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm, const uint32_t index);
void dup(const VReg8B &vd, const VRegBElem &vn);
void dup(const VReg16B &vd, const VRegBElem &vn);
void dup(const VReg4H &vd, const VRegHElem &vn);
void dup(const VReg8H &vd, const VRegHElem &vn);
void dup(const VReg2S &vd, const VRegSElem &vn);
void dup(const VReg4S &vd, const VRegSElem &vn);
void dup(const VReg2D &vd, const VRegDElem &vn);
void dup(const VReg8B &vd, const WReg &rn);
void dup(const VReg16B &vd, const WReg &rn);
void dup(const VReg4H &vd, const WReg &rn);
void dup(const VReg8H &vd, const WReg &rn);
void dup(const VReg2S &vd, const WReg &rn);
void dup(const VReg4S &vd, const WReg &rn);
void dup(const VReg2D &vd, const XReg &rn);
void smov(const WReg &rd, const VRegBElem &vn);
void smov(const WReg &rd, const VRegHElem &vn);
void smov(const XReg &rd, const VRegBElem &vn);
void smov(const XReg &rd, const VRegHElem &vn);
void smov(const XReg &rd, const VRegSElem &vn);
void umov(const WReg &rd, const VRegBElem &vn);
void umov(const WReg &rd, const VRegHElem &vn);
void umov(const WReg &rd, const VRegSElem &vn);
void umov(const XReg &rd, const VRegDElem &vn);
void mov(const WReg &rd, const VRegSElem &vn);
void mov(const XReg &rd, const VRegDElem &vn);
void ins(const VRegBElem &vd, const WReg &rn);
void ins(const VRegHElem &vd, const WReg &rn);
void ins(const VRegSElem &vd, const WReg &rn);
void ins(const VRegDElem &vd, const XReg &rn);
void mov(const VRegBElem &vd, const WReg &rn);
void mov(const VRegHElem &vd, const WReg &rn);
void mov(const VRegSElem &vd, const WReg &rn);
void mov(const VRegDElem &vd, const XReg &rn);
void ins(const VRegBElem &vd, const VRegBElem &vn);
void ins(const VRegHElem &vd, const VRegHElem &vn);
void ins(const VRegSElem &vd, const VRegSElem &vn);
void ins(const VRegDElem &vd, const VRegDElem &vn);
void mov(const VRegBElem &vd, const VRegBElem &vn);
void mov(const VRegHElem &vd, const VRegHElem &vn);
void mov(const VRegSElem &vd, const VRegSElem &vn);
void mov(const VRegDElem &vd, const VRegDElem &vn);
void fmaxnm(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmaxnm(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmla(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmla(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmulx(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmulx(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fcmeq(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fcmeq(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmax(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmax(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void frecps(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void frecps(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fminnm(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fminnm(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmls(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmls(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fsub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fsub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmin(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmin(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void frsqrts(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void frsqrts(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmaxnmp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmaxnmp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void faddp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void faddp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmul(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmul(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fcmge(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fcmge(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void facge(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void facge(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fmaxp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fmaxp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fdiv(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fdiv(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fminnmp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fminnmp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fabd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fabd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fcmgt(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fcmgt(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void facgt(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void facgt(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void fminp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void fminp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void frintn(const VReg4H &vd, const VReg4H &vn);
void frintn(const VReg8H &vd, const VReg8H &vn);
void frintm(const VReg4H &vd, const VReg4H &vn);
void frintm(const VReg8H &vd, const VReg8H &vn);
void fcvtns(const VReg4H &vd, const VReg4H &vn);
void fcvtns(const VReg8H &vd, const VReg8H &vn);
void fcvtms(const VReg4H &vd, const VReg4H &vn);
void fcvtms(const VReg8H &vd, const VReg8H &vn);
void fcvtas(const VReg4H &vd, const VReg4H &vn);
void fcvtas(const VReg8H &vd, const VReg8H &vn);
void scvtf(const VReg4H &vd, const VReg4H &vn);
void scvtf(const VReg8H &vd, const VReg8H &vn);
void fcmgt(const VReg4H &vd, const VReg4H &vn, const double zero);
void fcmgt(const VReg8H &vd, const VReg8H &vn, const double zero);
void fcmeq(const VReg4H &vd, const VReg4H &vn, const double zero);
void fcmeq(const VReg8H &vd, const VReg8H &vn, const double zero);
void fcmlt(const VReg4H &vd, const VReg4H &vn, const double zero);
void fcmlt(const VReg8H &vd, const VReg8H &vn, const double zero);
void fabs(const VReg4H &vd, const VReg4H &vn);
void fabs(const VReg8H &vd, const VReg8H &vn);
void frintp(const VReg4H &vd, const VReg4H &vn);
void frintp(const VReg8H &vd, const VReg8H &vn);
void frintz(const VReg4H &vd, const VReg4H &vn);
void frintz(const VReg8H &vd, const VReg8H &vn);
void fcvtps(const VReg4H &vd, const VReg4H &vn);
void fcvtps(const VReg8H &vd, const VReg8H &vn);
void fcvtzs(const VReg4H &vd, const VReg4H &vn);
void fcvtzs(const VReg8H &vd, const VReg8H &vn);
void frecpe(const VReg4H &vd, const VReg4H &vn);
void frecpe(const VReg8H &vd, const VReg8H &vn);
void frinta(const VReg4H &vd, const VReg4H &vn);
void frinta(const VReg8H &vd, const VReg8H &vn);
void frintx(const VReg4H &vd, const VReg4H &vn);
void frintx(const VReg8H &vd, const VReg8H &vn);
void fcvtnu(const VReg4H &vd, const VReg4H &vn);
void fcvtnu(const VReg8H &vd, const VReg8H &vn);
void fcvtmu(const VReg4H &vd, const VReg4H &vn);
void fcvtmu(const VReg8H &vd, const VReg8H &vn);
void fcvtau(const VReg4H &vd, const VReg4H &vn);
void fcvtau(const VReg8H &vd, const VReg8H &vn);
void ucvtf(const VReg4H &vd, const VReg4H &vn);
void ucvtf(const VReg8H &vd, const VReg8H &vn);
void fcmge(const VReg4H &vd, const VReg4H &vn, const double zero);
void fcmge(const VReg8H &vd, const VReg8H &vn, const double zero);
void fcmle(const VReg4H &vd, const VReg4H &vn, const double zero);
void fcmle(const VReg8H &vd, const VReg8H &vn, const double zero);
void fneg(const VReg4H &vd, const VReg4H &vn);
void fneg(const VReg8H &vd, const VReg8H &vn);
void frinti(const VReg4H &vd, const VReg4H &vn);
void frinti(const VReg8H &vd, const VReg8H &vn);
void fcvtpu(const VReg4H &vd, const VReg4H &vn);
void fcvtpu(const VReg8H &vd, const VReg8H &vn);
void fcvtzu(const VReg4H &vd, const VReg4H &vn);
void fcvtzu(const VReg8H &vd, const VReg8H &vn);
void frsqrte(const VReg4H &vd, const VReg4H &vn);
void frsqrte(const VReg8H &vd, const VReg8H &vn);
void fsqrt(const VReg4H &vd, const VReg4H &vn);
void fsqrt(const VReg8H &vd, const VReg8H &vn);
void sdot(const VReg2S &vd, const VReg8B &vn, const VReg8B &vm);
void sdot(const VReg4S &vd, const VReg16B &vn, const VReg16B &vm);
void sqrdmlah(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqrdmlah(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqrdmlah(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqrdmlah(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqrdmlsh(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqrdmlsh(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqrdmlsh(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqrdmlsh(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void udot(const VReg2S &vd, const VReg8B &vn, const VReg8B &vm);
void udot(const VReg4S &vd, const VReg16B &vn, const VReg16B &vm);
void fcmla(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm, const uint32_t rotate);
void fcmla(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm, const uint32_t rotate);
void fcmla(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm, const uint32_t rotate);
void fcmla(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm, const uint32_t rotate);
void fcmla(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm, const uint32_t rotate);
void fcadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm, const uint32_t rotate);
void fcadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm, const uint32_t rotate);
void fcadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm, const uint32_t rotate);
void fcadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm, const uint32_t rotate);
void fcadd(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm, const uint32_t rotate);
void rev64(const VReg8B &vd, const VReg8B &vn);
void rev64(const VReg4H &vd, const VReg4H &vn);
void rev64(const VReg2S &vd, const VReg2S &vn);
void rev64(const VReg16B &vd, const VReg16B &vn);
void rev64(const VReg8H &vd, const VReg8H &vn);
void rev64(const VReg4S &vd, const VReg4S &vn);
void rev16(const VReg8B &vd, const VReg8B &vn);
void rev16(const VReg16B &vd, const VReg16B &vn);
void saddlp(const VReg4H &vd, const VReg8B &vn);
void saddlp(const VReg8H &vd, const VReg16B &vn);
void saddlp(const VReg2S &vd, const VReg4H &vn);
void saddlp(const VReg4S &vd, const VReg8H &vn);
void saddlp(const VReg1D &vd, const VReg2S &vn);
void saddlp(const VReg2D &vd, const VReg4S &vn);
void suqadd(const VReg8B &vd, const VReg8B &vn);
void suqadd(const VReg4H &vd, const VReg4H &vn);
void suqadd(const VReg2S &vd, const VReg2S &vn);
void suqadd(const VReg16B &vd, const VReg16B &vn);
void suqadd(const VReg8H &vd, const VReg8H &vn);
void suqadd(const VReg4S &vd, const VReg4S &vn);
void suqadd(const VReg2D &vd, const VReg2D &vn);
void cls(const VReg8B &vd, const VReg8B &vn);
void cls(const VReg4H &vd, const VReg4H &vn);
void cls(const VReg2S &vd, const VReg2S &vn);
void cls(const VReg16B &vd, const VReg16B &vn);
void cls(const VReg8H &vd, const VReg8H &vn);
void cls(const VReg4S &vd, const VReg4S &vn);
void cnt(const VReg8B &vd, const VReg8B &vn);
void cnt(const VReg16B &vd, const VReg16B &vn);
void sadalp(const VReg4H &vd, const VReg8B &vn);
void sadalp(const VReg8H &vd, const VReg16B &vn);
void sadalp(const VReg2S &vd, const VReg4H &vn);
void sadalp(const VReg4S &vd, const VReg8H &vn);
void sadalp(const VReg1D &vd, const VReg2S &vn);
void sadalp(const VReg2D &vd, const VReg4S &vn);
void sqabs(const VReg8B &vd, const VReg8B &vn);
void sqabs(const VReg4H &vd, const VReg4H &vn);
void sqabs(const VReg2S &vd, const VReg2S &vn);
void sqabs(const VReg16B &vd, const VReg16B &vn);
void sqabs(const VReg8H &vd, const VReg8H &vn);
void sqabs(const VReg4S &vd, const VReg4S &vn);
void sqabs(const VReg2D &vd, const VReg2D &vn);
void abs(const VReg8B &vd, const VReg8B &vn);
void abs(const VReg4H &vd, const VReg4H &vn);
void abs(const VReg2S &vd, const VReg2S &vn);
void abs(const VReg16B &vd, const VReg16B &vn);
void abs(const VReg8H &vd, const VReg8H &vn);
void abs(const VReg4S &vd, const VReg4S &vn);
void abs(const VReg2D &vd, const VReg2D &vn);
void xtn(const VReg8B &vd, const VReg8H &vn);
void xtn(const VReg4H &vd, const VReg4S &vn);
void xtn(const VReg2S &vd, const VReg2D &vn);
void xtn2(const VReg16B &vd, const VReg8H &vn);
void xtn2(const VReg8H &vd, const VReg4S &vn);
void xtn2(const VReg4S &vd, const VReg2D &vn);
void sqxtn(const VReg8B &vd, const VReg8H &vn);
void sqxtn(const VReg4H &vd, const VReg4S &vn);
void sqxtn(const VReg2S &vd, const VReg2D &vn);
void sqxtn2(const VReg16B &vd, const VReg8H &vn);
void sqxtn2(const VReg8H &vd, const VReg4S &vn);
void sqxtn2(const VReg4S &vd, const VReg2D &vn);
void bfcvtn(const VReg4H &vd, const VReg4S &vn);
void bfcvtn2(const VReg8H &vd, const VReg4S &vn);
void rev32(const VReg8B &vd, const VReg8B &vn);
void rev32(const VReg4H &vd, const VReg4H &vn);
void rev32(const VReg16B &vd, const VReg16B &vn);
void rev32(const VReg8H &vd, const VReg8H &vn);
void uaddlp(const VReg4H &vd, const VReg8B &vn);
void uaddlp(const VReg8H &vd, const VReg16B &vn);
void uaddlp(const VReg2S &vd, const VReg4H &vn);
void uaddlp(const VReg4S &vd, const VReg8H &vn);
void uaddlp(const VReg1D &vd, const VReg2S &vn);
void uaddlp(const VReg2D &vd, const VReg4S &vn);
void usqadd(const VReg8B &vd, const VReg8B &vn);
void usqadd(const VReg4H &vd, const VReg4H &vn);
void usqadd(const VReg2S &vd, const VReg2S &vn);
void usqadd(const VReg16B &vd, const VReg16B &vn);
void usqadd(const VReg8H &vd, const VReg8H &vn);
void usqadd(const VReg4S &vd, const VReg4S &vn);
void usqadd(const VReg2D &vd, const VReg2D &vn);
void clz(const VReg8B &vd, const VReg8B &vn);
void clz(const VReg4H &vd, const VReg4H &vn);
void clz(const VReg2S &vd, const VReg2S &vn);
void clz(const VReg16B &vd, const VReg16B &vn);
void clz(const VReg8H &vd, const VReg8H &vn);
void clz(const VReg4S &vd, const VReg4S &vn);
void uadalp(const VReg4H &vd, const VReg8B &vn);
void uadalp(const VReg8H &vd, const VReg16B &vn);
void uadalp(const VReg2S &vd, const VReg4H &vn);
void uadalp(const VReg4S &vd, const VReg8H &vn);
void uadalp(const VReg1D &vd, const VReg2S &vn);
void uadalp(const VReg2D &vd, const VReg4S &vn);
void sqneg(const VReg8B &vd, const VReg8B &vn);
void sqneg(const VReg4H &vd, const VReg4H &vn);
void sqneg(const VReg2S &vd, const VReg2S &vn);
void sqneg(const VReg16B &vd, const VReg16B &vn);
void sqneg(const VReg8H &vd, const VReg8H &vn);
void sqneg(const VReg4S &vd, const VReg4S &vn);
void sqneg(const VReg2D &vd, const VReg2D &vn);
void neg(const VReg8B &vd, const VReg8B &vn);
void neg(const VReg4H &vd, const VReg4H &vn);
void neg(const VReg2S &vd, const VReg2S &vn);
void neg(const VReg16B &vd, const VReg16B &vn);
void neg(const VReg8H &vd, const VReg8H &vn);
void neg(const VReg4S &vd, const VReg4S &vn);
void neg(const VReg2D &vd, const VReg2D &vn);
void sqxtun(const VReg8B &vd, const VReg8H &vn);
void sqxtun(const VReg4H &vd, const VReg4S &vn);
void sqxtun(const VReg2S &vd, const VReg2D &vn);
void sqxtun2(const VReg16B &vd, const VReg8H &vn);
void sqxtun2(const VReg8H &vd, const VReg4S &vn);
void sqxtun2(const VReg4S &vd, const VReg2D &vn);
void shll(const VReg8H &vd, const VReg8B &vn, const uint32_t sh);
void shll(const VReg4S &vd, const VReg4H &vn, const uint32_t sh);
void shll(const VReg2D &vd, const VReg2S &vn, const uint32_t sh);
void shll2(const VReg8H &vd, const VReg16B &vn, const uint32_t sh);
void shll2(const VReg4S &vd, const VReg8H &vn, const uint32_t sh);
void shll2(const VReg2D &vd, const VReg4S &vn, const uint32_t sh);
void uqxtn(const VReg8B &vd, const VReg8H &vn);
void uqxtn(const VReg4H &vd, const VReg4S &vn);
void uqxtn(const VReg2S &vd, const VReg2D &vn);
void uqxtn2(const VReg16B &vd, const VReg8H &vn);
void uqxtn2(const VReg8H &vd, const VReg4S &vn);
void uqxtn2(const VReg4S &vd, const VReg2D &vn);
void cmgt(const VReg8B &vd, const VReg8B &vn, const uint32_t zero);
void cmgt(const VReg4H &vd, const VReg4H &vn, const uint32_t zero);
void cmgt(const VReg2S &vd, const VReg2S &vn, const uint32_t zero);
void cmgt(const VReg16B &vd, const VReg16B &vn, const uint32_t zero);
void cmgt(const VReg8H &vd, const VReg8H &vn, const uint32_t zero);
void cmgt(const VReg4S &vd, const VReg4S &vn, const uint32_t zero);
void cmgt(const VReg2D &vd, const VReg2D &vn, const uint32_t zero);
void cmeq(const VReg8B &vd, const VReg8B &vn, const uint32_t zero);
void cmeq(const VReg4H &vd, const VReg4H &vn, const uint32_t zero);
void cmeq(const VReg2S &vd, const VReg2S &vn, const uint32_t zero);
void cmeq(const VReg16B &vd, const VReg16B &vn, const uint32_t zero);
void cmeq(const VReg8H &vd, const VReg8H &vn, const uint32_t zero);
void cmeq(const VReg4S &vd, const VReg4S &vn, const uint32_t zero);
void cmeq(const VReg2D &vd, const VReg2D &vn, const uint32_t zero);
void cmlt(const VReg8B &vd, const VReg8B &vn, const uint32_t zero);
void cmlt(const VReg4H &vd, const VReg4H &vn, const uint32_t zero);
void cmlt(const VReg2S &vd, const VReg2S &vn, const uint32_t zero);
void cmlt(const VReg16B &vd, const VReg16B &vn, const uint32_t zero);
void cmlt(const VReg8H &vd, const VReg8H &vn, const uint32_t zero);
void cmlt(const VReg4S &vd, const VReg4S &vn, const uint32_t zero);
void cmlt(const VReg2D &vd, const VReg2D &vn, const uint32_t zero);
void cmge(const VReg8B &vd, const VReg8B &vn, const uint32_t zero);
void cmge(const VReg4H &vd, const VReg4H &vn, const uint32_t zero);
void cmge(const VReg2S &vd, const VReg2S &vn, const uint32_t zero);
void cmge(const VReg16B &vd, const VReg16B &vn, const uint32_t zero);
void cmge(const VReg8H &vd, const VReg8H &vn, const uint32_t zero);
void cmge(const VReg4S &vd, const VReg4S &vn, const uint32_t zero);
void cmge(const VReg2D &vd, const VReg2D &vn, const uint32_t zero);
void cmle(const VReg8B &vd, const VReg8B &vn, const uint32_t zero);
void cmle(const VReg4H &vd, const VReg4H &vn, const uint32_t zero);
void cmle(const VReg2S &vd, const VReg2S &vn, const uint32_t zero);
void cmle(const VReg16B &vd, const VReg16B &vn, const uint32_t zero);
void cmle(const VReg8H &vd, const VReg8H &vn, const uint32_t zero);
void cmle(const VReg4S &vd, const VReg4S &vn, const uint32_t zero);
void cmle(const VReg2D &vd, const VReg2D &vn, const uint32_t zero);
void not_(const VReg8B &vd, const VReg8B &vn);
void not_(const VReg16B &vd, const VReg16B &vn);
void mvn(const VReg8B &vd, const VReg8B &vn);
void mvn(const VReg16B &vd, const VReg16B &vn);
void rbit(const VReg8B &vd, const VReg8B &vn);
void rbit(const VReg16B &vd, const VReg16B &vn);
void fcvtn(const VReg4H &vd, const VReg4S &vn);
void fcvtn(const VReg2S &vd, const VReg2D &vn);
void fcvtn2(const VReg8H &vd, const VReg4S &vn);
void fcvtn2(const VReg4S &vd, const VReg2D &vn);
void fcvtl(const VReg4S &vd, const VReg4H &vn);
void fcvtl(const VReg2D &vd, const VReg2S &vn);
void fcvtl2(const VReg4S &vd, const VReg8H &vn);
void fcvtl2(const VReg2D &vd, const VReg4S &vn);
void frintn(const VReg2S &vd, const VReg2S &vn);
void frintn(const VReg4S &vd, const VReg4S &vn);
void frintn(const VReg2D &vd, const VReg2D &vn);
void frintm(const VReg2S &vd, const VReg2S &vn);
void frintm(const VReg4S &vd, const VReg4S &vn);
void frintm(const VReg2D &vd, const VReg2D &vn);
void fcvtns(const VReg2S &vd, const VReg2S &vn);
void fcvtns(const VReg4S &vd, const VReg4S &vn);
void fcvtns(const VReg2D &vd, const VReg2D &vn);
void fcvtms(const VReg2S &vd, const VReg2S &vn);
void fcvtms(const VReg4S &vd, const VReg4S &vn);
void fcvtms(const VReg2D &vd, const VReg2D &vn);
void fcvtas(const VReg2S &vd, const VReg2S &vn);
void fcvtas(const VReg4S &vd, const VReg4S &vn);
void fcvtas(const VReg2D &vd, const VReg2D &vn);
void scvtf(const VReg2S &vd, const VReg2S &vn);
void scvtf(const VReg4S &vd, const VReg4S &vn);
void scvtf(const VReg2D &vd, const VReg2D &vn);
void fcvtxn(const VReg2S &vd, const VReg2D &vn);
void fcvtxn(const VReg4S &vd, const VReg2D &vn);
void fcvtxn2(const VReg2S &vd, const VReg2D &vn);
void fcvtxn2(const VReg4S &vd, const VReg2D &vn);
void frinta(const VReg2S &vd, const VReg2S &vn);
void frinta(const VReg4S &vd, const VReg4S &vn);
void frinta(const VReg2D &vd, const VReg2D &vn);
void frintx(const VReg2S &vd, const VReg2S &vn);
void frintx(const VReg4S &vd, const VReg4S &vn);
void frintx(const VReg2D &vd, const VReg2D &vn);
void fcvtnu(const VReg2S &vd, const VReg2S &vn);
void fcvtnu(const VReg4S &vd, const VReg4S &vn);
void fcvtnu(const VReg2D &vd, const VReg2D &vn);
void fcvtmu(const VReg2S &vd, const VReg2S &vn);
void fcvtmu(const VReg4S &vd, const VReg4S &vn);
void fcvtmu(const VReg2D &vd, const VReg2D &vn);
void fcvtau(const VReg2S &vd, const VReg2S &vn);
void fcvtau(const VReg4S &vd, const VReg4S &vn);
void fcvtau(const VReg2D &vd, const VReg2D &vn);
void ucvtf(const VReg2S &vd, const VReg2S &vn);
void ucvtf(const VReg4S &vd, const VReg4S &vn);
void ucvtf(const VReg2D &vd, const VReg2D &vn);
void fcmgt(const VReg2S &vd, const VReg2S &vn, const double zero);
void fcmgt(const VReg4S &vd, const VReg4S &vn, const double zero);
void fcmgt(const VReg2D &vd, const VReg2D &vn, const double zero);
void fcmeq(const VReg2S &vd, const VReg2S &vn, const double zero);
void fcmeq(const VReg4S &vd, const VReg4S &vn, const double zero);
void fcmeq(const VReg2D &vd, const VReg2D &vn, const double zero);
void fcmlt(const VReg2S &vd, const VReg2S &vn, const double zero);
void fcmlt(const VReg4S &vd, const VReg4S &vn, const double zero);
void fcmlt(const VReg2D &vd, const VReg2D &vn, const double zero);
void fabs(const VReg2S &vd, const VReg2S &vn);
void fabs(const VReg4S &vd, const VReg4S &vn);
void fabs(const VReg2D &vd, const VReg2D &vn);
void frintp(const VReg2S &vd, const VReg2S &vn);
void frintp(const VReg4S &vd, const VReg4S &vn);
void frintp(const VReg2D &vd, const VReg2D &vn);
void frintz(const VReg2S &vd, const VReg2S &vn);
void frintz(const VReg4S &vd, const VReg4S &vn);
void frintz(const VReg2D &vd, const VReg2D &vn);
void fcvtps(const VReg2S &vd, const VReg2S &vn);
void fcvtps(const VReg4S &vd, const VReg4S &vn);
void fcvtps(const VReg2D &vd, const VReg2D &vn);
void fcvtzs(const VReg2S &vd, const VReg2S &vn);
void fcvtzs(const VReg4S &vd, const VReg4S &vn);
void fcvtzs(const VReg2D &vd, const VReg2D &vn);
void urecpe(const VReg2S &vd, const VReg2S &vn);
void urecpe(const VReg4S &vd, const VReg4S &vn);
void frecpe(const VReg2S &vd, const VReg2S &vn);
void frecpe(const VReg4S &vd, const VReg4S &vn);
void frecpe(const VReg2D &vd, const VReg2D &vn);
void fcmge(const VReg2S &vd, const VReg2S &vn, const double zero);
void fcmge(const VReg4S &vd, const VReg4S &vn, const double zero);
void fcmge(const VReg2D &vd, const VReg2D &vn, const double zero);
void fcmle(const VReg2S &vd, const VReg2S &vn, const double zero);
void fcmle(const VReg4S &vd, const VReg4S &vn, const double zero);
void fcmle(const VReg2D &vd, const VReg2D &vn, const double zero);
void fneg(const VReg2S &vd, const VReg2S &vn);
void fneg(const VReg4S &vd, const VReg4S &vn);
void fneg(const VReg2D &vd, const VReg2D &vn);
void frinti(const VReg2S &vd, const VReg2S &vn);
void frinti(const VReg4S &vd, const VReg4S &vn);
void frinti(const VReg2D &vd, const VReg2D &vn);
void fcvtpu(const VReg2S &vd, const VReg2S &vn);
void fcvtpu(const VReg4S &vd, const VReg4S &vn);
void fcvtpu(const VReg2D &vd, const VReg2D &vn);
void fcvtzu(const VReg2S &vd, const VReg2S &vn);
void fcvtzu(const VReg4S &vd, const VReg4S &vn);
void fcvtzu(const VReg2D &vd, const VReg2D &vn);
void ursqrte(const VReg2S &vd, const VReg2S &vn);
void ursqrte(const VReg4S &vd, const VReg4S &vn);
void frsqrte(const VReg2S &vd, const VReg2S &vn);
void frsqrte(const VReg4S &vd, const VReg4S &vn);
void frsqrte(const VReg2D &vd, const VReg2D &vn);
void fsqrt(const VReg2S &vd, const VReg2S &vn);
void fsqrt(const VReg4S &vd, const VReg4S &vn);
void fsqrt(const VReg2D &vd, const VReg2D &vn);
void saddlv(const HReg &vd, const VReg8B &vn);
void saddlv(const HReg &vd, const VReg16B &vn);
void saddlv(const SReg &vd, const VReg4H &vn);
void saddlv(const SReg &vd, const VReg8H &vn);
void saddlv(const DReg &vd, const VReg4S &vn);
void smaxv(const BReg &vd, const VReg8B &vn);
void smaxv(const BReg &vd, const VReg16B &vn);
void smaxv(const HReg &vd, const VReg4H &vn);
void smaxv(const HReg &vd, const VReg8H &vn);
void smaxv(const SReg &vd, const VReg4S &vn);
void sminv(const BReg &vd, const VReg8B &vn);
void sminv(const BReg &vd, const VReg16B &vn);
void sminv(const HReg &vd, const VReg4H &vn);
void sminv(const HReg &vd, const VReg8H &vn);
void sminv(const SReg &vd, const VReg4S &vn);
void addv(const BReg &vd, const VReg8B &vn);
void addv(const BReg &vd, const VReg16B &vn);
void addv(const HReg &vd, const VReg4H &vn);
void addv(const HReg &vd, const VReg8H &vn);
void addv(const SReg &vd, const VReg4S &vn);
void uaddlv(const HReg &vd, const VReg8B &vn);
void uaddlv(const HReg &vd, const VReg16B &vn);
void uaddlv(const SReg &vd, const VReg4H &vn);
void uaddlv(const SReg &vd, const VReg8H &vn);
void uaddlv(const DReg &vd, const VReg4S &vn);
void umaxv(const BReg &vd, const VReg8B &vn);
void umaxv(const BReg &vd, const VReg16B &vn);
void umaxv(const HReg &vd, const VReg4H &vn);
void umaxv(const HReg &vd, const VReg8H &vn);
void umaxv(const SReg &vd, const VReg4S &vn);
void uminv(const BReg &vd, const VReg8B &vn);
void uminv(const BReg &vd, const VReg16B &vn);
void uminv(const HReg &vd, const VReg4H &vn);
void uminv(const HReg &vd, const VReg8H &vn);
void uminv(const SReg &vd, const VReg4S &vn);
void fmaxnmv(const HReg &vd, const VReg4H &vn);
void fmaxnmv(const HReg &vd, const VReg8H &vn);
void fmaxv(const HReg &vd, const VReg4H &vn);
void fmaxv(const HReg &vd, const VReg8H &vn);
void fmaxnmv(const SReg &vd, const VReg4S &vn);
void fmaxv(const SReg &vd, const VReg4S &vn);
void fminnmv(const HReg &vd, const VReg4H &vn);
void fminnmv(const HReg &vd, const VReg8H &vn);
void fminv(const HReg &vd, const VReg4H &vn);
void fminv(const HReg &vd, const VReg8H &vn);
void fminnmv(const SReg &vd, const VReg4S &vn);
void fminv(const SReg &vd, const VReg4S &vn);
void saddl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void saddl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void saddl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void saddl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void saddl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void saddl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void saddw(const VReg8H &vd, const VReg8H &vn, const VReg8B &vm);
void saddw(const VReg4S &vd, const VReg4S &vn, const VReg4H &vm);
void saddw(const VReg2D &vd, const VReg2D &vn, const VReg2S &vm);
void saddw2(const VReg8H &vd, const VReg8H &vn, const VReg16B &vm);
void saddw2(const VReg4S &vd, const VReg4S &vn, const VReg8H &vm);
void saddw2(const VReg2D &vd, const VReg2D &vn, const VReg4S &vm);
void ssubl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void ssubl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void ssubl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void ssubl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void ssubl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void ssubl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void ssubw(const VReg8H &vd, const VReg8H &vn, const VReg8B &vm);
void ssubw(const VReg4S &vd, const VReg4S &vn, const VReg4H &vm);
void ssubw(const VReg2D &vd, const VReg2D &vn, const VReg2S &vm);
void ssubw2(const VReg8H &vd, const VReg8H &vn, const VReg16B &vm);
void ssubw2(const VReg4S &vd, const VReg4S &vn, const VReg8H &vm);
void ssubw2(const VReg2D &vd, const VReg2D &vn, const VReg4S &vm);
void addhn(const VReg8B &vd, const VReg8H &vn, const VReg8H &vm);
void addhn(const VReg4H &vd, const VReg4S &vn, const VReg4S &vm);
void addhn(const VReg2S &vd, const VReg2D &vn, const VReg2D &vm);
void addhn2(const VReg16B &vd, const VReg8H &vn, const VReg8H &vm);
void addhn2(const VReg8H &vd, const VReg4S &vn, const VReg4S &vm);
void addhn2(const VReg4S &vd, const VReg2D &vn, const VReg2D &vm);
void sabal(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void sabal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void sabal(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void sabal2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void sabal2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void sabal2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void subhn(const VReg8B &vd, const VReg8H &vn, const VReg8H &vm);
void subhn(const VReg4H &vd, const VReg4S &vn, const VReg4S &vm);
void subhn(const VReg2S &vd, const VReg2D &vn, const VReg2D &vm);
void subhn2(const VReg16B &vd, const VReg8H &vn, const VReg8H &vm);
void subhn2(const VReg8H &vd, const VReg4S &vn, const VReg4S &vm);
void subhn2(const VReg4S &vd, const VReg2D &vn, const VReg2D &vm);
void sabdl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void sabdl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void sabdl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void sabdl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void sabdl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void sabdl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void smlal(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void smlal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void smlal(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void smlal2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void smlal2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void smlal2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void sqdmlal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void sqdmlal(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void sqdmlal2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void sqdmlal2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void smlsl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void smlsl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void smlsl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void smlsl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void smlsl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void smlsl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void sqdmlsl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void sqdmlsl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void sqdmlsl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void sqdmlsl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void smull(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void smull(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void smull(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void smull2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void smull2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void smull2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void sqdmull(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void sqdmull(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void sqdmull2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void sqdmull2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void pmull(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void pmull(const VReg1Q &vd, const VReg1D &vn, const VReg1D &vm);
void pmull2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void pmull2(const VReg1Q &vd, const VReg2D &vn, const VReg2D &vm);
void uaddl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void uaddl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void uaddl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void uaddl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void uaddl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void uaddl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void uaddw(const VReg8H &vd, const VReg8H &vn, const VReg8B &vm);
void uaddw(const VReg4S &vd, const VReg4S &vn, const VReg4H &vm);
void uaddw(const VReg2D &vd, const VReg2D &vn, const VReg2S &vm);
void uaddw2(const VReg8H &vd, const VReg8H &vn, const VReg16B &vm);
void uaddw2(const VReg4S &vd, const VReg4S &vn, const VReg8H &vm);
void uaddw2(const VReg2D &vd, const VReg2D &vn, const VReg4S &vm);
void usubl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void usubl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void usubl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void usubl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void usubl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void usubl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void usubw(const VReg8H &vd, const VReg8H &vn, const VReg8B &vm);
void usubw(const VReg4S &vd, const VReg4S &vn, const VReg4H &vm);
void usubw(const VReg2D &vd, const VReg2D &vn, const VReg2S &vm);
void usubw2(const VReg8H &vd, const VReg8H &vn, const VReg16B &vm);
void usubw2(const VReg4S &vd, const VReg4S &vn, const VReg8H &vm);
void usubw2(const VReg2D &vd, const VReg2D &vn, const VReg4S &vm);
void raddhn(const VReg8B &vd, const VReg8H &vn, const VReg8H &vm);
void raddhn(const VReg4H &vd, const VReg4S &vn, const VReg4S &vm);
void raddhn(const VReg2S &vd, const VReg2D &vn, const VReg2D &vm);
void raddhn2(const VReg16B &vd, const VReg8H &vn, const VReg8H &vm);
void raddhn2(const VReg8H &vd, const VReg4S &vn, const VReg4S &vm);
void raddhn2(const VReg4S &vd, const VReg2D &vn, const VReg2D &vm);
void uabal(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void uabal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void uabal(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void uabal2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void uabal2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void uabal2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void rsubhn(const VReg8B &vd, const VReg8H &vn, const VReg8H &vm);
void rsubhn(const VReg4H &vd, const VReg4S &vn, const VReg4S &vm);
void rsubhn(const VReg2S &vd, const VReg2D &vn, const VReg2D &vm);
void rsubhn2(const VReg16B &vd, const VReg8H &vn, const VReg8H &vm);
void rsubhn2(const VReg8H &vd, const VReg4S &vn, const VReg4S &vm);
void rsubhn2(const VReg4S &vd, const VReg2D &vn, const VReg2D &vm);
void uabdl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void uabdl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void uabdl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void uabdl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void uabdl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void uabdl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void umlal(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void umlal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void umlal(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void umlal2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void umlal2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void umlal2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void umlsl(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void umlsl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void umlsl(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void umlsl2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void umlsl2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void umlsl2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void umull(const VReg8H &vd, const VReg8B &vn, const VReg8B &vm);
void umull(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void umull(const VReg2D &vd, const VReg2S &vn, const VReg2S &vm);
void umull2(const VReg8H &vd, const VReg16B &vn, const VReg16B &vm);
void umull2(const VReg4S &vd, const VReg8H &vn, const VReg8H &vm);
void umull2(const VReg2D &vd, const VReg4S &vn, const VReg4S &vm);
void shadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void shadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void shadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void shadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void shadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void shadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sqadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sqadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqadd(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void srhadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void srhadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void srhadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void srhadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void srhadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void srhadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void shsub(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void shsub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void shsub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void shsub(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void shsub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void shsub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqsub(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sqsub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqsub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqsub(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sqsub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqsub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqsub(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmgt(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmgt(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmgt(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmgt(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmgt(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmgt(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmgt(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmge(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmge(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmge(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmge(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmge(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmge(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmge(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void sshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void sqshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sqshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sqshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void srshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void srshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void srshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void srshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void srshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void srshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void srshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void sqrshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sqrshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqrshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqrshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sqrshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqrshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqrshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void smax(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void smax(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void smax(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void smax(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void smax(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void smax(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void smin(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void smin(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void smin(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void smin(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void smin(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void smin(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sabd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sabd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sabd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sabd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sabd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sabd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void saba(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void saba(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void saba(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void saba(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void saba(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void saba(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void add(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void add(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void add(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void add(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void add(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void add(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void add(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmtst(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmtst(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmtst(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmtst(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmtst(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmtst(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmtst(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void mla(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void mla(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void mla(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void mla(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void mla(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void mla(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void mul(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void mul(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void mul(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void mul(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void mul(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void mul(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void smaxp(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void smaxp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void smaxp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void smaxp(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void smaxp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void smaxp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sminp(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sminp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sminp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sminp(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sminp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sminp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqdmulh(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqdmulh(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqdmulh(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqdmulh(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void addp(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void addp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void addp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void addp(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void addp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void addp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void addp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void uhadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uhadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uhadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uhadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uhadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uhadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uqadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uqadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uqadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uqadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uqadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqadd(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void urhadd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void urhadd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void urhadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void urhadd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void urhadd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void urhadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uhsub(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uhsub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uhsub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uhsub(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uhsub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uhsub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqsub(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uqsub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uqsub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uqsub(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uqsub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uqsub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqsub(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmhi(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmhi(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmhi(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmhi(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmhi(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmhi(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmhi(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmhs(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmhs(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmhs(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmhs(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmhs(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmhs(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmhs(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void ushl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void ushl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void ushl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void ushl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void ushl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void ushl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void ushl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void uqshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uqshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uqshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uqshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uqshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uqshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void urshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void urshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void urshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void urshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void urshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void urshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void urshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void uqrshl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uqrshl(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uqrshl(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uqrshl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uqrshl(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uqrshl(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uqrshl(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void umax(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void umax(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void umax(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void umax(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void umax(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void umax(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void umin(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void umin(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void umin(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void umin(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void umin(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void umin(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uabd(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uabd(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uabd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uabd(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uabd(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uabd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uaba(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uaba(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uaba(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uaba(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uaba(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uaba(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sub(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void sub(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sub(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void sub(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sub(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void cmeq(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void cmeq(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void cmeq(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void cmeq(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void cmeq(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void cmeq(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void cmeq(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void mls(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void mls(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void mls(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void mls(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void mls(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void mls(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void pmul(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void pmul(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void umaxp(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void umaxp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void umaxp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void umaxp(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void umaxp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void umaxp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void uminp(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void uminp(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void uminp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void uminp(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void uminp(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void uminp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sqrdmulh(const VReg4H &vd, const VReg4H &vn, const VReg4H &vm);
void sqrdmulh(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void sqrdmulh(const VReg8H &vd, const VReg8H &vn, const VReg8H &vm);
void sqrdmulh(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmaxnm(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmaxnm(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmaxnm(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmla(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmla(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmla(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fadd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fadd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fadd(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmulx(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmulx(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmulx(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fcmeq(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fcmeq(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fcmeq(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmax(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmax(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmax(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void frecps(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void frecps(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void frecps(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmaxnmp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmaxnmp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmaxnmp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void faddp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void faddp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void faddp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmul(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmul(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmul(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fcmge(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fcmge(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fcmge(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void facge(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void facge(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void facge(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmaxp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmaxp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmaxp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fdiv(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fdiv(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fdiv(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fminnm(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fminnm(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fminnm(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmls(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmls(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmls(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fsub(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fsub(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fsub(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fmin(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fmin(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fmin(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void frsqrts(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void frsqrts(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void frsqrts(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fminnmp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fminnmp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fminnmp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fabd(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fabd(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fabd(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fcmgt(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fcmgt(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fcmgt(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void facgt(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void facgt(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void facgt(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void fminp(const VReg2S &vd, const VReg2S &vn, const VReg2S &vm);
void fminp(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void fminp(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void and_(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void and_(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void fmlal(const VReg2S &vd, const VReg2H &vn, const VReg2H &vm);
void fmlal(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void bic(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void bic(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void orr(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void orr(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void mov(const VReg8B &vd, const VReg8B &vn);
void mov(const VReg16B &vd, const VReg16B &vn);
void fmlsl(const VReg2S &vd, const VReg2H &vn, const VReg2H &vm);
void fmlsl(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void orn(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void orn(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void eor(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void eor(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void fmlal2(const VReg2S &vd, const VReg2H &vn, const VReg2H &vm);
void fmlal2(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void bsl(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void bsl(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void bit(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void bit(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void fmlsl2(const VReg2S &vd, const VReg2H &vn, const VReg2H &vm);
void fmlsl2(const VReg4S &vd, const VReg4H &vn, const VReg4H &vm);
void bif(const VReg8B &vd, const VReg8B &vn, const VReg8B &vm);
void bif(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm);
void movi(const VReg2S &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const VReg4S &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const VReg8B &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const VReg16B &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const VReg4H &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const VReg8H &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void movi(const DReg &vd, const uint64_t imm);
void movi(const VReg2D &vd, const uint64_t imm);
void mvni(const VReg2S &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void mvni(const VReg4S &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void mvni(const VReg4H &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void mvni(const VReg8H &vd, const uint32_t imm8, const ShMod mod = LSL, const uint32_t sh = 0);
void orr(const VReg4H &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void orr(const VReg8H &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void orr(const VReg2S &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void orr(const VReg4S &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void bic(const VReg4H &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void bic(const VReg8H &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void bic(const VReg2S &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void bic(const VReg4S &vd, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void fmov(const VReg2S &vd, const double imm);
void fmov(const VReg4S &vd, const double imm);
void fmov(const VReg4H &vd, const double imm);
void fmov(const VReg8H &vd, const double imm);
void fmov(const VReg2D &vd, const double imm);
void sshr(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void sshr(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void sshr(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void sshr(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void sshr(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void sshr(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void sshr(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void ssra(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void ssra(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void ssra(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void ssra(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void ssra(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void ssra(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void ssra(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void srshr(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void srshr(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void srshr(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void srshr(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void srshr(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void srshr(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void srshr(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void srsra(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void srsra(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void srsra(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void srsra(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void srsra(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void srsra(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void srsra(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void shl(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void shl(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void shl(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void shl(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void shl(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void shl(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void shl(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void sqshl(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void sqshl(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void sqshl(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void sqshl(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void sqshl(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void sqshl(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void sqshl(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void shrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void shrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void shrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void shrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void shrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void shrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void rshrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void rshrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void rshrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void rshrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void rshrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void rshrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void sqshrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void sqshrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void sqshrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void sqshrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void sqshrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void sqshrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void sqrshrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void sqrshrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void sqrshrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void sqrshrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void sqrshrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void sqrshrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void sshll(const VReg8H &vd, const VReg8B &vn, const uint32_t sh);
void sshll(const VReg4S &vd, const VReg4H &vn, const uint32_t sh);
void sshll(const VReg2D &vd, const VReg2S &vn, const uint32_t sh);
void sshll2(const VReg8H &vd, const VReg16B &vn, const uint32_t sh);
void sshll2(const VReg4S &vd, const VReg8H &vn, const uint32_t sh);
void sshll2(const VReg2D &vd, const VReg4S &vn, const uint32_t sh);
void sxtl(const VReg8H &vd, const VReg8B &vn);
void sxtl(const VReg4S &vd, const VReg4H &vn);
void sxtl(const VReg2D &vd, const VReg2S &vn);
void sxtl2(const VReg8H &vd, const VReg16B &vn);
void sxtl2(const VReg4S &vd, const VReg8H &vn);
void sxtl2(const VReg2D &vd, const VReg4S &vn);
void scvtf(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void scvtf(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void scvtf(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void scvtf(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void scvtf(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void fcvtzs(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void fcvtzs(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void fcvtzs(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void fcvtzs(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void fcvtzs(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void ushr(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void ushr(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void ushr(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void ushr(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void ushr(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void ushr(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void ushr(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void usra(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void usra(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void usra(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void usra(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void usra(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void usra(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void usra(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void urshr(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void urshr(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void urshr(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void urshr(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void urshr(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void urshr(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void urshr(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void ursra(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void ursra(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void ursra(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void ursra(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void ursra(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void ursra(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void ursra(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void sri(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void sri(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void sri(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void sri(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void sri(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void sri(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void sri(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void sli(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void sli(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void sli(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void sli(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void sli(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void sli(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void sli(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void sqshlu(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void sqshlu(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void sqshlu(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void sqshlu(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void sqshlu(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void sqshlu(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void sqshlu(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void uqshl(const VReg8B &vd, const VReg8B &vn, const uint32_t sh);
void uqshl(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void uqshl(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void uqshl(const VReg16B &vd, const VReg16B &vn, const uint32_t sh);
void uqshl(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void uqshl(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void uqshl(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void sqshrun(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void sqshrun(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void sqshrun(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void sqshrun2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void sqshrun2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void sqshrun2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void sqrshrun(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void sqrshrun(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void sqrshrun(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void sqrshrun2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void sqrshrun2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void sqrshrun2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void uqshrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void uqshrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void uqshrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void uqshrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void uqshrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void uqshrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void uqrshrn(const VReg8B &vd, const VReg8H &vn, const uint32_t sh);
void uqrshrn(const VReg4H &vd, const VReg4S &vn, const uint32_t sh);
void uqrshrn(const VReg2S &vd, const VReg2D &vn, const uint32_t sh);
void uqrshrn2(const VReg16B &vd, const VReg8H &vn, const uint32_t sh);
void uqrshrn2(const VReg8H &vd, const VReg4S &vn, const uint32_t sh);
void uqrshrn2(const VReg4S &vd, const VReg2D &vn, const uint32_t sh);
void ushll(const VReg8H &vd, const VReg8B &vn, const uint32_t sh);
void ushll(const VReg4S &vd, const VReg4H &vn, const uint32_t sh);
void ushll(const VReg2D &vd, const VReg2S &vn, const uint32_t sh);
void ushll2(const VReg8H &vd, const VReg16B &vn, const uint32_t sh);
void ushll2(const VReg4S &vd, const VReg8H &vn, const uint32_t sh);
void ushll2(const VReg2D &vd, const VReg4S &vn, const uint32_t sh);
void uxtl(const VReg8H &vd, const VReg8B &vn);
void uxtl(const VReg4S &vd, const VReg4H &vn);
void uxtl(const VReg2D &vd, const VReg2S &vn);
void uxtl2(const VReg8H &vd, const VReg16B &vn);
void uxtl2(const VReg4S &vd, const VReg8H &vn);
void uxtl2(const VReg2D &vd, const VReg4S &vn);
void ucvtf(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void ucvtf(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void ucvtf(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void ucvtf(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void ucvtf(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void fcvtzu(const VReg4H &vd, const VReg4H &vn, const uint32_t sh);
void fcvtzu(const VReg2S &vd, const VReg2S &vn, const uint32_t sh);
void fcvtzu(const VReg8H &vd, const VReg8H &vn, const uint32_t sh);
void fcvtzu(const VReg4S &vd, const VReg4S &vn, const uint32_t sh);
void fcvtzu(const VReg2D &vd, const VReg2D &vn, const uint32_t sh);
void smlal(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void smlal(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void smlal2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void smlal2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void sqdmlal(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void sqdmlal(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void sqdmlal2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void sqdmlal2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void smlsl(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void smlsl(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void smlsl2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void smlsl2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void sqdmlsl(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void sqdmlsl(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void sqdmlsl2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void sqdmlsl2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void mul(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void mul(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void mul(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void mul(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void smull(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void smull(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void smull2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void smull2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void sqdmull(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void sqdmull(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void sqdmull2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void sqdmull2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void sqdmulh(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void sqdmulh(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void sqdmulh(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void sqdmulh(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sqrdmulh(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void sqrdmulh(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void sqrdmulh(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void sqrdmulh(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sdot(const VReg2S &vd, const VReg8B &vn, const VRegBElem &vm);
void sdot(const VReg4S &vd, const VReg16B &vn, const VRegBElem &vm);
void mla(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void mla(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void mla(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void mla(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void umlal(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void umlal(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void umlal2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void umlal2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void mls(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void mls(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void mls(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void mls(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void umlsl(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void umlsl(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void umlsl2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void umlsl2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void umull(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void umull(const VReg2D &vd, const VReg2S &vn, const VRegSElem &vm);
void umull2(const VReg4S &vd, const VReg8H &vn, const VRegHElem &vm);
void umull2(const VReg2D &vd, const VReg4S &vn, const VRegSElem &vm);
void sqrdmlah(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void sqrdmlah(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void sqrdmlah(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void sqrdmlah(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void udot(const VReg2S &vd, const VReg8B &vn, const VRegBElem &vm);
void udot(const VReg4S &vd, const VReg16B &vn, const VRegBElem &vm);
void sqrdmlsh(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void sqrdmlsh(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void sqrdmlsh(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void sqrdmlsh(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void fcmla(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm, const uint32_t rotate);
void fcmla(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm, const uint32_t rotate);
void fcmla(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm, const uint32_t rotate);
void fmla(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void fmla(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void fmls(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void fmls(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void fmul(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void fmul(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void fmla(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void fmla(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void fmls(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void fmls(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void fmul(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void fmul(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void fmla(const VReg2D &vd, const VReg2D &vn, const VRegDElem &vm);
void fmls(const VReg2D &vd, const VReg2D &vn, const VRegDElem &vm);
void fmul(const VReg2D &vd, const VReg2D &vn, const VRegDElem &vm);
void fmlal(const VReg2S &vd, const VReg2H &vn, const VRegHElem &vm);
void fmlal(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void fmlsl(const VReg2S &vd, const VReg2H &vn, const VRegHElem &vm);
void fmlsl(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void fmulx(const VReg4H &vd, const VReg4H &vn, const VRegHElem &vm);
void fmulx(const VReg8H &vd, const VReg8H &vn, const VRegHElem &vm);
void fmulx(const VReg2S &vd, const VReg2S &vn, const VRegSElem &vm);
void fmulx(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void fmulx(const VReg2D &vd, const VReg2D &vn, const VRegDElem &vm);
void fmlal2(const VReg2S &vd, const VReg2H &vn, const VRegHElem &vm);
void fmlal2(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void fmlsl2(const VReg2S &vd, const VReg2H &vn, const VRegHElem &vm);
void fmlsl2(const VReg4S &vd, const VReg4H &vn, const VRegHElem &vm);
void sm3tt1a(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sm3tt1b(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sm3tt2a(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sm3tt2b(const VReg4S &vd, const VReg4S &vn, const VRegSElem &vm);
void sha512h(const QReg &vd, const QReg &vn, const VReg2D &vm);
void sha512h2(const QReg &vd, const QReg &vn, const VReg2D &vm);
void sha512su1(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void rax1(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm);
void sm3partw1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sm3partw2(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void sm4ekey(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm);
void xar(const VReg2D &vd, const VReg2D &vn, const VReg2D &vm, const uint32_t imm6);
void eor3(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm, const VReg16B &va);
void bcax(const VReg16B &vd, const VReg16B &vn, const VReg16B &vm, const VReg16B &va);
void sm3ss1(const VReg4S &vd, const VReg4S &vn, const VReg4S &vm, const VReg4S &va);
void sha512su0(const VReg2D &vd, const VReg2D &vn);
void sm4e(const VReg4S &vd, const VReg4S &vn);
void scvtf(const SReg &d, const WReg &n, const uint32_t fbits);
void scvtf(const SReg &d, const XReg &n, const uint32_t fbits);
void ucvtf(const SReg &d, const WReg &n, const uint32_t fbits);
void ucvtf(const SReg &d, const XReg &n, const uint32_t fbits);
void fcvtzs(const WReg &d, const SReg &n, const uint32_t fbits);
void fcvtzs(const XReg &d, const SReg &n, const uint32_t fbits);
void fcvtzu(const WReg &d, const SReg &n, const uint32_t fbits);
void fcvtzu(const XReg &d, const SReg &n, const uint32_t fbits);
void scvtf(const DReg &d, const WReg &n, const uint32_t fbits);
void scvtf(const DReg &d, const XReg &n, const uint32_t fbits);
void ucvtf(const DReg &d, const WReg &n, const uint32_t fbits);
void ucvtf(const DReg &d, const XReg &n, const uint32_t fbits);
void fcvtzs(const WReg &d, const DReg &n, const uint32_t fbits);
void fcvtzs(const XReg &d, const DReg &n, const uint32_t fbits);
void fcvtzu(const WReg &d, const DReg &n, const uint32_t fbits);
void fcvtzu(const XReg &d, const DReg &n, const uint32_t fbits);
void scvtf(const HReg &d, const WReg &n, const uint32_t fbits);
void scvtf(const HReg &d, const XReg &n, const uint32_t fbits);
void ucvtf(const HReg &d, const WReg &n, const uint32_t fbits);
void ucvtf(const HReg &d, const XReg &n, const uint32_t fbits);
void fcvtzs(const WReg &d, const HReg &n, const uint32_t fbits);
void fcvtzs(const XReg &d, const HReg &n, const uint32_t fbits);
void fcvtzu(const WReg &d, const HReg &n, const uint32_t fbits);
void fcvtzu(const XReg &d, const HReg &n, const uint32_t fbits);
void fcvtns(const WReg &d, const SReg &n);
void fcvtnu(const WReg &d, const SReg &n);
void fcvtas(const WReg &d, const SReg &n);
void fcvtau(const WReg &d, const SReg &n);
void fmov(const WReg &d, const SReg &n);
void fcvtps(const WReg &d, const SReg &n);
void fcvtpu(const WReg &d, const SReg &n);
void fcvtms(const WReg &d, const SReg &n);
void fcvtmu(const WReg &d, const SReg &n);
void fcvtzs(const WReg &d, const SReg &n);
void fcvtzu(const WReg &d, const SReg &n);
void scvtf(const SReg &d, const WReg &n);
void ucvtf(const SReg &d, const WReg &n);
void fmov(const SReg &d, const WReg &n);
void fcvtns(const WReg &d, const DReg &n);
void fcvtnu(const WReg &d, const DReg &n);
void fcvtas(const WReg &d, const DReg &n);
void fcvtau(const WReg &d, const DReg &n);
void fcvtps(const WReg &d, const DReg &n);
void fcvtpu(const WReg &d, const DReg &n);
void fcvtms(const WReg &d, const DReg &n);
void fcvtmu(const WReg &d, const DReg &n);
void fcvtzs(const WReg &d, const DReg &n);
void fcvtzu(const WReg &d, const DReg &n);
void fjcvtzs(const WReg &d, const DReg &n);
void scvtf(const DReg &d, const WReg &n);
void ucvtf(const DReg &d, const WReg &n);
void fcvtns(const WReg &d, const HReg &n);
void fcvtnu(const WReg &d, const HReg &n);
void fcvtas(const WReg &d, const HReg &n);
void fcvtau(const WReg &d, const HReg &n);
void fmov(const WReg &d, const HReg &n);
void fcvtps(const WReg &d, const HReg &n);
void fcvtpu(const WReg &d, const HReg &n);
void fcvtms(const WReg &d, const HReg &n);
void fcvtmu(const WReg &d, const HReg &n);
void fcvtzs(const WReg &d, const HReg &n);
void fcvtzu(const WReg &d, const HReg &n);
void scvtf(const HReg &d, const WReg &n);
void ucvtf(const HReg &d, const WReg &n);
void fmov(const HReg &d, const WReg &n);
void fcvtns(const XReg &d, const SReg &n);
void fcvtnu(const XReg &d, const SReg &n);
void fcvtas(const XReg &d, const SReg &n);
void fcvtau(const XReg &d, const SReg &n);
void fcvtps(const XReg &d, const SReg &n);
void fcvtpu(const XReg &d, const SReg &n);
void fcvtms(const XReg &d, const SReg &n);
void fcvtmu(const XReg &d, const SReg &n);
void fcvtzs(const XReg &d, const SReg &n);
void fcvtzu(const XReg &d, const SReg &n);
void scvtf(const SReg &d, const XReg &n);
void ucvtf(const SReg &d, const XReg &n);
void fcvtns(const XReg &d, const DReg &n);
void fcvtnu(const XReg &d, const DReg &n);
void fcvtas(const XReg &d, const DReg &n);
void fcvtau(const XReg &d, const DReg &n);
void fmov(const XReg &d, const DReg &n);
void fcvtps(const XReg &d, const DReg &n);
void fcvtpu(const XReg &d, const DReg &n);
void fcvtms(const XReg &d, const DReg &n);
void fcvtmu(const XReg &d, const DReg &n);
void fcvtzs(const XReg &d, const DReg &n);
void fcvtzu(const XReg &d, const DReg &n);
void scvtf(const DReg &d, const XReg &n);
void ucvtf(const DReg &d, const XReg &n);
void fmov(const DReg &d, const XReg &n);
void fmov(const XReg &d, const VRegDElem &n);
void fmov(const VRegDElem &d, const XReg &n);
void fcvtns(const XReg &d, const HReg &n);
void fcvtnu(const XReg &d, const HReg &n);
void fcvtas(const XReg &d, const HReg &n);
void fcvtau(const XReg &d, const HReg &n);
void fmov(const XReg &d, const HReg &n);
void fcvtps(const XReg &d, const HReg &n);
void fcvtpu(const XReg &d, const HReg &n);
void fcvtms(const XReg &d, const HReg &n);
void fcvtmu(const XReg &d, const HReg &n);
void fcvtzs(const XReg &d, const HReg &n);
void fcvtzu(const XReg &d, const HReg &n);
void scvtf(const HReg &d, const XReg &n);
void ucvtf(const HReg &d, const XReg &n);
void fmov(const HReg &d, const XReg &n);
void fmov(const SReg &vd, const SReg &vn);
void fabs(const SReg &vd, const SReg &vn);
void fneg(const SReg &vd, const SReg &vn);
void fsqrt(const SReg &vd, const SReg &vn);
void frintn(const SReg &vd, const SReg &vn);
void frintp(const SReg &vd, const SReg &vn);
void frintm(const SReg &vd, const SReg &vn);
void frintz(const SReg &vd, const SReg &vn);
void frinta(const SReg &vd, const SReg &vn);
void frintx(const SReg &vd, const SReg &vn);
void frinti(const SReg &vd, const SReg &vn);
void fcvt(const DReg &vd, const SReg &vn);
void fcvt(const HReg &vd, const SReg &vn);
void fmov(const DReg &vd, const DReg &vn);
void fabs(const DReg &vd, const DReg &vn);
void fneg(const DReg &vd, const DReg &vn);
void fsqrt(const DReg &vd, const DReg &vn);
void bfcvt(const HReg &vd, const SReg &vn);
void frintn(const DReg &vd, const DReg &vn);
void frintp(const DReg &vd, const DReg &vn);
void frintm(const DReg &vd, const DReg &vn);
void frintz(const DReg &vd, const DReg &vn);
void frinta(const DReg &vd, const DReg &vn);
void frintx(const DReg &vd, const DReg &vn);
void frinti(const DReg &vd, const DReg &vn);
void fcvt(const SReg &vd, const DReg &vn);
void fcvt(const HReg &vd, const DReg &vn);
void fmov(const HReg &vd, const HReg &vn);
void fabs(const HReg &vd, const HReg &vn);
void fneg(const HReg &vd, const HReg &vn);
void fsqrt(const HReg &vd, const HReg &vn);
void frintn(const HReg &vd, const HReg &vn);
void frintp(const HReg &vd, const HReg &vn);
void frintm(const HReg &vd, const HReg &vn);
void frintz(const HReg &vd, const HReg &vn);
void frinta(const HReg &vd, const HReg &vn);
void frintx(const HReg &vd, const HReg &vn);
void frinti(const HReg &vd, const HReg &vn);
void fcvt(const SReg &vd, const HReg &vn);
void fcvt(const DReg &vd, const HReg &vn);
void fcmp(const SReg &vn, const SReg &vm);
void fcmpe(const SReg &vn, const SReg &vm);
void fcmp(const SReg &vn, const double imm);
void fcmpe(const SReg &vn, const double imm);
void fcmp(const DReg &vn, const DReg &vm);
void fcmpe(const DReg &vn, const DReg &vm);
void fcmp(const DReg &vn, const double imm);
void fcmpe(const DReg &vn, const double imm);
void fcmp(const HReg &vn, const HReg &vm);
void fcmpe(const HReg &vn, const HReg &vm);
void fcmp(const HReg &vn, const double imm);
void fcmpe(const HReg &vn, const double imm);
void fmov(const SReg &vd, const double imm);
void fmov(const DReg &vd, const double imm);
void fmov(const HReg &vd, const double imm);
void fccmp(const SReg &vn, const SReg &vm, const uint32_t nzcv, const Cond cond);
void fccmpe(const SReg &vn, const SReg &vm, const uint32_t nzcv, const Cond cond);
void fccmp(const DReg &vn, const DReg &vm, const uint32_t nzcv, const Cond cond);
void fccmpe(const DReg &vn, const DReg &vm, const uint32_t nzcv, const Cond cond);
void fccmp(const HReg &vn, const HReg &vm, const uint32_t nzcv, const Cond cond);
void fccmpe(const HReg &vn, const HReg &vm, const uint32_t nzcv, const Cond cond);
void fmul(const SReg &vd, const SReg &vn, const SReg &vm);
void fdiv(const SReg &vd, const SReg &vn, const SReg &vm);
void fadd(const SReg &vd, const SReg &vn, const SReg &vm);
void fsub(const SReg &vd, const SReg &vn, const SReg &vm);
void fmax(const SReg &vd, const SReg &vn, const SReg &vm);
void fmin(const SReg &vd, const SReg &vn, const SReg &vm);
void fmaxnm(const SReg &vd, const SReg &vn, const SReg &vm);
void fminnm(const SReg &vd, const SReg &vn, const SReg &vm);
void fnmul(const SReg &vd, const SReg &vn, const SReg &vm);
void fmul(const DReg &vd, const DReg &vn, const DReg &vm);
void fdiv(const DReg &vd, const DReg &vn, const DReg &vm);
void fadd(const DReg &vd, const DReg &vn, const DReg &vm);
void fsub(const DReg &vd, const DReg &vn, const DReg &vm);
void fmax(const DReg &vd, const DReg &vn, const DReg &vm);
void fmin(const DReg &vd, const DReg &vn, const DReg &vm);
void fmaxnm(const DReg &vd, const DReg &vn, const DReg &vm);
void fminnm(const DReg &vd, const DReg &vn, const DReg &vm);
void fnmul(const DReg &vd, const DReg &vn, const DReg &vm);
void fmul(const HReg &vd, const HReg &vn, const HReg &vm);
void fdiv(const HReg &vd, const HReg &vn, const HReg &vm);
void fadd(const HReg &vd, const HReg &vn, const HReg &vm);
void fsub(const HReg &vd, const HReg &vn, const HReg &vm);
void fmax(const HReg &vd, const HReg &vn, const HReg &vm);
void fmin(const HReg &vd, const HReg &vn, const HReg &vm);
void fmaxnm(const HReg &vd, const HReg &vn, const HReg &vm);
void fminnm(const HReg &vd, const HReg &vn, const HReg &vm);
void fnmul(const HReg &vd, const HReg &vn, const HReg &vm);
void fcsel(const SReg &vd, const SReg &vn, const SReg &vm, const Cond cond);
void fcsel(const DReg &vd, const DReg &vn, const DReg &vm, const Cond cond);
void fcsel(const HReg &vd, const HReg &vn, const HReg &vm, const Cond cond);
void fmadd(const SReg &vd, const SReg &vn, const SReg &vm, const SReg &va);
void fmsub(const SReg &vd, const SReg &vn, const SReg &vm, const SReg &va);
void fnmadd(const SReg &vd, const SReg &vn, const SReg &vm, const SReg &va);
void fnmsub(const SReg &vd, const SReg &vn, const SReg &vm, const SReg &va);
void fmadd(const DReg &vd, const DReg &vn, const DReg &vm, const DReg &va);
void fmsub(const DReg &vd, const DReg &vn, const DReg &vm, const DReg &va);
void fnmadd(const DReg &vd, const DReg &vn, const DReg &vm, const DReg &va);
void fnmsub(const DReg &vd, const DReg &vn, const DReg &vm, const DReg &va);
void fmadd(const HReg &vd, const HReg &vn, const HReg &vm, const HReg &va);
void fmsub(const HReg &vd, const HReg &vn, const HReg &vm, const HReg &va);
void fnmadd(const HReg &vd, const HReg &vn, const HReg &vm, const HReg &va);
void fnmsub(const HReg &vd, const HReg &vn, const HReg &vm, const HReg &va);
void orr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void orr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void orr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void orr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void eor(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void eor(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void eor(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void eor(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void and_(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void and_(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void and_(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void and_(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void bic(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void bic(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void bic(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void bic(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void add(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void add(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void add(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void add(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sub(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void subr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void subr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void subr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void subr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void smax(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void smax(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void smax(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void smax(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void umax(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void umax(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void umax(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void umax(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void smin(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void smin(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void smin(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void smin(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void umin(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void umin(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void umin(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void umin(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sabd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sabd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sabd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sabd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uabd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uabd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uabd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uabd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void mul(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void mul(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void mul(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void mul(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void smulh(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void smulh(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void smulh(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void smulh(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void umulh(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void umulh(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void umulh(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void umulh(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sdiv(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sdiv(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void udiv(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void udiv(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sdivr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sdivr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void udivr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void udivr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void orv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void orv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void orv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void orv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void eorv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void eorv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void eorv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void eorv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void andv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void andv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void andv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void andv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void movprfx(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void movprfx(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void movprfx(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void movprfx(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void saddv(const DReg &vd, const _PReg &pg, const ZRegB &zn);
void saddv(const DReg &vd, const _PReg &pg, const ZRegH &zn);
void saddv(const DReg &vd, const _PReg &pg, const ZRegS &zn);
void uaddv(const DReg &vd, const _PReg &pg, const ZRegB &zn);
void uaddv(const DReg &vd, const _PReg &pg, const ZRegH &zn);
void uaddv(const DReg &vd, const _PReg &pg, const ZRegS &zn);
void uaddv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void smaxv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void smaxv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void smaxv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void smaxv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void umaxv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void umaxv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void umaxv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void umaxv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void sminv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void sminv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void sminv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void sminv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void uminv(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void uminv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void uminv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void uminv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void asr(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void asr(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void asr(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void asr(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void lsr(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void lsr(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void lsr(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void lsr(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void lsl(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void lsl(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void lsl(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void lsl(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void asrd(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void asrd(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void asrd(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void asrd(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void sqshl(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void sqshl(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void sqshl(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void sqshl(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void uqshl(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void uqshl(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void uqshl(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void uqshl(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void srshr(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void srshr(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void srshr(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void srshr(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void urshr(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void urshr(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void urshr(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void urshr(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void sqshlu(const ZRegB &zdn, const _PReg &pg, const uint32_t amount);
void sqshlu(const ZRegH &zdn, const _PReg &pg, const uint32_t amount);
void sqshlu(const ZRegS &zdn, const _PReg &pg, const uint32_t amount);
void sqshlu(const ZRegD &zdn, const _PReg &pg, const uint32_t amount);
void asr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void asr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void asr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void asr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void lsr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void lsr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void lsr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void lsr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void lsl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void lsl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void lsl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void lsl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void asrr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void asrr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void asrr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void asrr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void lsrr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void lsrr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void lsrr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void lsrr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void lslr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void lslr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void lslr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void lslr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void asr(const ZRegB &zdn, const _PReg &pg, const ZRegD &zm);
void asr(const ZRegH &zdn, const _PReg &pg, const ZRegD &zm);
void asr(const ZRegS &zdn, const _PReg &pg, const ZRegD &zm);
void lsr(const ZRegB &zdn, const _PReg &pg, const ZRegD &zm);
void lsr(const ZRegH &zdn, const _PReg &pg, const ZRegD &zm);
void lsr(const ZRegS &zdn, const _PReg &pg, const ZRegD &zm);
void lsl(const ZRegB &zdn, const _PReg &pg, const ZRegD &zm);
void lsl(const ZRegH &zdn, const _PReg &pg, const ZRegD &zm);
void lsl(const ZRegS &zdn, const _PReg &pg, const ZRegD &zm);
void cls(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void cls(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void cls(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void cls(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void clz(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void clz(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void clz(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void clz(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void cnt(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void cnt(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void cnt(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void cnt(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void cnot(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void cnot(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void cnot(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void cnot(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void fabs(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void fabs(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void fabs(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void fneg(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void fneg(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void fneg(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void not_(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void not_(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void not_(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void not_(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void sxtb(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void sxtb(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void sxtb(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void uxtb(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void uxtb(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void uxtb(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void sxth(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void sxth(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void uxth(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void uxth(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void sxtw(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void uxtw(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void abs(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void abs(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void abs(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void abs(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void neg(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void neg(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void neg(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void neg(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void mla(const ZRegB &zda, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void mla(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void mla(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void mla(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void mls(const ZRegB &zda, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void mls(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void mls(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void mls(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void mad(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm, const ZRegB &za);
void mad(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void mad(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void mad(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void msb(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm, const ZRegB &za);
void msb(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void msb(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void msb(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void add(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void add(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void add(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void add(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sub(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void sub(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void sub(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void sub(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sqadd(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void sqadd(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void sqadd(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void sqadd(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void uqadd(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void uqadd(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void uqadd(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void uqadd(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sqsub(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void sqsub(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void sqsub(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void sqsub(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void uqsub(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void uqsub(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void uqsub(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void uqsub(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void xar(const ZRegB &zdn, const ZRegB &zm, const uint32_t amount);
void xar(const ZRegH &zdn, const ZRegH &zm, const uint32_t amount);
void xar(const ZRegS &zdn, const ZRegS &zm, const uint32_t amount);
void xar(const ZRegD &zdn, const ZRegD &zm, const uint32_t amount);
void and_(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void orr(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void mov(const ZRegD &zd, const ZRegD &zn);
void eor(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void bic(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void eor3(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void bsl(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void bcax(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void bsl1n(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void bsl2n(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void nbsl(const ZRegD &zdn, const ZRegD &zm, const ZRegD &zk);
void index(const ZRegB &zd, const int32_t imm1, const int32_t imm2);
void index(const ZRegH &zd, const int32_t imm1, const int32_t imm2);
void index(const ZRegS &zd, const int32_t imm1, const int32_t imm2);
void index(const ZRegD &zd, const int32_t imm1, const int32_t imm2);
void index(const ZRegB &zd, const int32_t imm, const WReg &rm);
void index(const ZRegH &zd, const int32_t imm, const WReg &rm);
void index(const ZRegS &zd, const int32_t imm, const WReg &rm);
void index(const ZRegD &zd, const int32_t imm, const XReg &rm);
void index(const ZRegB &zd, const WReg &rn, const int32_t imm);
void index(const ZRegH &zd, const WReg &rn, const int32_t imm);
void index(const ZRegS &zd, const WReg &rn, const int32_t imm);
void index(const ZRegD &zd, const XReg &rn, const int32_t imm);
void index(const ZRegB &zd, const WReg &rn, const WReg &rm);
void index(const ZRegH &zd, const WReg &rn, const WReg &rm);
void index(const ZRegS &zd, const WReg &rn, const WReg &rm);
void index(const ZRegD &zd, const XReg &rn, const XReg &rm);
void addvl(const XReg &xd, const XReg &xn, const int32_t imm);
void addpl(const XReg &xd, const XReg &xn, const int32_t imm);
void rdvl(const XReg &xd, const int32_t imm);
void mul(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void mul(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void mul(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void mul(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void smulh(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void smulh(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void smulh(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void smulh(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void umulh(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void umulh(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void umulh(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void umulh(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void pmul(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void pmul(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void pmul(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void pmul(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sqdmulh(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void sqdmulh(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void sqdmulh(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void sqdmulh(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sqrdmulh(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void sqrdmulh(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void sqrdmulh(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void sqrdmulh(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void asr(const ZRegB &zd, const ZRegB &zn, const uint32_t amount);
void asr(const ZRegH &zd, const ZRegH &zn, const uint32_t amount);
void asr(const ZRegS &zd, const ZRegS &zn, const uint32_t amount);
void asr(const ZRegD &zd, const ZRegD &zn, const uint32_t amount);
void lsr(const ZRegB &zd, const ZRegB &zn, const uint32_t amount);
void lsr(const ZRegH &zd, const ZRegH &zn, const uint32_t amount);
void lsr(const ZRegS &zd, const ZRegS &zn, const uint32_t amount);
void lsr(const ZRegD &zd, const ZRegD &zn, const uint32_t amount);
void lsl(const ZRegB &zd, const ZRegB &zn, const uint32_t amount);
void lsl(const ZRegH &zd, const ZRegH &zn, const uint32_t amount);
void lsl(const ZRegS &zd, const ZRegS &zn, const uint32_t amount);
void lsl(const ZRegD &zd, const ZRegD &zn, const uint32_t amount);
void asr(const ZRegB &zd, const ZRegB &zn, const ZRegD &zm);
void asr(const ZRegH &zd, const ZRegH &zn, const ZRegD &zm);
void asr(const ZRegS &zd, const ZRegS &zn, const ZRegD &zm);
void lsr(const ZRegB &zd, const ZRegB &zn, const ZRegD &zm);
void lsr(const ZRegH &zd, const ZRegH &zn, const ZRegD &zm);
void lsr(const ZRegS &zd, const ZRegS &zn, const ZRegD &zm);
void lsl(const ZRegB &zd, const ZRegB &zn, const ZRegD &zm);
void lsl(const ZRegH &zd, const ZRegH &zn, const ZRegD &zm);
void lsl(const ZRegS &zd, const ZRegS &zn, const ZRegD &zm);
void adr(const ZRegS &zd, const AdrVec &adr);
void adr(const ZRegD &zd, const AdrVec &adr);
void adr(const ZRegD &zd, const AdrVecU &adr);
void movprfx(const ZReg &zd, const ZReg &zn);
void fexpa(const ZRegH &zd, const ZRegH &zn);
void fexpa(const ZRegS &zd, const ZRegS &zn);
void fexpa(const ZRegD &zd, const ZRegD &zn);
void ftssel(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void ftssel(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void ftssel(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void cntb(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void cnth(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void cntw(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void cntd(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void incb(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void decb(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void inch(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void dech(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void incw(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void decw(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void incd(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void decd(const XReg &xd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void inch(const ZRegH &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void dech(const ZRegH &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void incw(const ZRegS &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void decw(const ZRegS &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void incd(const ZRegD &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void decd(const ZRegD &zd, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincb(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincb(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincb(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincb(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecb(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecb(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecb(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecb(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqinch(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqinch(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqinch(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqinch(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdech(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdech(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdech(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdech(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincw(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincw(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincw(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincw(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecw(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecw(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecw(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecw(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincd(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincd(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincd(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincd(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecd(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecd(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecd(const WReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecd(const XReg &rdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqinch(const ZRegH &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqinch(const ZRegH &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdech(const ZRegH &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdech(const ZRegH &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincw(const ZRegS &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincw(const ZRegS &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecw(const ZRegS &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecw(const ZRegS &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqincd(const ZRegD &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqincd(const ZRegD &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void sqdecd(const ZRegD &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void uqdecd(const ZRegD &zdn, const Pattern pat = ALL, const ExtMod mod = MUL, const uint32_t imm = 1);
void orr(const ZRegB &zdn, const uint64_t imm);
void orr(const ZRegH &zdn, const uint64_t imm);
void orr(const ZRegS &zdn, const uint64_t imm);
void orr(const ZRegD &zdn, const uint64_t imm);
void orn(const ZRegB &zdn, const uint64_t imm);
void orn(const ZRegH &zdn, const uint64_t imm);
void orn(const ZRegS &zdn, const uint64_t imm);
void orn(const ZRegD &zdn, const uint64_t imm);
void eor(const ZRegB &zdn, const uint64_t imm);
void eor(const ZRegH &zdn, const uint64_t imm);
void eor(const ZRegS &zdn, const uint64_t imm);
void eor(const ZRegD &zdn, const uint64_t imm);
void eon(const ZRegB &zdn, const uint64_t imm);
void eon(const ZRegH &zdn, const uint64_t imm);
void eon(const ZRegS &zdn, const uint64_t imm);
void eon(const ZRegD &zdn, const uint64_t imm);
void and_(const ZRegB &zdn, const uint64_t imm);
void and_(const ZRegH &zdn, const uint64_t imm);
void and_(const ZRegS &zdn, const uint64_t imm);
void and_(const ZRegD &zdn, const uint64_t imm);
void bic(const ZRegB &zdn, const uint64_t imm);
void bic(const ZRegH &zdn, const uint64_t imm);
void bic(const ZRegS &zdn, const uint64_t imm);
void bic(const ZRegD &zdn, const uint64_t imm);
void dupm(const ZRegB &zd, const uint64_t imm);
void dupm(const ZRegH &zd, const uint64_t imm);
void dupm(const ZRegS &zd, const uint64_t imm);
void dupm(const ZRegD &zd, const uint64_t imm);
void mov(const ZRegB &zd, const uint64_t imm);
void mov(const ZRegH &zd, const uint64_t imm);
void mov(const ZRegS &zd, const uint64_t imm);
void mov(const ZRegD &zd, const uint64_t imm);
void fcpy(const ZRegH &zd, const _PReg &pg, const double imm);
void fcpy(const ZRegS &zd, const _PReg &pg, const double imm);
void fcpy(const ZRegD &zd, const _PReg &pg, const double imm);
void fmov(const ZRegH &zd, const _PReg &pg, const double imm);
void fmov(const ZRegS &zd, const _PReg &pg, const double imm);
void fmov(const ZRegD &zd, const _PReg &pg, const double imm);
void cpy(const ZRegB &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void cpy(const ZRegH &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void cpy(const ZRegS &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void cpy(const ZRegD &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegB &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegH &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegS &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegD &zd, const _PReg &pg, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void fmov(const ZRegH &zd, const _PReg &pg, const uint32_t imm = 0.0);
void fmov(const ZRegS &zd, const _PReg &pg, const uint32_t imm = 0.0);
void fmov(const ZRegD &zd, const _PReg &pg, const uint32_t imm = 0.0);
void ext(const ZRegB &zdn, const ZRegB &zm, const uint32_t imm);
void dup(const ZRegB &zd, const WReg &rn);
void dup(const ZRegH &zd, const WReg &rn);
void dup(const ZRegS &zd, const WReg &rn);
void dup(const ZRegD &zd, const XReg &rn);
void mov(const ZRegB &zd, const WReg &rn);
void mov(const ZRegH &zd, const WReg &rn);
void mov(const ZRegS &zd, const WReg &rn);
void mov(const ZRegD &zd, const XReg &rn);
void dup(const ZRegB &zd, const ZRegBElem &zn);
void dup(const ZRegH &zd, const ZRegHElem &zn);
void dup(const ZRegS &zd, const ZRegSElem &zn);
void dup(const ZRegD &zd, const ZRegDElem &zn);
void dup(const ZRegQ &zd, const ZRegQElem &zn);
void mov(const ZRegB &zd, const ZRegBElem &zn);
void mov(const ZRegH &zd, const ZRegHElem &zn);
void mov(const ZRegS &zd, const ZRegSElem &zn);
void mov(const ZRegD &zd, const ZRegDElem &zn);
void mov(const ZRegQ &zd, const ZRegQElem &zn);
void mov(const ZRegB &zd, const BReg &vn);
void mov(const ZRegH &zd, const HReg &vn);
void mov(const ZRegS &zd, const SReg &vn);
void mov(const ZRegD &zd, const DReg &vn);
void mov(const ZRegQ &zd, const QReg &vn);
void insr(const ZRegB &zdn, const BReg &vm);
void insr(const ZRegH &zdn, const HReg &vm);
void insr(const ZRegS &zdn, const SReg &vm);
void insr(const ZRegD &zdn, const DReg &vm);
void insr(const ZRegB &zdn, const WReg &rm);
void insr(const ZRegH &zdn, const WReg &rm);
void insr(const ZRegS &zdn, const WReg &rm);
void insr(const ZRegD &zdn, const XReg &rm);
void rev(const ZRegB &zd, const ZRegB &zn);
void rev(const ZRegH &zd, const ZRegH &zn);
void rev(const ZRegS &zd, const ZRegS &zn);
void rev(const ZRegD &zd, const ZRegD &zn);
void tbl(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void tbl(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void tbl(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void tbl(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void tbl(const ZRegB &zd, const ZRegBList &zn_list, const ZRegB &zm);
void tbl(const ZRegH &zd, const ZRegHList &zn_list, const ZRegH &zm);
void tbl(const ZRegS &zd, const ZRegSList &zn_list, const ZRegS &zm);
void tbl(const ZRegD &zd, const ZRegDList &zn_list, const ZRegD &zm);
void tbx(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void tbx(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void tbx(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void tbx(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void sunpklo(const ZRegH &zd, const ZRegB &zn);
void sunpklo(const ZRegS &zd, const ZRegH &zn);
void sunpklo(const ZRegD &zd, const ZRegS &zn);
void sunpkhi(const ZRegH &zd, const ZRegB &zn);
void sunpkhi(const ZRegS &zd, const ZRegH &zn);
void sunpkhi(const ZRegD &zd, const ZRegS &zn);
void uunpklo(const ZRegH &zd, const ZRegB &zn);
void uunpklo(const ZRegS &zd, const ZRegH &zn);
void uunpklo(const ZRegD &zd, const ZRegS &zn);
void uunpkhi(const ZRegH &zd, const ZRegB &zn);
void uunpkhi(const ZRegS &zd, const ZRegH &zn);
void uunpkhi(const ZRegD &zd, const ZRegS &zn);
void zip1(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void zip1(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void zip1(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void zip1(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void zip2(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void zip2(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void zip2(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void zip2(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void uzp1(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void uzp1(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void uzp1(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void uzp1(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void uzp2(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void uzp2(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void uzp2(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void uzp2(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void trn1(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void trn1(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void trn1(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void trn1(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void trn2(const PRegB &pd, const PRegB &pn, const PRegB &pm);
void trn2(const PRegH &pd, const PRegH &pn, const PRegH &pm);
void trn2(const PRegS &pd, const PRegS &pn, const PRegS &pm);
void trn2(const PRegD &pd, const PRegD &pn, const PRegD &pm);
void rev(const PRegB &pd, const PRegB &pn);
void rev(const PRegH &pd, const PRegH &pn);
void rev(const PRegS &pd, const PRegS &pn);
void rev(const PRegD &pd, const PRegD &pn);
void punpklo(const PRegH &pd, const PRegB &pn);
void punpkhi(const PRegH &pd, const PRegB &pn);
void zip1(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void zip1(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void zip1(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void zip1(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void zip2(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void zip2(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void zip2(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void zip2(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void uzp1(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void uzp1(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void uzp1(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void uzp1(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void uzp2(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void uzp2(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void uzp2(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void uzp2(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void trn1(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void trn1(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void trn1(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void trn1(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void trn2(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void trn2(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void trn2(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void trn2(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void compact(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void compact(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void clasta(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void clasta(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void clasta(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void clasta(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void clastb(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void clastb(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void clastb(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void clastb(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void clasta(const BReg &vdn, const _PReg &pg, const ZRegB &zm);
void clasta(const HReg &vdn, const _PReg &pg, const ZRegH &zm);
void clasta(const SReg &vdn, const _PReg &pg, const ZRegS &zm);
void clasta(const DReg &vdn, const _PReg &pg, const ZRegD &zm);
void clastb(const BReg &vdn, const _PReg &pg, const ZRegB &zm);
void clastb(const HReg &vdn, const _PReg &pg, const ZRegH &zm);
void clastb(const SReg &vdn, const _PReg &pg, const ZRegS &zm);
void clastb(const DReg &vdn, const _PReg &pg, const ZRegD &zm);
void clasta(const WReg &rdn, const _PReg &pg, const ZRegB &zm);
void clasta(const WReg &rdn, const _PReg &pg, const ZRegH &zm);
void clasta(const WReg &rdn, const _PReg &pg, const ZRegS &zm);
void clasta(const XReg &rdn, const _PReg &pg, const ZRegD &zm);
void clastb(const WReg &rdn, const _PReg &pg, const ZRegB &zm);
void clastb(const WReg &rdn, const _PReg &pg, const ZRegH &zm);
void clastb(const WReg &rdn, const _PReg &pg, const ZRegS &zm);
void clastb(const XReg &rdn, const _PReg &pg, const ZRegD &zm);
void cpy(const ZRegB &zd, const _PReg &pg, const BReg &vn);
void cpy(const ZRegH &zd, const _PReg &pg, const HReg &vn);
void cpy(const ZRegS &zd, const _PReg &pg, const SReg &vn);
void cpy(const ZRegD &zd, const _PReg &pg, const DReg &vn);
void mov(const ZRegB &zd, const _PReg &pg, const BReg &vn);
void mov(const ZRegH &zd, const _PReg &pg, const HReg &vn);
void mov(const ZRegS &zd, const _PReg &pg, const SReg &vn);
void mov(const ZRegD &zd, const _PReg &pg, const DReg &vn);
void cpy(const ZRegB &zd, const _PReg &pg, const WReg &rn);
void cpy(const ZRegH &zd, const _PReg &pg, const WReg &rn);
void cpy(const ZRegS &zd, const _PReg &pg, const WReg &rn);
void cpy(const ZRegD &zd, const _PReg &pg, const XReg &rn);
void mov(const ZRegB &zd, const _PReg &pg, const WReg &rn);
void mov(const ZRegH &zd, const _PReg &pg, const WReg &rn);
void mov(const ZRegS &zd, const _PReg &pg, const WReg &rn);
void mov(const ZRegD &zd, const _PReg &pg, const XReg &rn);
void lasta(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void lasta(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void lasta(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void lasta(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void lastb(const BReg &vd, const _PReg &pg, const ZRegB &zn);
void lastb(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void lastb(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void lastb(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void lasta(const WReg &rd, const _PReg &pg, const ZRegB &zn);
void lasta(const WReg &rd, const _PReg &pg, const ZRegH &zn);
void lasta(const WReg &rd, const _PReg &pg, const ZRegS &zn);
void lasta(const XReg &rd, const _PReg &pg, const ZRegD &zn);
void lastb(const WReg &rd, const _PReg &pg, const ZRegB &zn);
void lastb(const WReg &rd, const _PReg &pg, const ZRegH &zn);
void lastb(const WReg &rd, const _PReg &pg, const ZRegS &zn);
void lastb(const XReg &rd, const _PReg &pg, const ZRegD &zn);
void revb(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void revb(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void revb(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void revh(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void revh(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void revw(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void rbit(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void rbit(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void rbit(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void rbit(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void splice(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void splice(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void splice(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void splice(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sel(const ZRegB &zd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void sel(const ZRegH &zd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void sel(const ZRegS &zd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void sel(const ZRegD &zd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void mov(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void mov(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void mov(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void mov(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void cmphs(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmphs(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmphs(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmphs(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmphi(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmphi(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmphi(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmphi(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpeq(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmpeq(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmpeq(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmpne(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmpne(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmpne(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmpge(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmpge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmpge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmpge(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpgt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmpgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmpgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmpgt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpeq(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmpeq(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmpeq(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmpeq(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpne(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmpne(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmpne(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmpne(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmple(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmple(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmple(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmple(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmplo(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmplo(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmplo(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmplo(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpls(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmpls(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmpls(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmpls(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmplt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void cmplt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void cmplt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void cmplt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void cmpge(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmpge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmpge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmpgt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmpgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmpgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmplt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmplt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmplt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmple(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmple(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmple(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmphs(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmphs(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmphs(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmphi(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmphi(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmphi(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmplo(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmplo(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmplo(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmpls(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegD &zm);
void cmpls(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegD &zm);
void cmpls(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegD &zm);
void cmphs(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const uint32_t imm);
void cmphs(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const uint32_t imm);
void cmphs(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const uint32_t imm);
void cmphs(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const uint32_t imm);
void cmphi(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const uint32_t imm);
void cmphi(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const uint32_t imm);
void cmphi(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const uint32_t imm);
void cmphi(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const uint32_t imm);
void cmplo(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const uint32_t imm);
void cmplo(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const uint32_t imm);
void cmplo(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const uint32_t imm);
void cmplo(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const uint32_t imm);
void cmpls(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const uint32_t imm);
void cmpls(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const uint32_t imm);
void cmpls(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const uint32_t imm);
void cmpls(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const uint32_t imm);
void and_(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void mov(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void bic(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void eor(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void not_(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void sel(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void ands(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void movs(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void bics(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void eors(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void nots(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void orr(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void mov(const PRegB &pd, const PRegB &pn);
void orn(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void nor(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void nand(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void orrs(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void movs(const PRegB &pd, const PRegB &pn);
void orns(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void nors(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void nands(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void brkpa(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void brkpb(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void brkpas(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void brkpbs(const PRegB &pd, const _PReg &pg, const PRegB &pn, const PRegB &pm);
void brka(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void brkas(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void brkb(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void brkbs(const PRegB &pd, const _PReg &pg, const PRegB &pn);
void brkn(const PRegB &pdm, const _PReg &pg, const PRegB &pn);
void brkns(const PRegB &pdm, const _PReg &pg, const PRegB &pn);
void pfirst(const PRegB &pdn, const _PReg &pg);
void ptrue(const PRegB &pd, const Pattern pat = ALL);
void ptrue(const PRegH &pd, const Pattern pat = ALL);
void ptrue(const PRegS &pd, const Pattern pat = ALL);
void ptrue(const PRegD &pd, const Pattern pat = ALL);
void ptrues(const PRegB &pd, const Pattern pat = ALL);
void ptrues(const PRegH &pd, const Pattern pat = ALL);
void ptrues(const PRegS &pd, const Pattern pat = ALL);
void ptrues(const PRegD &pd, const Pattern pat = ALL);
void pnext(const PRegB &pdn, const _PReg &pg);
void pnext(const PRegH &pdn, const _PReg &pg);
void pnext(const PRegS &pdn, const _PReg &pg);
void pnext(const PRegD &pdn, const _PReg &pg);
void rdffr(const PRegB &pd, const _PReg &pg);
void rdffrs(const PRegB &pd, const _PReg &pg);
void rdffr(const PRegB &pd);
void ptest(const _PReg &pg, const PRegB &pn);
void pfalse(const PRegB &pd);
void cmpge(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmpge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmpge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmpge(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cmpgt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmpgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmpgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmpgt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cmplt(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmplt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmplt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmplt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cmple(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmple(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmple(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmple(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cmpeq(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmpeq(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmpeq(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmpeq(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cmpne(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const int32_t imm);
void cmpne(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const int32_t imm);
void cmpne(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const int32_t imm);
void cmpne(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const int32_t imm);
void cntp(const XReg &rd, const _PReg &pg, const PRegB &pn);
void cntp(const XReg &rd, const _PReg &pg, const PRegH &pn);
void cntp(const XReg &rd, const _PReg &pg, const PRegS &pn);
void cntp(const XReg &rd, const _PReg &pg, const PRegD &pn);
void incp(const XReg &xdn, const PRegB &pg);
void incp(const XReg &xdn, const PRegH &pg);
void incp(const XReg &xdn, const PRegS &pg);
void incp(const XReg &xdn, const PRegD &pg);
void decp(const XReg &xdn, const PRegB &pg);
void decp(const XReg &xdn, const PRegH &pg);
void decp(const XReg &xdn, const PRegS &pg);
void decp(const XReg &xdn, const PRegD &pg);
void incp(const ZRegH &zdn, const _PReg &pg);
void incp(const ZRegS &zdn, const _PReg &pg);
void incp(const ZRegD &zdn, const _PReg &pg);
void decp(const ZRegH &zdn, const _PReg &pg);
void decp(const ZRegS &zdn, const _PReg &pg);
void decp(const ZRegD &zdn, const _PReg &pg);
void sqincp(const WReg &rdn, const PRegB &pg);
void sqincp(const WReg &rdn, const PRegH &pg);
void sqincp(const WReg &rdn, const PRegS &pg);
void sqincp(const WReg &rdn, const PRegD &pg);
void sqincp(const XReg &rdn, const PRegB &pg);
void sqincp(const XReg &rdn, const PRegH &pg);
void sqincp(const XReg &rdn, const PRegS &pg);
void sqincp(const XReg &rdn, const PRegD &pg);
void uqincp(const WReg &rdn, const PRegB &pg);
void uqincp(const WReg &rdn, const PRegH &pg);
void uqincp(const WReg &rdn, const PRegS &pg);
void uqincp(const WReg &rdn, const PRegD &pg);
void uqincp(const XReg &rdn, const PRegB &pg);
void uqincp(const XReg &rdn, const PRegH &pg);
void uqincp(const XReg &rdn, const PRegS &pg);
void uqincp(const XReg &rdn, const PRegD &pg);
void sqdecp(const WReg &rdn, const PRegB &pg);
void sqdecp(const WReg &rdn, const PRegH &pg);
void sqdecp(const WReg &rdn, const PRegS &pg);
void sqdecp(const WReg &rdn, const PRegD &pg);
void sqdecp(const XReg &rdn, const PRegB &pg);
void sqdecp(const XReg &rdn, const PRegH &pg);
void sqdecp(const XReg &rdn, const PRegS &pg);
void sqdecp(const XReg &rdn, const PRegD &pg);
void uqdecp(const WReg &rdn, const PRegB &pg);
void uqdecp(const WReg &rdn, const PRegH &pg);
void uqdecp(const WReg &rdn, const PRegS &pg);
void uqdecp(const WReg &rdn, const PRegD &pg);
void uqdecp(const XReg &rdn, const PRegB &pg);
void uqdecp(const XReg &rdn, const PRegH &pg);
void uqdecp(const XReg &rdn, const PRegS &pg);
void uqdecp(const XReg &rdn, const PRegD &pg);
void sqincp(const ZRegH &zdn, const _PReg &pg);
void sqincp(const ZRegS &zdn, const _PReg &pg);
void sqincp(const ZRegD &zdn, const _PReg &pg);
void uqincp(const ZRegH &zdn, const _PReg &pg);
void uqincp(const ZRegS &zdn, const _PReg &pg);
void uqincp(const ZRegD &zdn, const _PReg &pg);
void sqdecp(const ZRegH &zdn, const _PReg &pg);
void sqdecp(const ZRegS &zdn, const _PReg &pg);
void sqdecp(const ZRegD &zdn, const _PReg &pg);
void uqdecp(const ZRegH &zdn, const _PReg &pg);
void uqdecp(const ZRegS &zdn, const _PReg &pg);
void uqdecp(const ZRegD &zdn, const _PReg &pg);
void setffr();
void wrffr(const PRegB &pn);
void whilege(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilege(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilege(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilege(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilege(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilege(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilege(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilege(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilegt(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilegt(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilegt(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilegt(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilegt(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilegt(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilegt(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilegt(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilelt(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilelt(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilelt(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilelt(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilelt(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilelt(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilelt(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilelt(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilele(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilele(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilele(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilele(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilele(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilele(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilele(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilele(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilehs(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilehs(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilehs(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilehs(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilehs(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilehs(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilehs(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilehs(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilehi(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilehi(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilehi(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilehi(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilehi(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilehi(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilehi(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilehi(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilelo(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilelo(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilelo(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilelo(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilelo(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilelo(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilelo(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilelo(const PRegD &pd, const XReg &rn, const XReg &rm);
void whilels(const PRegB &pd, const WReg &rn, const WReg &rm);
void whilels(const PRegH &pd, const WReg &rn, const WReg &rm);
void whilels(const PRegS &pd, const WReg &rn, const WReg &rm);
void whilels(const PRegD &pd, const WReg &rn, const WReg &rm);
void whilels(const PRegB &pd, const XReg &rn, const XReg &rm);
void whilels(const PRegH &pd, const XReg &rn, const XReg &rm);
void whilels(const PRegS &pd, const XReg &rn, const XReg &rm);
void whilels(const PRegD &pd, const XReg &rn, const XReg &rm);
void ctermeq(const WReg &rn, const WReg &rm);
void ctermeq(const XReg &rn, const XReg &rm);
void ctermne(const WReg &rn, const WReg &rm);
void ctermne(const XReg &rn, const XReg &rm);
void whilewr(const _PReg &pd, const XReg &xn, const XReg &xm);
void whilerw(const _PReg &pd, const XReg &xn, const XReg &xm);
void fdup(const ZRegH &zd, const double imm);
void fdup(const ZRegS &zd, const double imm);
void fdup(const ZRegD &zd, const double imm);
void fmov(const ZRegH &zd, const double imm);
void fmov(const ZRegS &zd, const double imm);
void fmov(const ZRegD &zd, const double imm);
void dup(const ZRegB &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void dup(const ZRegH &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void dup(const ZRegS &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void dup(const ZRegD &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegB &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegH &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegS &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void mov(const ZRegD &zd, const int32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void fmov(const ZRegB &zd, const float imm = 0.0);
void fmov(const ZRegH &zd, const float imm = 0.0);
void fmov(const ZRegS &zd, const float imm = 0.0);
void fmov(const ZRegD &zd, const float imm = 0.0);
void add(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void add(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void add(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void add(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sub(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sub(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sub(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sub(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void subr(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void subr(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void subr(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void subr(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqadd(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqadd(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqadd(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqadd(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqadd(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqadd(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqadd(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqadd(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqsub(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqsub(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqsub(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void sqsub(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqsub(const ZRegB &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqsub(const ZRegH &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqsub(const ZRegS &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void uqsub(const ZRegD &zdn, const uint32_t imm, const ShMod mod = LSL, const uint32_t sh = 0);
void smax(const ZRegB &zdn, const int32_t imm);
void smax(const ZRegH &zdn, const int32_t imm);
void smax(const ZRegS &zdn, const int32_t imm);
void smax(const ZRegD &zdn, const int32_t imm);
void umax(const ZRegB &zdn, const int32_t imm);
void umax(const ZRegH &zdn, const int32_t imm);
void umax(const ZRegS &zdn, const int32_t imm);
void umax(const ZRegD &zdn, const int32_t imm);
void smin(const ZRegB &zdn, const int32_t imm);
void smin(const ZRegH &zdn, const int32_t imm);
void smin(const ZRegS &zdn, const int32_t imm);
void smin(const ZRegD &zdn, const int32_t imm);
void umin(const ZRegB &zdn, const int32_t imm);
void umin(const ZRegH &zdn, const int32_t imm);
void umin(const ZRegS &zdn, const int32_t imm);
void umin(const ZRegD &zdn, const int32_t imm);
void mul(const ZRegB &zdn, const int32_t imm);
void mul(const ZRegH &zdn, const int32_t imm);
void mul(const ZRegS &zdn, const int32_t imm);
void mul(const ZRegD &zdn, const int32_t imm);
void sdot(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void sdot(const ZRegD &zda, const ZRegH &zn, const ZRegH &zm);
void udot(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void udot(const ZRegD &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlalbt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlalbt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlalbt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqdmlslbt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlslbt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlslbt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void cdot(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm, const uint32_t rot);
void cdot(const ZRegD &zda, const ZRegH &zn, const ZRegH &zm, const uint32_t rot);
void cmla(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm, const uint32_t rot);
void cmla(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm, const uint32_t rot);
void cmla(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm, const uint32_t rot);
void cmla(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm, const uint32_t rot);
void sqrdcmlah(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm, const uint32_t rot);
void sqrdcmlah(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm, const uint32_t rot);
void sqrdcmlah(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm, const uint32_t rot);
void sqrdcmlah(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm, const uint32_t rot);
void smlalb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void smlalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void smlalb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void smlalt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void smlalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void smlalt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void umlalb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void umlalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void umlalb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void umlalt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void umlalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void umlalt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void smlslb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void smlslb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void smlslb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void smlslt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void smlslt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void smlslt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void umlslb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void umlslb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void umlslb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void umlslt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void umlslt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void umlslt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqdmlalb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlalb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqdmlalt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlalt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqdmlslb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlslb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlslb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqdmlslt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sqdmlslt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sqdmlslt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sqrdmlah(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm);
void sqrdmlah(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm);
void sqrdmlah(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void sqrdmlah(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void sqrdmlsh(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm);
void sqrdmlsh(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm);
void sqrdmlsh(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void sqrdmlsh(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void usdot(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void sadalp(const ZRegH &zda, const _PReg &pg, const ZRegB &zn);
void sadalp(const ZRegS &zda, const _PReg &pg, const ZRegH &zn);
void sadalp(const ZRegD &zda, const _PReg &pg, const ZRegS &zn);
void uadalp(const ZRegH &zda, const _PReg &pg, const ZRegB &zn);
void uadalp(const ZRegS &zda, const _PReg &pg, const ZRegH &zn);
void uadalp(const ZRegD &zda, const _PReg &pg, const ZRegS &zn);
void urecpe(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void ursqrte(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void sqabs(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void sqabs(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void sqabs(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void sqabs(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void sqneg(const ZRegB &zd, const _PReg &pg, const ZRegB &zn);
void sqneg(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void sqneg(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void sqneg(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void srshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void srshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void srshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void srshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void urshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void urshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void urshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void urshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void srshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void srshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void srshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void srshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void urshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void urshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void urshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void urshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqrshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqrshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqrshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqrshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqrshl(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqrshl(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqrshl(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqrshl(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqrshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqrshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqrshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqrshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqrshlr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqrshlr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqrshlr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqrshlr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void shadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void shadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void shadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void shadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uhadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uhadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uhadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uhadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void shsub(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void shsub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void shsub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void shsub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uhsub(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uhsub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uhsub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uhsub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void srhadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void srhadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void srhadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void srhadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void urhadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void urhadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void urhadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void urhadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void shsubr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void shsubr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void shsubr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void shsubr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uhsubr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uhsubr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uhsubr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uhsubr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void addp(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void addp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void addp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void addp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void smaxp(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void smaxp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void smaxp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void smaxp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void umaxp(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void umaxp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void umaxp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void umaxp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sminp(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sminp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sminp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sminp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uminp(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uminp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uminp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uminp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqsub(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqsub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqsub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqsub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqsub(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqsub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqsub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqsub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void suqadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void suqadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void suqadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void suqadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void usqadd(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void usqadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void usqadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void usqadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sqsubr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void sqsubr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void sqsubr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void sqsubr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void uqsubr(const ZRegB &zdn, const _PReg &pg, const ZRegB &zm);
void uqsubr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void uqsubr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void uqsubr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void sdot(const ZRegS &zda, const ZRegB &zn, const ZRegBElem &zm);
void sdot(const ZRegD &zda, const ZRegH &zn, const ZRegHElem &zm);
void udot(const ZRegS &zda, const ZRegB &zn, const ZRegBElem &zm);
void udot(const ZRegD &zda, const ZRegH &zn, const ZRegHElem &zm);
void mla(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void mla(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void mla(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void mls(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void mls(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void mls(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void sqrdmlah(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqrdmlah(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqrdmlah(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void sqrdmlsh(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqrdmlsh(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqrdmlsh(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void usdot(const ZRegS &zda, const ZRegB &zn, const ZRegBElem &zm);
void sudot(const ZRegS &zda, const ZRegB &zn, const ZRegBElem &zm);
void sqdmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmlalb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmlalt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmlslb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmlslb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmlslt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmlslt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void cdot(const ZRegS &zda, const ZRegB &zn, const ZRegBElem &zm, const uint32_t rot);
void cdot(const ZRegD &zda, const ZRegH &zn, const ZRegHElem &zm, const uint32_t rot);
void cmla(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm, const uint32_t rot);
void cmla(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm, const uint32_t rot);
void sqrdcmlah(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm, const uint32_t rot);
void sqrdcmlah(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm, const uint32_t rot);
void smlalb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smlalb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void smlalt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smlalt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umlalb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umlalb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umlalt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umlalt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void smlslb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smlslb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void smlslt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smlslt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umlslb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umlslb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umlslt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umlslt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void smullb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smullb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void smullt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void smullt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umullb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umullb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void umullt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void umullt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmullb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmullb(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmullt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmullt(const ZRegD &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmulh(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqdmulh(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqdmulh(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void sqrdmulh(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void sqrdmulh(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void sqrdmulh(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void mul(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void mul(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void mul(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void saddlb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void saddlb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void saddlb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void saddlt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void saddlt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void saddlt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void uaddlb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void uaddlb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void uaddlb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void uaddlt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void uaddlt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void uaddlt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void ssublb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void ssublb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void ssublb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void ssublt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void ssublt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void ssublt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void usublb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void usublb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void usublb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void usublt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void usublt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void usublt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void sabdlb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void sabdlb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void sabdlb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void sabdlt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void sabdlt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void sabdlt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void uabdlb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void uabdlb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void uabdlb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void uabdlt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void uabdlt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void uabdlt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void saddwb(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void saddwb(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void saddwb(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void saddwt(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void saddwt(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void saddwt(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void uaddwb(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void uaddwb(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void uaddwb(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void uaddwt(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void uaddwt(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void uaddwt(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void ssubwb(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void ssubwb(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void ssubwb(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void ssubwt(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void ssubwt(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void ssubwt(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void usubwb(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void usubwb(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void usubwb(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void usubwt(const ZRegH &zd, const ZRegH &zn, const ZRegB &zm);
void usubwt(const ZRegS &zd, const ZRegS &zn, const ZRegH &zm);
void usubwt(const ZRegD &zd, const ZRegD &zn, const ZRegS &zm);
void sqdmullb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void sqdmullb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void sqdmullb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void sqdmullt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void sqdmullt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void sqdmullt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void pmullb(const ZRegQ &zd, const ZRegD &zn, const ZRegD &zm);
void pmullb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void pmullb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void pmullt(const ZRegQ &zd, const ZRegD &zn, const ZRegD &zm);
void pmullt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void pmullt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void smullb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void smullb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void smullb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void smullt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void smullt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void smullt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void umullb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void umullb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void umullb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void umullt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void umullt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void umullt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void sshllb(const ZRegH &zd, const ZRegB &zn, const uint32_t imm6);
void sshllb(const ZRegS &zd, const ZRegH &zn, const uint32_t imm6);
void sshllb(const ZRegD &zd, const ZRegS &zn, const uint32_t imm6);
void sshllt(const ZRegH &zd, const ZRegB &zn, const uint32_t imm6);
void sshllt(const ZRegS &zd, const ZRegH &zn, const uint32_t imm6);
void sshllt(const ZRegD &zd, const ZRegS &zn, const uint32_t imm6);
void ushllb(const ZRegH &zd, const ZRegB &zn, const uint32_t imm6);
void ushllb(const ZRegS &zd, const ZRegH &zn, const uint32_t imm6);
void ushllb(const ZRegD &zd, const ZRegS &zn, const uint32_t imm6);
void ushllt(const ZRegH &zd, const ZRegB &zn, const uint32_t imm6);
void ushllt(const ZRegS &zd, const ZRegH &zn, const uint32_t imm6);
void ushllt(const ZRegD &zd, const ZRegS &zn, const uint32_t imm6);
void saddlbt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void saddlbt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void saddlbt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void ssublbt(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void ssublbt(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void ssublbt(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void ssubltb(const ZRegH &zd, const ZRegB &zn, const ZRegB &zm);
void ssubltb(const ZRegS &zd, const ZRegH &zn, const ZRegH &zm);
void ssubltb(const ZRegD &zd, const ZRegS &zn, const ZRegS &zm);
void eorbt(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void eorbt(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void eorbt(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void eorbt(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void eortb(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void eortb(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void eortb(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void eortb(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void smmla(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void usmmla(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void ummla(const ZRegS &zda, const ZRegB &zn, const ZRegB &zm);
void bext(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void bext(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void bext(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void bext(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void bdep(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void bdep(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void bdep(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void bdep(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void bgrp(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void bgrp(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void bgrp(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void bgrp(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void cadd(const ZRegB &zdn, const ZRegB &zm, const uint32_t rot);
void cadd(const ZRegH &zdn, const ZRegH &zm, const uint32_t rot);
void cadd(const ZRegS &zdn, const ZRegS &zm, const uint32_t rot);
void cadd(const ZRegD &zdn, const ZRegD &zm, const uint32_t rot);
void sqcadd(const ZRegB &zdn, const ZRegB &zm, const uint32_t rot);
void sqcadd(const ZRegH &zdn, const ZRegH &zm, const uint32_t rot);
void sqcadd(const ZRegS &zdn, const ZRegS &zm, const uint32_t rot);
void sqcadd(const ZRegD &zdn, const ZRegD &zm, const uint32_t rot);
void sabalb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sabalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sabalb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void sabalt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void sabalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void sabalt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void uabalb(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void uabalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void uabalb(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void uabalt(const ZRegH &zda, const ZRegB &zn, const ZRegB &zm);
void uabalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void uabalt(const ZRegD &zda, const ZRegS &zn, const ZRegS &zm);
void adclb(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void adclb(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void adclt(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void adclt(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void sbclb(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void sbclb(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void sbclt(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void sbclt(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void ssra(const ZRegB &zda, const ZRegB &zn, const uint32_t imm6);
void ssra(const ZRegH &zda, const ZRegH &zn, const uint32_t imm6);
void ssra(const ZRegS &zda, const ZRegS &zn, const uint32_t imm6);
void ssra(const ZRegD &zda, const ZRegD &zn, const uint32_t imm6);
void usra(const ZRegB &zda, const ZRegB &zn, const uint32_t imm6);
void usra(const ZRegH &zda, const ZRegH &zn, const uint32_t imm6);
void usra(const ZRegS &zda, const ZRegS &zn, const uint32_t imm6);
void usra(const ZRegD &zda, const ZRegD &zn, const uint32_t imm6);
void srsra(const ZRegB &zda, const ZRegB &zn, const uint32_t imm6);
void srsra(const ZRegH &zda, const ZRegH &zn, const uint32_t imm6);
void srsra(const ZRegS &zda, const ZRegS &zn, const uint32_t imm6);
void srsra(const ZRegD &zda, const ZRegD &zn, const uint32_t imm6);
void ursra(const ZRegB &zda, const ZRegB &zn, const uint32_t imm6);
void ursra(const ZRegH &zda, const ZRegH &zn, const uint32_t imm6);
void ursra(const ZRegS &zda, const ZRegS &zn, const uint32_t imm6);
void ursra(const ZRegD &zda, const ZRegD &zn, const uint32_t imm6);
void sri(const ZRegB &zd, const ZRegB &zn, const uint32_t imm6);
void sri(const ZRegH &zd, const ZRegH &zn, const uint32_t imm6);
void sri(const ZRegS &zd, const ZRegS &zn, const uint32_t imm6);
void sri(const ZRegD &zd, const ZRegD &zn, const uint32_t imm6);
void sli(const ZRegB &zd, const ZRegB &zn, const uint32_t imm6);
void sli(const ZRegH &zd, const ZRegH &zn, const uint32_t imm6);
void sli(const ZRegS &zd, const ZRegS &zn, const uint32_t imm6);
void sli(const ZRegD &zd, const ZRegD &zn, const uint32_t imm6);
void saba(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm);
void saba(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm);
void saba(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void saba(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void uaba(const ZRegB &zda, const ZRegB &zn, const ZRegB &zm);
void uaba(const ZRegH &zda, const ZRegH &zn, const ZRegH &zm);
void uaba(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void uaba(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void sqxtnb(const ZRegB &zd, const ZRegH &zn);
void sqxtnb(const ZRegH &zd, const ZRegS &zn);
void sqxtnb(const ZRegS &zd, const ZRegD &zn);
void sqxtnt(const ZRegB &zd, const ZRegH &zn);
void sqxtnt(const ZRegH &zd, const ZRegS &zn);
void sqxtnt(const ZRegS &zd, const ZRegD &zn);
void uqxtnb(const ZRegB &zd, const ZRegH &zn);
void uqxtnb(const ZRegH &zd, const ZRegS &zn);
void uqxtnb(const ZRegS &zd, const ZRegD &zn);
void uqxtnt(const ZRegB &zd, const ZRegH &zn);
void uqxtnt(const ZRegH &zd, const ZRegS &zn);
void uqxtnt(const ZRegS &zd, const ZRegD &zn);
void sqxtunb(const ZRegB &zd, const ZRegH &zn);
void sqxtunb(const ZRegH &zd, const ZRegS &zn);
void sqxtunb(const ZRegS &zd, const ZRegD &zn);
void sqxtunt(const ZRegB &zd, const ZRegH &zn);
void sqxtunt(const ZRegH &zd, const ZRegS &zn);
void sqxtunt(const ZRegS &zd, const ZRegD &zn);
void sqshrunb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqshrunb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqshrunb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqshrunt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqshrunt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqshrunt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqrshrunb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqrshrunb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqrshrunb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqrshrunt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqrshrunt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqrshrunt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void shrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void shrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void shrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void shrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void shrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void shrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void rshrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void rshrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void rshrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void rshrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void rshrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void rshrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqshrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqshrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqshrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqshrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqshrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqshrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqrshrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqrshrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqrshrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void sqrshrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void sqrshrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void sqrshrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void uqshrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void uqshrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void uqshrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void uqshrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void uqshrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void uqshrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void uqrshrnb(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void uqrshrnb(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void uqrshrnb(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void uqrshrnt(const ZRegB &zd, const ZRegH &zn, const uint32_t imm6);
void uqrshrnt(const ZRegH &zd, const ZRegS &zn, const uint32_t imm6);
void uqrshrnt(const ZRegS &zd, const ZRegD &zn, const uint32_t imm6);
void addhnb(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void addhnb(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void addhnb(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void addhnt(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void addhnt(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void addhnt(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void raddhnb(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void raddhnb(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void raddhnb(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void raddhnt(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void raddhnt(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void raddhnt(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void subhnb(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void subhnb(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void subhnb(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void subhnt(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void subhnt(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void subhnt(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void rsubhnb(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void rsubhnb(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void rsubhnb(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void rsubhnt(const ZRegB &zd, const ZRegH &zn, const ZRegH &zm);
void rsubhnt(const ZRegH &zd, const ZRegS &zn, const ZRegS &zm);
void rsubhnt(const ZRegS &zd, const ZRegD &zn, const ZRegD &zm);
void match(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void match(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void nmatch(const PRegB &pd, const _PReg &pg, const ZRegB &zn, const ZRegB &zm);
void nmatch(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void histseg(const ZRegB &zd, const ZRegB &zn, const ZRegB &zm);
void histcnt(const ZRegS &zd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void histcnt(const ZRegD &zd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void aesmc(const ZRegB &zdn);
void aesimc(const ZRegB &zdn);
void aese(const ZRegB &zdn, const ZRegB &zm);
void aesd(const ZRegB &zdn, const ZRegB &zm);
void sm4e(const ZRegS &zdn, const ZRegS &zm);
void sm4ekey(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void rax1(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void fcvtxnt(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void fcvtnt(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void fcvtnt(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void fcvtlt(const ZRegS &zd, const _PReg &pg, const ZRegH &zn);
void fcvtlt(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void bfcvtnt(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void faddp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void faddp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void faddp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmaxnmp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmaxnmp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmaxnmp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fminnmp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fminnmp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fminnmp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmaxp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmaxp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmaxp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fminp(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fminp(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fminp(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void bfdot(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmlslb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmlslt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void bfmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void bfmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegHElem &zm);
void bfdot(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void fmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void fmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void fmlslb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void fmlslt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void bfmlalb(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void bfmlalt(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void bfmmla(const ZRegS &zda, const ZRegH &zn, const ZRegH &zm);
void fmmla(const ZRegS &zda, const ZRegS &zn, const ZRegS &zm);
void fmmla(const ZRegD &zda, const ZRegD &zn, const ZRegD &zm);
void fcadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const uint32_t ct);
void fcadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const uint32_t ct);
void fcadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const uint32_t ct);
void fcmla(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm, const uint32_t ct);
void fcmla(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm, const uint32_t ct);
void fcmla(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm, const uint32_t ct);
void fmla(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmla(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void fmla(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void fmls(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm);
void fmls(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm);
void fmls(const ZRegD &zda, const ZRegD &zn, const ZRegDElem &zm);
void fcmla(const ZRegH &zda, const ZRegH &zn, const ZRegHElem &zm, const uint32_t ct);
void fcmla(const ZRegS &zda, const ZRegS &zn, const ZRegSElem &zm, const uint32_t ct);
void fmul(const ZRegH &zd, const ZRegH &zn, const ZRegHElem &zm);
void fmul(const ZRegS &zd, const ZRegS &zn, const ZRegSElem &zm);
void fmul(const ZRegD &zd, const ZRegD &zn, const ZRegDElem &zm);
void faddv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void faddv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void faddv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void fmaxnmv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void fmaxnmv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void fmaxnmv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void fminnmv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void fminnmv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void fminnmv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void fmaxv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void fmaxv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void fmaxv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void fminv(const HReg &vd, const _PReg &pg, const ZRegH &zn);
void fminv(const SReg &vd, const _PReg &pg, const ZRegS &zn);
void fminv(const DReg &vd, const _PReg &pg, const ZRegD &zn);
void frecpe(const ZRegH &zd, const ZRegH &zn);
void frecpe(const ZRegS &zd, const ZRegS &zn);
void frecpe(const ZRegD &zd, const ZRegD &zn);
void frsqrte(const ZRegH &zd, const ZRegH &zn);
void frsqrte(const ZRegS &zd, const ZRegS &zn);
void frsqrte(const ZRegD &zd, const ZRegD &zn);
void fcmge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmge(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fcmgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmgt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fcmlt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmlt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmlt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fcmle(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmle(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmle(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fcmeq(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmeq(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmeq(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fcmne(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const double zero);
void fcmne(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const double zero);
void fcmne(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const double zero);
void fadda(const HReg &vdn, const _PReg &pg, const ZRegH &zm);
void fadda(const SReg &vdn, const _PReg &pg, const ZRegS &zm);
void fadda(const DReg &vdn, const _PReg &pg, const ZRegD &zm);
void fadd(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void fadd(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void fadd(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void fsub(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void fsub(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void fsub(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void fmul(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void fmul(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void fmul(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void ftsmul(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void ftsmul(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void ftsmul(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void frecps(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void frecps(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void frecps(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void frsqrts(const ZRegH &zd, const ZRegH &zn, const ZRegH &zm);
void frsqrts(const ZRegS &zd, const ZRegS &zn, const ZRegS &zm);
void frsqrts(const ZRegD &zd, const ZRegD &zn, const ZRegD &zm);
void fadd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fadd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fadd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fsub(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fsub(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fsub(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmul(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmul(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmul(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fsubr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fsubr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fsubr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmaxnm(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmaxnm(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmaxnm(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fminnm(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fminnm(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fminnm(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmax(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmax(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmax(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmin(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmin(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmin(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fabd(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fabd(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fabd(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fscale(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fscale(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fscale(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fmulx(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fmulx(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fmulx(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fdivr(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fdivr(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fdivr(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fdiv(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm);
void fdiv(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm);
void fdiv(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm);
void fadd(const ZRegH &zdn, const _PReg &pg, const float ct);
void fadd(const ZRegS &zdn, const _PReg &pg, const float ct);
void fadd(const ZRegD &zdn, const _PReg &pg, const float ct);
void fsub(const ZRegH &zdn, const _PReg &pg, const float ct);
void fsub(const ZRegS &zdn, const _PReg &pg, const float ct);
void fsub(const ZRegD &zdn, const _PReg &pg, const float ct);
void fmul(const ZRegH &zdn, const _PReg &pg, const float ct);
void fmul(const ZRegS &zdn, const _PReg &pg, const float ct);
void fmul(const ZRegD &zdn, const _PReg &pg, const float ct);
void fsubr(const ZRegH &zdn, const _PReg &pg, const float ct);
void fsubr(const ZRegS &zdn, const _PReg &pg, const float ct);
void fsubr(const ZRegD &zdn, const _PReg &pg, const float ct);
void fmaxnm(const ZRegH &zdn, const _PReg &pg, const float ct);
void fmaxnm(const ZRegS &zdn, const _PReg &pg, const float ct);
void fmaxnm(const ZRegD &zdn, const _PReg &pg, const float ct);
void fminnm(const ZRegH &zdn, const _PReg &pg, const float ct);
void fminnm(const ZRegS &zdn, const _PReg &pg, const float ct);
void fminnm(const ZRegD &zdn, const _PReg &pg, const float ct);
void fmax(const ZRegH &zdn, const _PReg &pg, const float ct);
void fmax(const ZRegS &zdn, const _PReg &pg, const float ct);
void fmax(const ZRegD &zdn, const _PReg &pg, const float ct);
void fmin(const ZRegH &zdn, const _PReg &pg, const float ct);
void fmin(const ZRegS &zdn, const _PReg &pg, const float ct);
void fmin(const ZRegD &zdn, const _PReg &pg, const float ct);
void ftmad(const ZRegH &zdn, const ZRegH &zm, const uint32_t imm);
void ftmad(const ZRegS &zdn, const ZRegS &zm, const uint32_t imm);
void ftmad(const ZRegD &zdn, const ZRegD &zm, const uint32_t imm);
void fcvt(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void fcvt(const ZRegS &zd, const _PReg &pg, const ZRegH &zn);
void fcvt(const ZRegH &zd, const _PReg &pg, const ZRegD &zn);
void fcvt(const ZRegD &zd, const _PReg &pg, const ZRegH &zn);
void fcvt(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void fcvt(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void fcvtx(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void bfcvt(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void fcvtzs(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzu(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzs(const ZRegS &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzu(const ZRegS &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzs(const ZRegD &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzu(const ZRegD &zd, const _PReg &pg, const ZRegH &zn);
void fcvtzs(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void fcvtzu(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void fcvtzs(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void fcvtzu(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void fcvtzs(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void fcvtzu(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void fcvtzs(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void fcvtzu(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void flogb(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void flogb(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void flogb(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frintn(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frintn(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frintn(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frintp(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frintp(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frintp(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frintm(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frintm(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frintm(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frintz(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frintz(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frintz(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frinta(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frinta(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frinta(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frintx(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frintx(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frintx(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frinti(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frinti(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frinti(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void frecpx(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void frecpx(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void frecpx(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void fsqrt(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void fsqrt(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void fsqrt(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void scvtf(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void ucvtf(const ZRegH &zd, const _PReg &pg, const ZRegH &zn);
void scvtf(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void ucvtf(const ZRegH &zd, const _PReg &pg, const ZRegS &zn);
void scvtf(const ZRegH &zd, const _PReg &pg, const ZRegD &zn);
void ucvtf(const ZRegH &zd, const _PReg &pg, const ZRegD &zn);
void scvtf(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void ucvtf(const ZRegS &zd, const _PReg &pg, const ZRegS &zn);
void scvtf(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void ucvtf(const ZRegD &zd, const _PReg &pg, const ZRegS &zn);
void scvtf(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void ucvtf(const ZRegS &zd, const _PReg &pg, const ZRegD &zn);
void scvtf(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void ucvtf(const ZRegD &zd, const _PReg &pg, const ZRegD &zn);
void fcmge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmge(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmgt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmle(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmle(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmle(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmlt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmlt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmlt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmeq(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmeq(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmeq(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmne(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmne(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmne(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fcmuo(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fcmuo(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fcmuo(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void facge(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void facge(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void facge(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void facgt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void facgt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void facgt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void facle(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void facle(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void facle(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void faclt(const PRegH &pd, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void faclt(const PRegS &pd, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void faclt(const PRegD &pd, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fmla(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fmla(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fmla(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fmls(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fmls(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fmls(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fnmla(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fnmla(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fnmla(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fnmls(const ZRegH &zda, const _PReg &pg, const ZRegH &zn, const ZRegH &zm);
void fnmls(const ZRegS &zda, const _PReg &pg, const ZRegS &zn, const ZRegS &zm);
void fnmls(const ZRegD &zda, const _PReg &pg, const ZRegD &zn, const ZRegD &zm);
void fmad(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void fmad(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void fmad(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void fmsb(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void fmsb(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void fmsb(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void fnmad(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void fnmad(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void fnmad(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void fnmsb(const ZRegH &zdn, const _PReg &pg, const ZRegH &zm, const ZRegH &za);
void fnmsb(const ZRegS &zdn, const _PReg &pg, const ZRegS &zm, const ZRegS &za);
void fnmsb(const ZRegD &zdn, const _PReg &pg, const ZRegD &zm, const ZRegD &za);
void ld1sb(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ldff1sb(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ld1b(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ldff1b(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ldff1sh(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ldff1h(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ldff1w(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void ld1sb(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ldff1sb(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ld1b(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ldff1b(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ldff1sh(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ldff1h(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ldff1w(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void ldff1sh(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void ldff1h(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void ldff1w(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32S &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32S &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32S &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32S &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm32 &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm32 &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm32 &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm32 &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrScImm &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrNoOfs &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrScImm &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrNoOfs &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrScImm &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrNoOfs &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrScImm &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrNoOfs &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrScSc &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrScSc &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrScSc &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrScSc &adr);
void ld1rb(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rb(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rb(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rb(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rb(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rb(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rb(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rb(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsw(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsw(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rh(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rh(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rh(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rh(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rh(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rh(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsh(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsh(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsh(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsh(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rw(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rw(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rw(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rw(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsb(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsb(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsb(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsb(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rsb(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rsb(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rd(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rd(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldr(const _PReg &pt, const AdrScImm &adr);
void ldr(const _PReg &pt, const AdrNoOfs &adr);
void ldr(const ZReg &zt, const AdrScImm &adr);
void ldr(const ZReg &zt, const AdrNoOfs &adr);
void ldff1b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1b(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1b(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1b(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1b(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1b(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1b(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1h(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1h(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sh(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sh(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sb(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sb(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sb(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sb(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1sb(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1sb(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1b(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1b(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1b(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1b(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sb(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sb(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1sb(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1sb(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ld1b(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld1b(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld1h(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sh(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sb(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sb(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ldnf1b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1b(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1b(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1b(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1b(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1b(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1b(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sw(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sw(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1h(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1h(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1h(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1h(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sh(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sh(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sh(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sh(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1w(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1w(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sb(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sb(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sb(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sb(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1sb(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1sb(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnf1d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnf1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnt1b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ldnt1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnt1h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ldnt1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnt1w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ldnt1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnt1d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ldnt1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ldnt1b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ldnt1h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ldnt1w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ldnt1d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1rqb(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rqb(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rqh(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rqh(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rqw(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rqw(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rqd(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld1rqd(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld1rqb(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ld1rqh(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld1rqw(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld1rqd(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld2b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld2b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld3b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld3b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld4b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void ld4b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld2h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld2h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld3h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld3h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld4h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void ld4h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld2w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld2w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld3w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld3w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld4w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void ld4w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld2d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld2d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld3d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld3d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld4d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void ld4d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void ld2b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ld3b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ld4b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void ld2h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld3h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld4h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void ld2w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld3w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld4w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void ld2d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld3d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld4d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1sb(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1b(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1sb(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1b(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void ldnt1sb(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1b(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1sh(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1h(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1sw(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1w(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1d(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void ldnt1sb(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void ldnt1b(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void ldnt1sh(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void ldnt1h(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void ldnt1w(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void ld1sb(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1sb(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1b(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1b(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1sh(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1sh(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1h(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1h(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1sw(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1sw(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1w(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1w(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ld1d(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void ldff1d(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc64S &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc64S &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc64S &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc64S &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32US &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32US &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32US &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrSc32US &adr);
void prfb(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm64 &adr);
void prfh(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm64 &adr);
void prfw(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm64 &adr);
void prfd(const PrfopSve prfop_sve, const _PReg &pg, const AdrVecImm64 &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrSc32S &adr);
void st1b(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrSc32U &adr);
void st1b(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrVecImm32 &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrSc64S &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrSc64U &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrSc32US &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrSc32UU &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrVecImm64 &adr);
void stnt1b(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void stnt1h(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void stnt1w(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void stnt1d(const ZRegD &zt, const _PReg &pg, const AdrVecSc64 &adr);
void stnt1b(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void stnt1h(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void stnt1w(const ZRegS &zt, const _PReg &pg, const AdrVecSc32 &adr);
void stnt1b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void stnt1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void stnt1h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void stnt1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void stnt1w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void stnt1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void stnt1d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void stnt1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void stnt1b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void stnt1h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void stnt1w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void stnt1d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st1b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void st1b(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void st1b(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st1b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1b(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1b(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st1h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st1b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void st1b(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void st1b(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st1b(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st1h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void st1h(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st1h(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st1w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st1w(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st1d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st2b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void st2b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void st3b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void st3b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void st4b(const ZRegB &zt, const _PReg &pg, const AdrScImm &adr);
void st4b(const ZRegB &zt, const _PReg &pg, const AdrNoOfs &adr);
void st2h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void st2h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void st3h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void st3h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void st4h(const ZRegH &zt, const _PReg &pg, const AdrScImm &adr);
void st4h(const ZRegH &zt, const _PReg &pg, const AdrNoOfs &adr);
void st2w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st2w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st3w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st3w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st4w(const ZRegS &zt, const _PReg &pg, const AdrScImm &adr);
void st4w(const ZRegS &zt, const _PReg &pg, const AdrNoOfs &adr);
void st2d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st2d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st3d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st3d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st4d(const ZRegD &zt, const _PReg &pg, const AdrScImm &adr);
void st4d(const ZRegD &zt, const _PReg &pg, const AdrNoOfs &adr);
void st2b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void st3b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void st4b(const ZRegB &zt, const _PReg &pg, const AdrScSc &adr);
void st2h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void st3h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void st4h(const ZRegH &zt, const _PReg &pg, const AdrScSc &adr);
void st2w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st3w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st4w(const ZRegS &zt, const _PReg &pg, const AdrScSc &adr);
void st2d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st3d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void st4d(const ZRegD &zt, const _PReg &pg, const AdrScSc &adr);
void str(const _PReg &pt, const AdrScImm &adr);
void str(const _PReg &pt, const AdrNoOfs &adr);
void str(const ZReg &zt, const AdrScImm &adr);
void str(const ZReg &zt, const AdrNoOfs &adr);
void beq(const Label &label);
void beq(int64_t label);
void bne(const Label &label);
void bne(int64_t label);
void bcs(const Label &label);
void bcs(int64_t label);
void bcc(const Label &label);
void bcc(int64_t label);
void bmi(const Label &label);
void bmi(int64_t label);
void bpl(const Label &label);
void bpl(int64_t label);
void bvs(const Label &label);
void bvs(int64_t label);
void bvc(const Label &label);
void bvc(int64_t label);
void bhi(const Label &label);
void bhi(int64_t label);
void bls(const Label &label);
void bls(int64_t label);
void bge(const Label &label);
void bge(int64_t label);
void blt(const Label &label);
void blt(int64_t label);
void bgt(const Label &label);
void bgt(int64_t label);
void ble(const Label &label);
void ble(int64_t label);
void b_none(const Label &label);
void b_none(int64_t label);
void b_any(const Label &label);
void b_any(int64_t label);
void b_nlast(const Label &label);
void b_nlast(int64_t label);
void b_last(const Label &label);
void b_last(int64_t label);
void b_first(const Label &label);
void b_first(int64_t label);
void b_nfrst(const Label &label);
void b_nfrst(int64_t label);
void b_pmore(const Label &label);
void b_pmore(int64_t label);
void b_plast(const Label &label);
void b_plast(int64_t label);
void b_tcont(const Label &label);
void b_tcont(int64_t label);
void b_tstop(const Label &label);
void b_tstop(int64_t label);
