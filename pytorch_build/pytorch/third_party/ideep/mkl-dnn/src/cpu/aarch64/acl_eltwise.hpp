/*******************************************************************************
* Copyright 2021-2024 Arm Ltd. and affiliates
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

#ifndef CPU_AARCH64_ACL_ELTWISE_HPP
#define CPU_AARCH64_ACL_ELTWISE_HPP

#include <memory>
#include "cpu/cpu_eltwise_pd.hpp"

#include "acl_utils.hpp"

#include "arm_compute/runtime/experimental/operators/CpuActivation.h"

namespace dnnl {
namespace impl {
namespace cpu {
namespace aarch64 {

struct acl_eltwise_conf_t {
    arm_compute::ActivationLayerInfo act_info;
    // src and dst have the same info
    arm_compute::TensorInfo data_info;
};

struct acl_eltwise_fwd_t : public primitive_t {
    using Op = arm_compute::experimental::op::CpuActivation;

    struct pd_t : public cpu_eltwise_fwd_pd_t {
        using cpu_eltwise_fwd_pd_t::cpu_eltwise_fwd_pd_t;

        DECLARE_COMMON_PD_T("acl", acl_eltwise_fwd_t);

        status_t init(engine_t *engine);

        acl_eltwise_conf_t aep;

        friend struct acl_post_ops_t;
    };

    acl_eltwise_fwd_t(const pd_t *apd)
        : primitive_t(apd), act_(std::make_unique<Op>()) {}

    status_t execute(const exec_ctx_t &ctx) const override;

    status_t init(engine_t *engine) override;

private:
    status_t execute_forward(const exec_ctx_t &ctx) const;

    // Execute forward with arbitrary src and dst, used by acl_post_ops_t
    status_t execute_forward(
            const exec_ctx_t &ctx, const void *src, void *dst) const;

    const pd_t *pd() const;

    std::unique_ptr<Op> act_;

    friend struct acl_post_ops_t;
}; // acl_eltwise_fwd_t

} // namespace aarch64
} // namespace cpu
} // namespace impl
} // namespace dnnl

#endif // CPU_AARCH64_ACL_ELTWISE_HPP
