--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -152,7 +152,7 @@ # LZ4 {
 option(ENABLE_LZ4_EXT "Enable external LZ4 library support" ON)
 set(WITH_LZ4_EXT OFF)
-if(ENABLE_LZ4_EXT)
-  find_package(LZ4)
-  if(LZ4_FOUND)
+if(1)
+  find_package(lz4 CONFIG REQUIRED)
+  if(1)
     set(WITH_LZ4_EXT ON)
     list(APPEND BUILT_WITH "LZ4_EXT")
@@ -248,4 +248,4 @@ # LZ4 {
 install(
-    FILES "${project_config}" "${project_version}" "packaging/cmake/Modules/FindLZ4.cmake"
+    FILES "${project_config}" "${project_version}"
     DESTINATION "${config_install_dir}"
 )

--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -241,4 +241,3 @@
 if(WITH_LZ4_EXT)
-  target_include_directories(rdkafka PRIVATE ${LZ4_INCLUDE_DIRS})
-  target_link_libraries(rdkafka PUBLIC LZ4::LZ4)
+  target_link_libraries(rdkafka PUBLIC lz4::lz4)
 endif()
