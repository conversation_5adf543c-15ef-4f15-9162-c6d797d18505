{"name": "librdkafka", "version": "2.2.0", "description": "The Apache Kafka C/C++ library", "homepage": "https://github.com/edenhill/librdkafka", "license": null, "supports": "!uwp", "dependencies": ["lz4", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"curl": {"description": "Build with curl", "dependencies": ["curl"]}, "snappy": {"description": "Build with snappy"}, "ssl": {"description": "Build with OpenSSL", "dependencies": ["openssl"]}, "zlib": {"description": "Build with zlib", "dependencies": ["zlib"]}, "zstd": {"description": "Build with zstd", "dependencies": ["zstd"]}}}