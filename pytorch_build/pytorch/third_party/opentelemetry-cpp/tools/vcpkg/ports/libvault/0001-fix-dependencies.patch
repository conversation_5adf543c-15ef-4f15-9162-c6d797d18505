diff --git a/CMakeLists.txt b/CMakeLists.txt
index cd4482b..ac50fb5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -17,9 +17,8 @@ option(LINK_CURL "Link curl library for vault" OFF)
 option(BUILD_SHARED_LIBS "Build vault as a shared library" ON)
 option(INSTALL "Run install targets" ON)
 
-find_package(CURL)
+find_package(CURL CONFIG REQUIRED)
 if(CURL_FOUND)
-  include_directories(${CURL_INCLUDE_DIR})
 else(CURL_FOUND)
   message(FATAL_ERROR "CURL not found")
 endif(CURL_FOUND)
@@ -126,7 +125,7 @@ set_target_properties(
 target_include_directories(vault PRIVATE src)
 
 if(LINK_CURL)
-  target_link_libraries(vault curl)
+  target_link_libraries(vault CURL::libcurl)
 endif(LINK_CURL)
 
 if(ENABLE_COVERAGE)
diff --git a/VaultConfig.cmake.in b/VaultConfig.cmake.in
index 67a40ab..82db52d 100644
--- a/VaultConfig.cmake.in
+++ b/VaultConfig.cmake.in
@@ -1,5 +1,8 @@
 @PACKAGE_INIT@
 
+include(CMakeFindDependencyMacro)
+find_dependency(CURL CONFIG)
+
 include("${CMAKE_CURRENT_LIST_DIR}/libvaultTargets.cmake")
 
 check_required_components(vault)
