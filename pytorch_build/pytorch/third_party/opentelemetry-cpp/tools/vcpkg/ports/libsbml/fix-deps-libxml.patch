diff --git a/CMakeLists.txt b/CMakeLists.txt
index 42b3bd6..34e9a4c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -442,17 +442,15 @@ endif(WITH_EXPAT)
 set(USE_LIBXML OFF)
 if(WITH_LIBXML)
 
-  find_package(LIBXML REQUIRED)
+  find_package(LibXml2 REQUIRED)
   
   add_definitions( -DUSE_LIBXML )
   list(APPEND SWIG_EXTRA_ARGS -DUSE_LIBXML)
   set(USE_LIBXML ON)
 
-  set(LIBSBML_XML_LIBRARY "libxml2")
-  set(LIBSBML_XML_LIBRARY_INCLUDE ${LIBXML_INCLUDE_DIR})
-  set(LIBSBML_XML_LIBRARY_LIBS ${LIBXML_LIBRARY})
+  set(LIBSBML_XML_LIBRARY "LibXml2::LibXml2")
+  set(LIBSBML_XML_LIBRARY_LIBS ${LIBSBML_XML_LIBRARY})
 
-  list(APPEND LIBSBML_FIND_MODULES "${CMAKE_CURRENT_SOURCE_DIR}/CMakeModules/FindLIBXML.cmake")
 
 endif(WITH_LIBXML)
 
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 79301a6..985f6cb 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -423,7 +423,7 @@ if(WITH_LIBXML)
         sbml/xml/LibXMLParser.h
         sbml/xml/LibXMLTranscode.h
     )
-    set(LIBSBML_LIBS ${LIBSBML_LIBS} LIBXML::LIBXML)
+    set(LIBSBML_LIBS ${LIBSBML_LIBS} LibXml2::LibXml2)
 
 endif(WITH_LIBXML)
 
