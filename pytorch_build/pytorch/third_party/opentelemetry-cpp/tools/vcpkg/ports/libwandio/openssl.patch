diff --git a/configure.ac b/configure.ac
index 4579fbb3d..f6be008c8 100644
--- a/configure.ac	
+++ b/configure.ac
@@ -296,12 +304,14 @@ AC_ARG_WITH([http],
 AS_IF([test "x$with_http" != "xno"],
         [
         # we need curl_easy_pause which was added in 7.18.0
-        AC_CHECK_LIB(curl, curl_easy_pause, have_curl=yes, have_curl=no)
+        AC_SEARCH_LIBS(curl_easy_pause, [curl curl-d], have_curl=yes, have_curl=no, [-lssl -lcrypto $ac_cv_search_pthread_create -ldl])
+        # AC_CHECK_LIB(curl, curl_easy_pause, have_curl=yes, have_curl=no)
 	], [have_curl=no])
 
 AS_IF([test "x$have_curl" = "xyes"], [
-        if test "$ac_cv_lib_curl_curl_easy_pause" != "none required"; then
-                LIBWANDIO_LIBS="$LIBWANDIO_LIBS -lcurl"
+        if test "$ac_cv_search_curl_easy_pause" != "none required"; then
+                LIBWANDIO_LIBS="$LIBWANDIO_LIBS $ac_cv_search_curl_easy_pause -lssl -lcrypto $ac_cv_search_pthread_create -ldl"
+                LIBS="$LIBS -lssl -lcrypto $ac_cv_search_pthread_create -ldl"
         fi
         AC_DEFINE(HAVE_HTTP, 1, "Compiled with http support")
         with_http=yes],
