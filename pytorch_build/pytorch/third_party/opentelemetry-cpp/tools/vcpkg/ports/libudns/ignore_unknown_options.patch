diff --git a/configure b/configure
index dda98b3ab..6f5f8a3c8 100644
--- a/configure	
+++ b/configure
@@ -25,10 +25,9 @@ fi
 enable() {
   opt=`echo "$1" | sed 's/^--[^-]*-//'`
   case "$opt" in
-    ipv6) ;;
-    *) echo "configure: unrecognized option \`$1'" >&2; exit 1;;
+    ipv6) eval enable_$opt=$2 ;;
+    *) echo "configure: unrecognized option \`$1'" >&2;;
   esac
-  eval enable_$opt=$2
 }
 
 while [ $# -gt 0 ]; do
@@ -50,7 +49,7 @@ Optional features (all enabled by default if system supports a feature):
 EOF
       exit 0
       ;;
-    *) echo "configure: unknown option \`$1'" >&2; exit 1 ;;
+    *) echo "configure: unknown option \`$1'" >&2;;
   esac
   shift
 done
