{"name": "libwebp", "version": "1.3.2", "description": "WebP codec: library to encode and decode images in WebP format", "homepage": "https://github.com/webmproject/libwebp", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "libwebp", "default-features": false, "features": ["unicode"], "platform": "windows"}, {"name": "libwebp", "default-features": false, "features": ["simd"], "platform": "osx & x64"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["libwebpmux", "nearlossless", "simd"], "features": {"all": {"description": "enable all features except for swap16bitcsp", "dependencies": [{"name": "libwebp", "features": ["anim", "cwebp", "dwebp", "extras", "gif2webp", "img2webp", "info", "libwebpmux", "mux", "nearlossless", "simd"]}, {"name": "libwebp", "features": ["vwebp"], "platform": "!osx & !static"}, {"name": "libwebp", "features": ["vwebp-sdl"], "platform": "!osx & !static & !uwp"}]}, "anim": {"description": "Build animation utilities.", "dependencies": ["gif<PERSON>b", "libjpeg-turbo", "libpng", {"name": "libwebp", "default-features": false, "features": ["img2webp", "mux"]}, "tiff"]}, "cwebp": {"description": "Build the cwebp command line tool.", "dependencies": ["libjpeg-turbo", "libpng", "tiff"]}, "dwebp": {"description": "Build the dwebp command line tool.", "dependencies": ["libjpeg-turbo", "libpng", "tiff"]}, "extras": {"description": "Build extras. (Doesn't include vwebp-sdl.)", "dependencies": ["libjpeg-turbo", "libpng", "tiff"]}, "gif2webp": {"description": "Build the gif2webp conversion tool.", "dependencies": ["gif<PERSON>b", {"name": "libwebp", "default-features": false, "features": ["libwebpmux"]}]}, "img2webp": {"description": "Build the img2webp animation tool.", "dependencies": ["libjpeg-turbo", "libpng", {"name": "libwebp", "default-features": false, "features": ["libwebpmux"]}, "tiff"]}, "info": {"description": "Build the webpinfo command line tool."}, "libbwebpmux": {"description": "Obsolete, use feature libwebpmux instead.", "dependencies": [{"name": "libwebp", "default-features": false, "features": ["libwebpmux"]}]}, "libwebpmux": {"description": "Build the libwebpmux library"}, "mux": {"description": "Build the webpmux command line tool.", "dependencies": [{"name": "libwebp", "default-features": false, "features": ["libwebpmux"]}]}, "nearlossless": {"description": "Enable near-lossless encoding"}, "simd": {"description": "Enable any SIMD optimization."}, "swap16bitcsp": {"description": "Enable byte swap for 16 bit colorspaces."}, "unicode": {"description": "Build Unicode executables. (Adds definition UNICODE and _UNICODE)"}, "vwebp": {"description": "Build the vwebp viewer tool.", "supports": "!osx & !static", "dependencies": ["freeglut", "opengl"]}, "vwebp-sdl": {"description": "Build the vwebp viewer tool for SDL.", "dependencies": [{"name": "libwebp", "default-features": false, "features": ["extras", "vwebp"]}, "sdl1"]}}}