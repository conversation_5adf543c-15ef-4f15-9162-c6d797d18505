diff --git a/cmake/WebPConfig.cmake.in b/cmake/WebPConfig.cmake.in
index 74a5891..4c084d9 100644
--- a/cmake/WebPConfig.cmake.in
+++ b/cmake/WebPConfig.cmake.in
@@ -11,7 +11,7 @@ endif()
 include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
 
 set_and_check(WebP_INCLUDE_DIR "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
-set(WEBP_INCLUDE_DIRS ${WebP_INCLUDE_DIRS})
+set(WEBP_INCLUDE_DIRS ${WebP_INCLUDE_DIR})
 set(WebP_LIBRARIES "")
 include(SelectLibraryConfigurations)
 foreach(_vcpkg_libwebp_lib IN ITEMS @INSTALLED_LIBRARIES@ sharpyuv)
