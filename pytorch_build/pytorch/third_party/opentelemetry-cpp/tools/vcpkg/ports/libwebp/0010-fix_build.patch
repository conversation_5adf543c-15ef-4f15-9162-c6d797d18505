diff --git a/CMakeLists.txt b/CMakeLists.txt
index 636eef7..80361f1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -452,6 +452,7 @@ endif()
 if(WEBP_BUILD_ANIM_UTILS
    OR WEBP_BUILD_CWEBP
    OR WEBP_BUILD_DWEBP
+   OR WEBP_BUILD_EXTRAS
    OR WEBP_BUILD_GIF2WEBP
    OR WEBP_BUILD_IMG2WEBP
    OR WEBP_BUILD_VWEBP
@@ -488,6 +489,8 @@ if(WEBP_BUILD_ANIM_UTILS
     TARGET exampleutil imageioutil imagedec imageenc
     PROPERTY INCLUDE_DIRECTORIES ${CMAKE_CURRENT_SOURCE_DIR}/src
              ${CMAKE_CURRENT_BINARY_DIR}/src)
+  target_include_directories(imagedec PRIVATE ${WEBP_DEP_IMG_INCLUDE_DIRS})
+  target_include_directories(imageenc PRIVATE ${WEBP_DEP_IMG_INCLUDE_DIRS})
 endif()
 
 if(WEBP_BUILD_DWEBP)
