diff --git a/Makefile.am b/Makefile.am
index 8e039a07f..e99d5d6d2 100644
--- a/Makefile.am
+++ b/Makefile.am
@@ -9,8 +9,12 @@ pkgconfig_DATA += xaw6.pc
 if PLATFORM_DARWIN
 DEFAULT_LIB = libXaw.6.@LIBEXT@
 else
+if PLATFORM_WIN32
+DEFAULT_LIB = Xaw6.@LIBEXT@
+else
 DEFAULT_LIB = libXaw6.@LIBEXT@
 endif
+endif
 
 endif
 
@@ -20,8 +24,12 @@ pkgconfig_DATA += xaw7.pc
 if PLATFORM_DARWIN
 DEFAULT_LIB = libXaw.7.@LIBEXT@
 else
+if PLATFORM_WIN32
+DEFAULT_LIB = Xaw7.@LIBEXT@
+else
 DEFAULT_LIB = libXaw7.@LIBEXT@
 endif
+endif
 
 endif
 
@@ -33,7 +41,7 @@ EXTRA_DIST = 		   \
 	old-doc/CHANGES
 
 install-exec-hook:
-	cd $(DESTDIR)$(libdir) && rm -f libXaw.@LIBEXT@ && $(LN_S) $(DEFAULT_LIB) libXaw.@LIBEXT@
+	cd $(DESTDIR)$(libdir) && rm -f libXaw.@LIBEXT@ && $(LN_S) $(DEFAULT_LIB) Xaw.@LIBEXT@
 
 uninstall-local:
 	-rm -f $(DESTDIR)$(libdir)/libXaw.@LIBEXT@
diff --git a/src/AsciiSrc.c b/src/AsciiSrc.c
index 875b97c6c..8223e3bf4 100644
--- a/src/AsciiSrc.c
+++ b/src/AsciiSrc.c
@@ -54,6 +54,10 @@ in this Software without prior written authorization from The Open Group.
 #include <sys/types.h>
 #include <sys/stat.h>
 #include <fcntl.h>
+#ifdef _WIN32
+#include <io.h>
+typedef int mode_t; 
+#endif
 
 #if (defined(ASCII_STRING) || defined(ASCII_DISK))
 #include <X11/Xaw/AsciiText.h>		/* for Widget Classes */
diff --git a/src/MultiSrc.c b/src/MultiSrc.c
index b4a123c14..dc2146913 100644
--- a/src/MultiSrc.c
+++ b/src/MultiSrc.c
@@ -74,6 +74,10 @@ in this Software without prior written authorization from The Open Group.
 #include <sys/types.h>
 #include <sys/stat.h>
 #include <fcntl.h>
+#ifdef _WIN32
+#include <io.h>
+typedef int mode_t; 
+#endif
 
 #define MAGIC_VALUE	((XawTextPosition)-1)
 #define streq(a, b)	(strcmp((a), (b)) == 0)
diff --git a/src/TextAction.c b/src/TextAction.c
index 43d1d3258..e083c908f 100644
--- a/src/TextAction.c
+++ b/src/TextAction.c
@@ -29,7 +29,12 @@ in this Software without prior written authorization from The Open Group.
 #endif
 #include <stdio.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
+#ifdef _WIN32
+#define _WILLWINSOCK_
+#endif
 #include <X11/Xos.h>		/* for select() and struct timeval */
 #include <ctype.h>
 #include <X11/IntrinsicP.h>
@@ -3186,7 +3191,11 @@ InsertChar(Widget w, XEvent *event, String *p _X_UNUSED, Cardinal *n _X_UNUSED)
 	FD_SET(ConnectionNumber(XtDisplay(w)), &fds);
 	(void)select(FD_SETSIZE, &fds, NULL, NULL, &tmval);
 	if (tmval.tv_usec != 500000)
+    #ifndef _WIN32
 	    usleep(40000);
+    #else
+        Sleep(40);
+    #endif
 
 	StartAction(ctx, NULL);
 #ifndef OLDXAW
