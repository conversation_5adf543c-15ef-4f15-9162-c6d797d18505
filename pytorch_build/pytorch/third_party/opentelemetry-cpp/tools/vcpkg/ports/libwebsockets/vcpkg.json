{"name": "libwebsockets", "version-semver": "4.3.2", "description": "Libwebsockets is a lightweight pure C library built to use minimal CPU and memory resources, and provide fast throughput in both directions as client or server.", "homepage": "https://github.com/warmcat/libwebsockets", "supports": "!uwp", "dependencies": [{"name": "libuv", "platform": "!emscripten"}, "openssl", "pthreads", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"]}