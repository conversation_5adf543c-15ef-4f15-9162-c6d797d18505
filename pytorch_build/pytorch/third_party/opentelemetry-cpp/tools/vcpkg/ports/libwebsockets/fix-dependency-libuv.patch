diff --git a/lib/event-libs/libuv/CMakeLists.txt b/lib/event-libs/libuv/CMakeLists.txt
index fb810a8..2258ade 100644
--- a/lib/event-libs/libuv/CMakeLists.txt
+++ b/lib/event-libs/libuv/CMakeLists.txt
@@ -36,7 +36,7 @@ set(LWS_LIBUV_INCLUDE_DIRS CACHE PATH "Path to the libuv include directory")
 if ("${LWS_LIBUV_LIBRARIES}" STREQUAL "" OR "${LWS_LIBUV_INCLUDE_DIRS}" STREQUAL "")
 	if (NOT LIBUV_FOUND)
 		find_path(LIBUV_INCLUDE_DIRS NAMES uv.h)
-		find_library(LIBUV_LIBRARIES NAMES uv)
+		find_library(LIBUV_LIBRARIES NAMES uv libuv)
 	endif()
 else()
 	set(LIBUV_LIBRARIES ${LWS_LIBUV_LIBRARIES})
