diff --git a/CMakeLists.txt b/CMakeLists.txt
index a6a7554..b20fd4b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -111,6 +111,11 @@ if(WIN32)
 	set(PLATFORM_LIBS ${PLATFORM_LIBS} ws2_32 bcrypt)
 endif()
 
+if(MSVC AND MSVC_VERSION GREATER_EQUAL 1912)
+	message(STATUS "Setting /Qspectre switch")
+	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /Qspectre")
+endif()
+
 if(MSVC)
 	add_definitions(-Dinline=__inline)
 	message(STATUS "Using [${CMAKE_C_COMPILER_ID}] compiler")
@@ -123,16 +128,29 @@ if(MSVC)
 			"C4127" # conditional expression is constant
 			"C4146" # unary minus operator applied to unsigned type,
 				# result still unsigned
+			"C4242" # 'identifier' : conversion from 'type1' to 'type2',
+				# possible loss of data
 			"C4244" # 'argument' : conversion from 'type1' to 'type2',
 				# possible loss of data
 			"C4245" # 'conversion' : conversion from 'type1' to 'type2',
 				# signed/unsigned mismatch
+			"C4255" # 'function' : no function prototype given:
+				# converting '()' to '(void)'
 			"C4267" # 'var' : conversion from 'size_t' to 'type',
 				# possible loss of data
+			"C4388" # 'expression': signed/unsigned mismatch
 			"C4389" # 'operator' : signed/unsigned mismatch
+			"C4464" # relative include path contains '..'
+			"C4668" # 'symbol' is not defined as a preprocessor macro,
+				# replacing with '0' for 'directives'
 			"C4706" # assignment within conditional expression
+			"C4710" # 'function' : function not inlined
+			"C4711" # function 'function' selected for inline expansion
+			"C4820" # 'bytes' bytes padding added after construct 'member_name'
 			"C4996" # The POSIX name for this item is deprecated.
 				# Instead, use the ISO C and C++ conformant name
+			"C5045" # Compiler will insert Spectre mitigation for memory load
+				# if /Qspectre switch specified
 		)
 	elseif(CMAKE_C_COMPILER_ID MATCHES "Intel")
 		add_definitions(-D_CRT_SUPPRESS_RESTRICT)

