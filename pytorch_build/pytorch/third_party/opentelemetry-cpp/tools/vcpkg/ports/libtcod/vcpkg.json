{"name": "libtcod", "version": "1.24.0", "maintainers": "<PERSON> <<EMAIL>>", "description": "Common algorithms and tools for roguelikes.", "homepage": "https://github.com/libtcod/libtcod", "documentation": "https://libtcod.readthedocs.io/en/latest/", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["stb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["png", "sdl", "unicode", "zlib"], "features": {"png": {"description": "Support for reading and writing PNG files.  Required to save screenshots and to load tilesets from files.", "dependencies": ["lodepng"]}, "sdl": {"description": "Support for SDL2 windows and events with the libtcod context.", "dependencies": ["sdl2"]}, "threads": {"description": "Support for deprecated threading functions.  If in doubt then leave this disabled."}, "unicode": {"description": "Support for non-ASCII characters.  Required for text printing functions", "dependencies": ["utf8proc"]}, "zlib": {"description": "Support for REXPaint files and TCODZip archives.", "dependencies": ["zlib"]}}}