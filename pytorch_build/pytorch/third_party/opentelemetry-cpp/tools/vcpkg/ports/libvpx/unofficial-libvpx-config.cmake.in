if(NOT TARGET unofficial::libvpx::libvpx)
  # Compute the installation prefix relative to this file.
  get_filename_component(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_FILE}" PATH)
  get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
  get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)

  # Add library target (note: vpx always has a static build in vcpkg).
  add_library(unofficial::libvpx::libvpx STATIC IMPORTED)

  # Add interface include directories and link interface languages (applies to all configurations).
  set_target_properties(unofficial::libvpx::libvpx PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "${_IMPORT_PREFIX}/include"
    IMPORTED_LINK_INTERFACE_LANGUAGES "C"
  )
  list(APPEND _IMPORT_CHECK_FILES "${_IMPORT_PREFIX}/include/vpx/vpx_codec.h")

  # Add release configuration properties.
  find_library(_LIBFILE_RELEASE NAMES vpx PATHS "${_IMPORT_PREFIX}/lib/" NO_DEFAULT_PATH)
  set_property(TARGET unofficial::libvpx::libvpx
    APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
  set_target_properties(unofficial::libvpx::libvpx PROPERTIES
    IMPORTED_LOCATION_RELEASE ${_LIBFILE_RELEASE})
  list(APPEND _IMPORT_CHECK_FILES ${_LIBFILE_RELEASE})
  unset(_LIBFILE_RELEASE CACHE)

  # Add debug configuration properties.
  if(@LIBVPX_CONFIG_DEBUG@)
    find_library(_LIBFILE_DEBUG NAMES vpx PATHS "${_IMPORT_PREFIX}/debug/lib/" NO_DEFAULT_PATH)
    set_property(TARGET unofficial::libvpx::libvpx
      APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
    set_target_properties(unofficial::libvpx::libvpx PROPERTIES
      IMPORTED_LOCATION_DEBUG ${_LIBFILE_DEBUG})
    list(APPEND _IMPORT_CHECK_FILES ${_LIBFILE_DEBUG})
    unset(_LIBFILE_DEBUG CACHE)
  endif()

  # Check header and library files are present.
  foreach(file ${_IMPORT_CHECK_FILES} )
    if(NOT EXISTS "${file}" )
      message(FATAL_ERROR "unofficial::libvpx::libvpx references the file
   \"${file}\"
but this file does not exist.  Possible reasons include:
* The file was deleted, renamed, or moved to another location.
* An install or uninstall procedure did not complete successfully.
")
    endif()
  endforeach()
  unset(_IMPORT_CHECK_FILES)
endif()
