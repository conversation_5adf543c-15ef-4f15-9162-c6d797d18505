diff --git a/build/make/configure.sh b/build/make/configure.sh
index 81d30a1..325017e 100644
--- a/build/make/configure.sh
+++ b/build/make/configure.sh
@@ -1370,12 +1370,14 @@ EOF
       case  ${tgt_os} in
         win32)
           add_asflags -f win32
-          enabled debug && add_asflags -g cv8
+          enabled debug && [ "${AS}" = yasm ] && add_asflags -g cv8
+          enabled debug && [ "${AS}" = nasm ] && add_asflags -gcv8
           EXE_SFX=.exe
           ;;
         win64)
           add_asflags -f win64
-          enabled debug && add_asflags -g cv8
+          enabled debug && [ "${AS}" = yasm ] && add_asflags -g cv8
+          enabled debug && [ "${AS}" = nasm ] && add_asflags -gcv8
           EXE_SFX=.exe
           ;;
         linux*|solaris*|android*)
