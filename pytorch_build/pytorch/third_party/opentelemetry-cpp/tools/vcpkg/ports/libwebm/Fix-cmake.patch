diff --git a/CMakeLists.txt b/CMakeLists.txt
index a83d23b..c5abf83 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -8,6 +8,8 @@
 cmake_minimum_required(VERSION 3.2)
 project(LIBWEBM CXX)
 
+set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
+
 include(GNUInstallDirs)
 include("${CMAKE_CURRENT_SOURCE_DIR}/build/cxx_flags.cmake")
 
@@ -330,6 +332,50 @@ add_library(webm ${libwebm_common_sources}
             $<TARGET_OBJECTS:mkvmuxer>
             $<TARGET_OBJECTS:mkvparser>)
 
+install(
+    TARGETS webm
+    RUNTIME DESTINATION bin
+    ARCHIVE DESTINATION lib
+    LIBRARY DESTINATION lib
+)
+
+install(
+  FILES
+    mkvmuxer.hpp
+    mkvmuxertypes.hpp
+    mkvmuxerutil.hpp
+    mkvparser.hpp
+    mkvreader.hpp
+    mkvwriter.hpp
+    webmids.hpp
+  DESTINATION
+    include/libwebm
+)
+
+install(
+  DIRECTORY mkvmuxer/
+  DESTINATION include/libwebm/mkvmuxer
+  FILES_MATCHING PATTERN "*.h"
+)
+
+install(
+  DIRECTORY mkvparser/
+  DESTINATION include/libwebm/mkvparser
+  FILES_MATCHING PATTERN "*.h"
+)
+
+install(
+  DIRECTORY webvtt/
+  DESTINATION include/libwebm/webvtt
+  FILES_MATCHING PATTERN "*.h"
+)
+
+install(
+  DIRECTORY common/
+  DESTINATION include/libwebm/common
+  FILES_MATCHING PATTERN "*.h"
+)
+
 if (WIN32)
   # Use libwebm and libwebm.lib for project and library name on Windows (instead
   # webm and webm.lib).
@@ -337,18 +383,22 @@ if (WIN32)
   set_target_properties(webm PROPERTIES PREFIX lib)
 endif ()
 
-add_executable(mkvparser_sample ${mkvparser_sample_sources})
-target_link_libraries(mkvparser_sample LINK_PUBLIC webm)
+if (ENABLE_SAMPLES)
+  add_executable(mkvparser_sample ${mkvparser_sample_sources})
+  target_link_libraries(mkvparser_sample LINK_PUBLIC webm)
 
-add_executable(mkvmuxer_sample ${mkvmuxer_sample_sources}
-               $<TARGET_OBJECTS:webvtt_common>)
-target_link_libraries(mkvmuxer_sample LINK_PUBLIC webm)
+  add_executable(mkvmuxer_sample ${mkvmuxer_sample_sources}
+                 $<TARGET_OBJECTS:webvtt_common>)
+  target_link_libraries(mkvmuxer_sample LINK_PUBLIC webm)
+endif()
 
-add_executable(dumpvtt ${dumpvtt_sources} $<TARGET_OBJECTS:webvtt_common>)
-target_link_libraries(dumpvtt LINK_PUBLIC webm)
+if (ENABLE_TOOLS)
+  add_executable(dumpvtt ${dumpvtt_sources} $<TARGET_OBJECTS:webvtt_common>)
+  target_link_libraries(dumpvtt LINK_PUBLIC webm)
 
-add_executable(vttdemux ${vttdemux_sources})
-target_link_libraries(vttdemux LINK_PUBLIC webm)
+  add_executable(vttdemux ${vttdemux_sources})
+  target_link_libraries(vttdemux LINK_PUBLIC webm)
+endif()
 
 if (ENABLE_WEBMINFO)
   add_executable(webm_info ${webm_info_sources})
