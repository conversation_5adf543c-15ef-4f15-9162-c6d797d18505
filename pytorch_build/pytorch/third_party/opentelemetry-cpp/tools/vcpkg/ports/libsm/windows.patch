diff --git a/src/sm_genid.c b/src/sm_genid.c
index 1a8b9a0ed..3726eb1d2 100644
--- a/src/sm_genid.c
+++ b/src/sm_genid.c
@@ -64,7 +64,9 @@ in this Software without prior written authorization from The Open Group.
 # include <X11/Xthreads.h>
 #endif
 #include <stdio.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 
 #include <time.h>
 #define Time_t time_t
@@ -84,6 +86,7 @@ in this Software without prior written authorization from The Open Group.
 
 # include <X11/Xwinsock.h>
 # include <X11/Xw32defs.h>
+# include <ws2ipdef.h>
 # define X_INCLUDE_NETDB_H
 # define XOS_USE_MTSAFE_NETDBAPI
 # include <X11/Xos_r.h>
