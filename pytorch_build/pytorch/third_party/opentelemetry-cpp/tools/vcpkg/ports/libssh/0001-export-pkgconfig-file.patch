diff --git a/CMakeLists.txt b/CMakeLists.txt
index 741dc61..42f0e8f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -124,8 +124,28 @@ add_subdirectory(include)
 add_subdirectory(src)
 
 # pkg-config file
-if (UNIX OR MINGW)
 configure_file(libssh.pc.cmake ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc @ONLY)
+  file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc "Requires:")
+  if (WITH_ZLIB)
+    file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc " zlib")
+  endif ()
+  if (WITH_GCRYPT)
+    file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc "\nLibs.private: -lgcrypt")
+  elseif (WITH_MBEDTLS)
+    file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc "\nLibs.private: -lmbedcrypto -lpthread")
+  else ()
+    if (WIN32)
+      file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc "\nLibs.private: -llibcrypto -luser32 -lcrypt32")
+    else ()
+      file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc " libcrypto\nLibs.private:")
+    endif ()
+  endif ()
+  if (CMAKE_USE_PTHREADS_INIT)
+    file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc " -lpthread")
+  endif ()
+  if (WIN32)
+    file(APPEND ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc " -lws2_32 -lshell32 -ladvapi32")
+  endif ()
 install(
   FILES
     ${CMAKE_CURRENT_BINARY_DIR}/libssh.pc
@@ -134,7 +154,6 @@ install(
   COMPONENT
     pkgconfig
 )
-endif (UNIX OR MINGW)
 
 # CMake config files
 include(CMakePackageConfigHelpers)
