diff --git a/CMakeLists.txt b/CMakeLists.txt
index 53e6bd5..51d76c5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -231,10 +231,11 @@ endif (WITH_SYMBOL_VERSIONING AND AB<PERSON><PERSON>_FOUND)
 add_custom_target(dist COMMAND ${CMAKE_MAKE_PROGRAM} package_source DEPENDS ${_SYMBOL_TARGET} VERBATIM)
 
 # Link compile database for clangd
+if(UNIX)
 execute_process(COMMAND ${CMAKE_COMMAND} -E create_symlink
                 "${CMAKE_BINARY_DIR}/compile_commands.json"
                 "${CMAKE_SOURCE_DIR}/compile_commands.json")
-
+endif()
 message(STATUS "********************************************")
 message(STATUS "********** ${PROJECT_NAME} build options : **********")
 
