diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4bf1e85..48b01ae 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -693,7 +693,7 @@ endif()
 
 set(BUILD_ONEDNN_GRAPH OFF)
 
-include(cmake/Dependencies.cmake)
+include(cmake/vcpkg-dependencies.cmake)
 
 if(USE_CUDA AND (CMAKE_CUDA_COMPILER_VERSION VERSION_LESS 10.2) AND (CMAKE_HOST_SYSTEM_NAME MATCHES "Windows"))
   # CUDA < 10.2 doesn't support compiling and extracting header dependencies in
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 48b01ae..8e46275 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -660,7 +660,7 @@ if(NOT CMAKE_BUILD_TYPE)
 endif()
 
 # The below means we are cross compiling for arm64 or x86_64 on MacOSX
-if(NOT IOS AND CMAKE_SYSTEM_NAME STREQUAL "Darwin" AND CMAKE_OSX_ARCHITECTURES MATCHES "^(x86_64|arm64)$")
+if(false)
   set(CROSS_COMPILING_MACOSX TRUE)
   # We need to compile a universal protoc to not fail protobuf build
   # We set CMAKE_TRY_COMPILE_TARGET_TYPE to STATIC_LIBRARY (vs executable) to succeed the cmake compiler check for cross-compiling
@@ -678,6 +678,7 @@ if(NOT IOS AND CMAKE_SYSTEM_NAME STREQUAL "Darwin" AND CMAKE_OSX_ARCHITECTURES M
   set(PROTOBUF_PROTOC_EXECUTABLE "${PROJECT_SOURCE_DIR}/build_host_protoc/bin/protoc")
   set(CAFFE2_CUSTOM_PROTOC_EXECUTABLE "${PROJECT_SOURCE_DIR}/build_host_protoc/bin/protoc")
 endif()
+include(cmake/ProtoBuf.cmake)
 
 # ---[ Misc checks to cope with various compiler modes
 include(cmake/MiscCheck.cmake)
diff --git a/caffe2/CMakeLists.txt b/caffe2/CMakeLists.txt
index 328210c..c407442 100644
--- a/caffe2/CMakeLists.txt
+++ b/caffe2/CMakeLists.txt
@@ -112,13 +112,8 @@ endif()
 # addressed yet.
 
 if(NOT MSVC AND USE_XNNPACK)
-  if(NOT TARGET fxdiv)
-    set(FXDIV_BUILD_TESTS OFF CACHE BOOL "")
-    set(FXDIV_BUILD_BENCHMARKS OFF CACHE BOOL "")
-    add_subdirectory(
-      "${FXDIV_SOURCE_DIR}"
-      "${CMAKE_BINARY_DIR}/FXdiv")
-  endif()
+  find_path(FXDIV_INCLUDE_DIRS "fxdiv.h" REQUIRED)
+  include_directories(${FXDIV_INCLUDE_DIRS})
 endif()
 
 add_subdirectory(core)
diff --git a/caffe2/CMakeLists.txt b/caffe2/CMakeLists.txt
index c407442..16fa898 100644
--- a/caffe2/CMakeLists.txt
+++ b/caffe2/CMakeLists.txt
@@ -1011,10 +1011,6 @@ if(USE_CUDA OR USE_ROCM)
   include(${TORCH_SRC_DIR}/csrc/jit/codegen/cuda/nvfuser.cmake)
 endif()
 
-if(NOT MSVC AND USE_XNNPACK)
-  TARGET_LINK_LIBRARIES(torch_cpu PRIVATE fxdiv)
-endif()
-
 # ==========================================================
 # formerly-libtorch flags
 # ==========================================================
diff --git a/c10/CMakeLists.txt b/c10/CMakeLists.txt
index 41b1a1a..3b390ec 100644
--- a/c10/CMakeLists.txt
+++ b/c10/CMakeLists.txt
@@ -110,7 +110,11 @@ endif()
 # Note: for now, we will put all export path into one single Caffe2Targets group
 # to deal with the cmake deployment need. Inside the Caffe2Targets set, the
 # individual libraries like libc10.so and libcaffe2.so are still self-contained.
-install(TARGETS c10 EXPORT Caffe2Targets DESTINATION lib)
+install(TARGETS c10 EXPORT Caffe2Targets
+  RUNTIME DESTINATION bin
+  LIBRARY DESTINATION lib
+  ARCHIVE DESTINATION lib
+)
 install(DIRECTORY ${CMAKE_CURRENT_LIST_DIR}
         DESTINATION include
         FILES_MATCHING PATTERN "*.h")
diff --git a/caffe2/CMakeLists.txt b/caffe2/CMakeLists.txt
index 16fa898..7a343f7 100644
--- a/caffe2/CMakeLists.txt
+++ b/caffe2/CMakeLists.txt
@@ -1547,7 +1547,11 @@ endif()
 
 caffe2_interface_library(torch torch_library)
 
-install(TARGETS torch_cpu torch_cpu_library EXPORT Caffe2Targets DESTINATION "${TORCH_INSTALL_LIB_DIR}")
+install(TARGETS torch_cpu torch_cpu_library EXPORT Caffe2Targets
+  RUNTIME DESTINATION "${TORCH_INSTALL_BIN_DIR}"
+  LIBRARY DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+  ARCHIVE DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+)
 
 if(USE_CUDA)
   install(TARGETS torch_cuda torch_cuda_library EXPORT Caffe2Targets DESTINATION "${TORCH_INSTALL_LIB_DIR}")
@@ -1558,7 +1562,11 @@ if(USE_CUDA)
 elseif(USE_ROCM)
   install(TARGETS torch_hip torch_hip_library EXPORT Caffe2Targets DESTINATION "${TORCH_INSTALL_LIB_DIR}")
 endif()
-install(TARGETS torch torch_library EXPORT Caffe2Targets DESTINATION "${TORCH_INSTALL_LIB_DIR}")
+install(TARGETS torch torch_library EXPORT Caffe2Targets
+  RUNTIME DESTINATION "${TORCH_INSTALL_BIN_DIR}"
+  LIBRARY DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+  ARCHIVE DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+)
 
 target_link_libraries(torch PUBLIC torch_cpu_library)
 
@@ -1688,7 +1696,11 @@ if(BUILD_SHARED_LIBS)
     target_link_libraries(torch_global_deps TBB::tbb)
   endif()
 
-  install(TARGETS torch_global_deps DESTINATION "${TORCH_INSTALL_LIB_DIR}")
+  install(TARGETS torch_global_deps
+    RUNTIME DESTINATION "${TORCH_INSTALL_BIN_DIR}"
+    LIBRARY DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+    ARCHIVE DESTINATION "${TORCH_INSTALL_LIB_DIR}"
+  )
 endif()
 
 # ---[ Caffe2 HIP sources.
diff --git a/caffe2/CMakeLists.txt b/caffe2/CMakeLists.txt
index 7a343f7..3390f9b 100644
--- a/caffe2/CMakeLists.txt
+++ b/caffe2/CMakeLists.txt
@@ -1673,7 +1673,7 @@ target_link_libraries(torch_cpu PRIVATE flatbuffers)
 # namespaces, so libtorch is loaded with all its dependencies in a local scope.
 # That usually leads to missing symbol errors at run-time, so to avoid a situation like
 # this we have to preload those libs in a global namespace.
-if(BUILD_SHARED_LIBS)
+if(BUILD_SHARED_LIBS AND (NOT WIN32))
   add_library(torch_global_deps SHARED ${TORCH_SRC_DIR}/csrc/empty.c)
   if(HAVE_SOVERSION)
     set_target_properties(torch_global_deps PROPERTIES
