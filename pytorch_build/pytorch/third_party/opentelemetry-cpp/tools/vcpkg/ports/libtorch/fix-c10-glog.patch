diff --git a/c10/util/Logging.cpp b/c10/util/Logging.cpp
index fe74e49..06e09d6 100644
--- a/c10/util/Logging.cpp
+++ b/c10/util/Logging.cpp
@@ -4,7 +4,7 @@
 #ifdef FBCODE_CAFFE2
 #include <folly/synchronization/SanitizeThread.h>
 #endif
-
+#include <glog/logging.h>
 #include <algorithm>
 #include <cstdlib>
 #include <cstring>
@@ -192,23 +194,13 @@ C10_DEFINE_int(
     google::GLOG_WARNING,
     "The minimum log level that caffe2 will output.");
 
-// Google glog's api does not have an external function that allows one to check
-// if glog is initialized or not. It does have an internal function - so we are
-// declaring it here. This is a hack but has been used by a bunch of others too
-// (e.g. Torch).
-namespace google {
-namespace glog_internal_namespace_ {
-bool IsGoogleLoggingInitialized();
-} // namespace glog_internal_namespace_
-} // namespace google
-
 namespace c10 {
 namespace {
 
 void initGoogleLogging(char const* name) {
 #if !defined(_MSC_VER)
   // This trick can only be used on UNIX platforms
-  if (!::google::glog_internal_namespace_::IsGoogleLoggingInitialized())
+  if (!::google::IsGoogleLoggingInitialized())
 #endif
   {
     ::google::InitGoogleLogging(name);
diff --git a/c10/util/Logging.cpp b/c10/util/Logging.cpp
index 1864f17..b958fa0 100644
--- a/c10/util/Logging.cpp
+++ b/c10/util/Logging.cpp
@@ -154,18 +154,6 @@ void setLogLevelFlagFromEnv();
 } // namespace detail
 } // namespace c10
 
-#if defined(C10_USE_GFLAGS) && defined(C10_USE_GLOG)
-// When GLOG depends on GFLAGS, these variables are being defined in GLOG
-// directly via the GFLAGS definition, so we will use DECLARE_* to declare
-// them, and use them in Caffe2.
-// GLOG's minloglevel
-DECLARE_int32(minloglevel);
-// GLOG's verbose log value.
-DECLARE_int32(v);
-// GLOG's logtostderr value
-DECLARE_bool(logtostderr);
-#endif // defined(C10_USE_GFLAGS) && defined(C10_USE_GLOG)
-
 #if !defined(C10_USE_GLOG)
 // This backward compatibility flags are in order to deal with cases where
 // Caffe2 are not built with glog, but some init flags still pass in these
