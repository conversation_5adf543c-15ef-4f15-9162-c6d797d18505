diff --git a/CMakeLists.txt b/CMakeLists.txt
index a36e574..a3ecdde 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -75,6 +75,13 @@ INCLUDE_DIRECTORIES(${CMAKE_CURRENT_SOURCE_DIR})
 
 ADD_LIBRARY(squish ${SQUISH_SRCS} ${SQUISH_HDRS})
 
+INCLUDE(GenerateExportHeader)
+GENERATE_EXPORT_HEADER(squish
+    EXPORT_FILE_NAME ${CMAKE_CURRENT_SOURCE_DIR}/squish_export.h
+    )
+
+list(APPEND SQUISH_HDRS "squish_export.h")
+
 SET_TARGET_PROPERTIES(
     squish PROPERTIES
     PUBLIC_HEADER "${SQUISH_HDRS}"
@@ -109,9 +116,12 @@ IF (BUILD_SQUISH_EXTRA)
     ENDIF (PNG_FOUND)
 ENDIF (BUILD_SQUISH_EXTRA)
 
+INCLUDE(GNUInstallDirs)
+
 INSTALL(
     TARGETS squish
-    LIBRARY DESTINATION lib
-    ARCHIVE DESTINATION lib
-    PUBLIC_HEADER DESTINATION include
+    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+    PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
     )
diff --git a/squish.h b/squish.h
index 14c9bb5..aaffbb2 100644
--- a/squish.h
+++ b/squish.h
@@ -26,6 +26,8 @@
 #ifndef SQUISH_H
 #define SQUISH_H
 
+#include "squish_export.h"
+
 //! All squish API functions live in this namespace.
 namespace squish {

@@ -115,5 +117,5 @@
 */
-void CompressMasked( u8 const* rgba, int mask, void* block, int flags, float* metric = 0 );
+SQUISH_EXPORT void CompressMasked( u8 const* rgba, int mask, void* block, int flags, float* metric = 0 );
 
 // -----------------------------------------------------------------------------
 
@@ -176,7 +178,7 @@ inline void Compress( u8 const* rgba, void* block, int flags, float* metric = 0
     however, DXT1 will be used by default if none is specified. All other flags
     are ignored.
 */
-void Decompress( u8* rgba, void const* block, int flags );
+SQUISH_EXPORT void Decompress( u8* rgba, void const* block, int flags );
 
 // -----------------------------------------------------------------------------
 
@@ -194,7 +196,7 @@ void Decompress( u8* rgba, void const* block, int flags );
     function supports arbitrary size images by allowing the outer blocks to
     be only partially used.
 */
-int GetStorageRequirements( int width, int height, int flags );
+SQUISH_EXPORT int GetStorageRequirements( int width, int height, int flags );
 
 // -----------------------------------------------------------------------------
 
@@ -249,8 +251,8 @@ int GetStorageRequirements( int width, int height, int flags );
     Windows platform but for other platforms like MacOS X a different
     gamma value may be more suitable.
 */
-void CompressImage( u8 const* rgba, int width, int height, int pitch, void* blocks, int flags, float* metric = 0 );
-void CompressImage( u8 const* rgba, int width, int height, void* blocks, int flags, float* metric = 0 );
+SQUISH_EXPORT void CompressImage( u8 const* rgba, int width, int height, int pitch, void* blocks, int flags, float* metric = 0 );
+SQUISH_EXPORT void CompressImage( u8 const* rgba, int width, int height, void* blocks, int flags, float* metric = 0 );
 
 // -----------------------------------------------------------------------------
 
@@ -274,8 +276,8 @@ void CompressImage( u8 const* rgba, int width, int height, void* blocks, int fla
 
     Internally this function calls squish::Decompress for each block.
 */
-void DecompressImage( u8* rgba, int width, int height, int pitch, void const* blocks, int flags );
-void DecompressImage( u8* rgba, int width, int height, void const* blocks, int flags );
+SQUISH_EXPORT void DecompressImage( u8* rgba, int width, int height, int pitch, void const* blocks, int flags );
+SQUISH_EXPORT void DecompressImage( u8* rgba, int width, int height, void const* blocks, int flags );
 
 // -----------------------------------------------------------------------------
 
@@ -299,8 +301,8 @@ void DecompressImage( u8* rgba, int width, int height, void const* blocks, int f
 
     Internally this function calls squish::Decompress for each block.
 */
-void ComputeMSE(u8 const *rgba, int width, int height, int pitch, u8 const *dxt, int flags, double &colourMSE, double &alphaMSE);
-void ComputeMSE(u8 const *rgba, int width, int height, u8 const *dxt, int flags, double &colourMSE, double &alphaMSE);
+SQUISH_EXPORT void ComputeMSE(u8 const *rgba, int width, int height, int pitch, u8 const *dxt, int flags, double &colourMSE, double &alphaMSE);
+SQUISH_EXPORT void ComputeMSE(u8 const *rgba, int width, int height, u8 const *dxt, int flags, double &colourMSE, double &alphaMSE);
 
 // -----------------------------------------------------------------------------
 
