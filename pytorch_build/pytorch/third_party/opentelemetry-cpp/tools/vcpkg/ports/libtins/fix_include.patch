diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 94bc8bf..14a15e9 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -211,6 +211,7 @@ ADD_LIBRARY(
 )
 
 TARGET_LINK_LIBRARIES(tins ${PCAP_LIBRARY} ${OPENSSL_LIBRARIES} ${LIBTINS_OS_LIBS})
+target_include_directories(tins PUBLIC $<INSTALL_INTERFACE:include>)
 
 SET_TARGET_PROPERTIES(tins PROPERTIES OUTPUT_NAME tins)
 SET_TARGET_PROPERTIES(tins PROPERTIES VERSION ${LIBTINS_VERSION} SOVERSION ${LIBTINS_VERSION} )
diff --git a/libtins.pc.in b/libtins.pc.in
index e9a5c29..c7e3f6c 100644
--- a/libtins.pc.in
+++ b/libtins.pc.in
@@ -7,4 +7,4 @@ Name: libtins
 Description: C++ packet crafting, sniffing and interpretation library.
 Version: @pkgconfig_version@
 Libs: -L${libdir} -ltins
-Cflags: -I${includedir}/tins
+Cflags: -I${includedir}
