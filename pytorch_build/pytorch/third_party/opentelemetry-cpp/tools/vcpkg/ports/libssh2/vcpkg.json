{"name": "libssh2", "version": "1.11.0", "port-version": 1, "description": "libssh2 is a client-side C library implementing the SSH2 protocol.", "homepage": "https://www.libssh2.org", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "libssh2", "default-features": false, "features": ["openssl"], "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["openssl", "zlib"], "features": {"openssl": {"description": "Use the openssl crypto backend", "dependencies": ["openssl"]}, "zlib": {"description": "Use compression via zlib", "dependencies": ["zlib"]}}}