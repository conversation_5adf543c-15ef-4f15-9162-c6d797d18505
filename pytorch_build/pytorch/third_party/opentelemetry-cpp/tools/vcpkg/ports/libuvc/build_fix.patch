diff --git a/CMakeLists.txt b/CMakeLists.txt
index f3c8116..b9a6d38 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -39,10 +39,10 @@ set(SOURCES
   src/misc.c
 )
 
-find_package(LibUSB)
+find_package(libusb CONFIG REQUIRED)
 
 # JpegPkg name to differ from shipped with CMake
-find_package(JpegPkg QUIET)
+find_package(JPEG REQUIRED)
 if(JPEG_FOUND)
   message(STATUS "Building libuvc with JPEG support.")
   set(LIBUVC_HAS_JPEG TRUE)
@@ -107,9 +107,10 @@ foreach(target_name IN LISTS UVC_TARGETS)
       $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/include>
       $<INSTALL_INTERFACE:include>
   )
+  target_include_directories(${target_name} PRIVATE ${LIBUSB_INCLUDE_DIRS})
   target_link_libraries(${target_name}
     # libusb-1.0 used internally so we link to it privately.
-    PRIVATE LibUSB::LibUSB
+    PRIVATE ${LIBUSB_LIBRARIES}
   )
   if(JPEG_FOUND)
     target_link_libraries(${target_name}
diff --git a/libuvcConfig.cmake b/libuvcConfig.cmake
index b9887ea..e8f09dc 100644
--- a/libuvcConfig.cmake
+++ b/libuvcConfig.cmake
@@ -10,8 +10,8 @@ if(${CMAKE_FIND_PACKAGE_NAME}_FIND_REQUIRED)
   list(APPEND extraArgs REQUIRED)
 endif()
 
-find_package(JpegPkg ${extraArgs})
-find_package(LibUSB ${extraArgs})
+include(CMakeFindDependencyMacro)
+find_dependency(JPEG ${extraArgs})
 include("${CMAKE_CURRENT_LIST_DIR}/libuvcTargets.cmake")
 
 set(libuvc_FOUND TRUE)
