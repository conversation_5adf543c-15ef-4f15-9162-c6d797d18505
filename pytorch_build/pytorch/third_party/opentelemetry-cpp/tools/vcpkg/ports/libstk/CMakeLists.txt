cmake_minimum_required(VERSION 3.0)
project(libstk)

set(LIBSTK_SOURCES
	src/ADSR.cpp
	src/Asymp.cpp
	src/BandedWG.cpp
	src/BeeThree.cpp
	src/BiQuad.cpp
	src/Blit.cpp
	src/BlitSaw.cpp
	src/BlitSquare.cpp
	src/BlowBotl.cpp
	src/BlowHole.cpp
	src/Bowed.cpp
	src/Brass.cpp
	src/Chorus.cpp
	src/Clarinet.cpp
	src/Delay.cpp
	src/DelayA.cpp
	src/DelayL.cpp
	src/Drummer.cpp
	src/Echo.cpp
	src/Envelope.cpp
	src/FileLoop.cpp
	src/FileRead.cpp
	src/FileWrite.cpp
	src/FileWvIn.cpp
	src/FileWvOut.cpp
	src/Fir.cpp
	src/Flute.cpp
	src/FM.cpp
	src/FMVoices.cpp
	src/FormSwep.cpp
	src/FreeVerb.cpp
	src/Granulate.cpp
	src/Guitar.cpp
	src/HevyMetl.cpp
	src/Iir.cpp
	src/JCRev.cpp
	src/LentPitShift.cpp
	src/Makefile.in
	src/Mandolin.cpp
	src/Mesh2D.cpp
	src/Messager.cpp
	src/MidiFileIn.cpp
	src/Modal.cpp
	src/ModalBar.cpp
	src/Modulate.cpp
	src/Moog.cpp
	src/Noise.cpp
	src/NRev.cpp
	src/OnePole.cpp
	src/OneZero.cpp
	src/PercFlut.cpp
	src/Phonemes.cpp
	src/PitShift.cpp
	src/Plucked.cpp
	src/PoleZero.cpp
	src/PRCRev.cpp
	src/Resonate.cpp
	src/Rhodey.cpp
	src/Sampler.cpp
	src/Saxofony.cpp
	src/Shakers.cpp
	src/Simple.cpp
	src/SineWave.cpp
	src/SingWave.cpp
	src/Sitar.cpp
	src/Skini.cpp
	src/Sphere.cpp
	src/StifKarp.cpp
	src/Stk.cpp
	src/TapDelay.cpp
	src/TubeBell.cpp
	src/Twang.cpp
	src/TwoPole.cpp
	src/TwoZero.cpp
	src/Voicer.cpp
	src/VoicForm.cpp
	src/Whistle.cpp
	src/Wurley.cpp)

set(LIBSTK_REALTIME_SOURCES
	src/InetWvIn.cpp
	src/InetWvOut.cpp
	src/Mutex.cpp
	src/RtAudio.cpp
	src/RtMidi.cpp
	src/RtWvIn.cpp
	src/RtWvOut.cpp
	src/Socket.cpp
	src/TcpClient.cpp
	src/TcpServer.cpp
	src/Thread.cpp
	src/UdpSocket.cpp
)

set(LIBSTK_HEADERS
	include/ADSR.h
	include/Asymp.h
	include/BandedWG.h
	include/BeeThree.h
	include/BiQuad.h
	include/Blit.h
	include/BlitSaw.h
	include/BlitSquare.h
	include/BlowBotl.h
	include/BlowHole.h
	include/Bowed.h
	include/BowTable.h
	include/Brass.h
	include/Chorus.h
	include/Clarinet.h
	include/Cubic.h
	include/Delay.h
	include/DelayA.h
	include/DelayL.h
	include/Drummer.h
	include/Echo.h
	include/Effect.h
	include/Envelope.h
	include/FileLoop.h
	include/FileRead.h
	include/FileWrite.h
	include/FileWvIn.h
	include/FileWvOut.h
	include/Filter.h
	include/Fir.h
	include/Flute.h
	include/FM.h
	include/FMVoices.h
	include/FormSwep.h
	include/FreeVerb.h
	include/Function.h
	include/Generator.h
	include/Granulate.h
	include/Guitar.h
	include/HevyMetl.h
	include/Iir.h
	include/InetWvIn.h
	include/InetWvOut.h
	include/Instrmnt.h
	include/JCRev.h
	include/JetTable.h
	include/LentPitShift.h
	include/Mandolin.h
	include/Mesh2D.h
	include/Messager.h
	include/MidiFileIn.h
	include/Modal.h
	include/ModalBar.h
	include/Modulate.h
	include/Moog.h
	include/Mutex.h
	include/Noise.h
	include/NRev.h
	include/OnePole.h
	include/OneZero.h
	include/PercFlut.h
	include/Phonemes.h
	include/PitShift.h
	include/Plucked.h
	include/PoleZero.h
	include/PRCRev.h
	include/ReedTable.h
	include/Resonate.h
	include/Rhodey.h
	include/RtAudio.h
	include/RtMidi.h
	include/RtWvIn.h
	include/RtWvOut.h
	include/Sampler.h
	include/Saxofony.h
	include/Shakers.h
	include/Simple.h
	include/SineWave.h
	include/SingWave.h
	include/Sitar.h
	include/Skini.h
	include/SKINImsg.h
	include/SKINItbl.h
	include/Socket.h
	include/Sphere.h
	include/StifKarp.h
	include/Stk.h
	include/TapDelay.h
	include/TcpClient.h
	include/TcpServer.h
	include/Thread.h
	include/TubeBell.h
	include/Twang.h
	include/TwoPole.h
	include/TwoZero.h
	include/UdpSocket.h
	include/Vector3D.h
	include/Voicer.h
	include/VoicForm.h
	include/Whistle.h
	include/Wurley.h
	include/WvIn.h
	include/WvOut.h
)

add_library(libstk ${LIBSTK_SOURCES})
target_include_directories(libstk PRIVATE include PUBLIC $<INSTALL_INTERFACE:include>)
set_target_properties(libstk PROPERTIES PUBLIC_HEADER "${LIBSTK_HEADERS}")
target_compile_definitions(libstk PRIVATE __LITTLE_ENDIAN__)

install(TARGETS libstk EXPORT unofficial-libstk-config
	LIBRARY DESTINATION lib
	ARCHIVE DESTINATION lib
	RUNTIME DESTINATION bin
	PUBLIC_HEADER DESTINATION include/stk)
install(EXPORT unofficial-libstk-config NAMESPACE unofficial::libstk:: DESTINATION share/unofficial-libstk)
