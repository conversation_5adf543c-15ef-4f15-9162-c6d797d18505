{"name": "libusbmuxd", "version-date": "2023-06-21", "port-version": 1, "description": "A client library to multiplex connections from and to iOS devices", "homepage": "https://libimobiledevice.org/", "license": "LGPL-2.1-or-later", "supports": "!uwp", "dependencies": ["libimobiledevice-glue", "libplist", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "build command line tool", "supports": "!android & !ios & !xbox", "dependencies": ["getopt"]}}}