diff --git a/CMakeLists.txt b/CMakeLists.txt
index e1b28fe..73990c4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -53,6 +53,13 @@ set (PROJECT_SOURCE_DIR "${sigc++_SOURCE_DIR}/sigc++")
 include_directories (${sigc++_SOURCE_DIR})
 include_directories (${sigc++_BINARY_DIR})
 
+if (BUILD_SHARED_LIBS)
+    add_compile_options (-DBUILD_SHARED)
+    if (MSVC)
+        add_compile_options (-D_WINDLL)
+    endif()
+endif()
+
 configure_file (sigc++config.h.cmake sigc++config.h)
 
 set (prefix ${CMAKE_INSTALL_PREFIX})
diff --git a/sigc++config.h.cmake b/sigc++config.h.cmake
index 74d348a..43a99c5 100644
--- a/sigc++config.h.cmake
+++ b/sigc++config.h.cmake
@@ -16,7 +16,9 @@
 # if defined(_MSC_VER)
 #  define SIGC_MSC 1
 #  define SIGC_WIN32 1
+# ifdef BUILD_SHARED
 #  define SIGC_DLL 1
+# endif
 # elif defined(__CYGWIN__)
 #  define SIGC_CONFIGURE 1
 # elif defined(__MINGW32__)
