# For old projects where the minimum CMake version is lower than 3.3.
cmake_policy(SET CMP0057 NEW)
include(CMakeFindDependencyMacro)

if(TARGET libunibreak)
  return()
endif()

# Compute the installation prefix relative to this file.
get_filename_component(_INSTALL_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component(_INSTALL_DIR "${_INSTALL_DIR}" PATH)
get_filename_component(_INSTALL_DIR "${_INSTALL_DIR}" PATH)
if(_INSTALL_DIR STREQUAL "/")
  set(_INSTALL_DIR "")
endif()

add_library(unibreak INTERFACE IMPORTED GLOBAL)
add_library(libunibreak::libunibreak ALIAS unibreak)

find_library(LIBUNIBREAK_LIBRARY_DEBUG NAMES unibreak PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug" PATH_SUFFIXES lib NO_DEFAULT_PATH)
if(LIBUNIBREAK_LIBRARY_DEBUG)
    target_link_libraries(unibreak INTERFACE $<$<CONFIG:DEBUG>:${LIBUNIBREAK_LIBRARY_DEBUG}>)
endif()

find_library(LIBUNIBREAK_LIBRARY_RELEASE NAMES unibreak PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}" PATH_SUFFIXES lib NO_DEFAULT_PATH)
target_link_libraries(unibreak INTERFACE $<$<NOT:$<CONFIG:DEBUG>>:${LIBUNIBREAK_LIBRARY_RELEASE}>)

set(LIBUNIBREAK_LIBRARY libunibreak::libunibreak PARENT_SCOPE)
set(LIBUNIBREAK_LIBRARIES libunibreak::libunibreak PARENT_SCOPE)
set(LIBUNIBREAK_INCLUDE_DIR "${_INSTALL_DIR}/include" PARENT_SCOPE)

target_include_directories(unibreak INTERFACE ${_INSTALL_DIR}/include)