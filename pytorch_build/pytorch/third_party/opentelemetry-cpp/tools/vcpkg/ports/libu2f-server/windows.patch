diff --git a/u2f-server/cdecode.c b/u2f-server/cdecode.c
index 6fff114..dc05ca5 100644
--- a/u2f-server/cdecode.c
+++ b/u2f-server/cdecode.c
@@ -5,7 +5,7 @@ This is part of the libb64 project, and has been placed in the public domain.
 For details, see http://sourceforge.net/projects/libb64
 */
 
-#include <b64/cdecode.h>
+#include "b64/cdecode.h"
 
 int base64_decode_value(char value_in)
 {
diff --git a/u2f-server/cencode.c b/u2f-server/cencode.c
index eebb24a..f3b7fa4 100644
--- a/u2f-server/cencode.c
+++ b/u2f-server/cencode.c
@@ -5,7 +5,7 @@ This is part of the libb64 project, and has been placed in the public domain.
 For details, see http://sourceforge.net/projects/libb64
 */
 
-#include <b64/cencode.h>
+#include "b64/cencode.h"
 
 const int CHARS_PER_LINE = 72;
 
diff --git a/u2f-server/core.c b/u2f-server/core.c
index da440ec..294c94b 100644
--- a/u2f-server/core.c
+++ b/u2f-server/core.c
@@ -30,8 +30,7 @@
 #include "internal.h"
 
 #include <string.h>
-#include <unistd.h>
-#include <json.h>
+#include <json-c/json.h>
 #include "crypto.h"
 #include "b64/cencode.h"
 #include "b64/cdecode.h"
 