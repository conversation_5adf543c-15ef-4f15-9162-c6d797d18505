diff --git a/lib/gl/cdefs.h b/lib/gl/cdefs.h
index 7b8ed5b..63574f9 100644
--- a/lib/gl/cdefs.h
+++ b/lib/gl/cdefs.h
@@ -140,6 +140,7 @@
 #endif
 
 
+#ifndef __GNULIB_CDEFS
 /* Fortify support.  */
 #define __bos(ptr) __builtin_object_size (ptr, __USE_FORTIFY_LEVEL > 1)
 #define __bos0(ptr) __builtin_object_size (ptr, 0)
@@ -201,6 +202,8 @@
       ? __ ## f ## _chk_warn (__VA_ARGS__, (__osz) / (__s))		      \
       : __ ## f ## _chk (__VA_ARGS__, (__osz) / (__s))))		      \
 
+#endif
+
 #if __GNUC_PREREQ (4,3)
 # define __warnattr(msg) __attribute__((__warning__ (msg)))
 # define __errordecl(name, msg) \
diff --git a/lib/gl/libc-config.h b/lib/gl/libc-config.h
index a56665b..876e1a1 100644
--- a/lib/gl/libc-config.h
+++ b/lib/gl/libc-config.h
@@ -137,8 +137,10 @@
 # undef __attribute_returns_twice__
 # undef __attribute_used__
 # undef __attribute_warn_unused_result__
+# ifndef __GNULIB_CDEFS
 # undef __bos
 # undef __bos0
+# endif
 # undef __errordecl
 # undef __extension__
 # undef __extern_always_inline
@@ -147,21 +149,27 @@
 # undef __fortified_attr_access
 # undef __fortify_function
 # undef __glibc_c99_flexarr_available
+# ifndef __GNULIB_CDEFS
 # undef __glibc_fortify
 # undef __glibc_fortify_n
+# endif
 # undef __glibc_has_attribute
 # undef __glibc_has_builtin
 # undef __glibc_has_extension
 # undef __glibc_likely
 # undef __glibc_macro_warning
 # undef __glibc_macro_warning1
+# ifndef __GNULIB_CDEFS
 # undef __glibc_objsize
 # undef __glibc_objsize0
 # undef __glibc_safe_len_cond
 # undef __glibc_safe_or_unknown_len
+# endif
 # undef __glibc_unlikely
+# ifndef __GNULIB_CDEFS
 # undef __glibc_unsafe_len
 # undef __glibc_unsigned_or_positive
+# endif
 # undef __inline
 # undef __ptr_t
 # undef __restrict
