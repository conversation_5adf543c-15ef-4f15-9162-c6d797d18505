diff --git a/src/FreeType/ftenc.c b/src/FreeType/ftenc.c
index 5b0299345..5abffb6e0 100644
--- a/src/FreeType/ftenc.c
+++ b/src/FreeType/ftenc.c
@@ -41,6 +41,10 @@ THE SOFTWARE.
 #include FT_XFREE86_H
 #include "ft.h"
 
+#if (defined(WIN32) || defined(WIN64) || defined(_WIN32) || defined(_WIN64)) && !defined(HAVE_STRCASECMP)
+#define strcasecmp _stricmp
+#endif
+
 static int find_cmap(int, int, int, FT_Face, FT_CharMap *);
 
 static int
diff --git a/src/FreeType/xttcap.c b/src/FreeType/xttcap.c
index f54f76c85..a5a047ee6 100644
--- a/src/FreeType/xttcap.c
+++ b/src/FreeType/xttcap.c
@@ -56,6 +56,10 @@ static char const * const releaseID =
 
 #include "xttcap.h"
 
+#if (defined(WIN32) || defined(WIN64) || defined(_WIN32) || defined(_WIN64)) && !defined(HAVE_STRCASECMP)
+#define strcasecmp _stricmp
+#endif
+
 
 /**************************************************************************
   Private Data Types
diff --git a/src/fontfile/renderers.c b/src/fontfile/renderers.c
index a457fb7bb..c44591f13 100644
--- a/src/fontfile/renderers.c
+++ b/src/fontfile/renderers.c
@@ -31,10 +31,13 @@ in this Software without prior written authorization from The Open Group.
 #ifdef HAVE_CONFIG_H
 #include <config.h>
 #endif
+#include <string.h>
 #include "libxfontint.h"
 #include "src/util/replace.h"
 #include <X11/fonts/fntfilst.h>
-
+#if (defined(WIN32) || defined(WIN64) || defined(_WIN32) || defined(_WIN64)) && !defined(HAVE_STRCASECMP)
+#define strcasecmp _stricmp
+#endif
 static FontRenderersRec	renderers;
 
 /*
