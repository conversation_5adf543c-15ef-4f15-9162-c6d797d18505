diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1008,6 +1008,7 @@ set (INSTALL_SHARED_DIR ${CMAKE_INSTALL_LIBDIR})
 if (CYGWIN)
 	set (INSTALL_SHARED_DIR ${CMAKE_INSTALL_BINDIR})
 endif()
+set(CMAKE_INSTALL_INCLUDEDIR include)
 
 message(STATUS "INSTALL DIRS: bin=${CMAKE_INSTALL_BINDIR} lib=${CMAKE_INSTALL_LIBDIR} shlib=${INSTALL_SHARED_DIR} include=${CMAKE_INSTALL_INCLUDEDIR}")
 
@@ -1085,7 +1085,7 @@
 
 macro(srt_add_program name)
 	srt_add_program_dont_install(${name} ${ARGN})
-	install(TARGETS ${name} RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
+	install(TARGETS ${name} RUNTIME DESTINATION tools)
 endmacro()
 
 macro(srt_make_application name)
@@ -1124,7 +1124,7 @@ endmacro()
 macro(srt_add_application name) # ARGN=sources...
 	srt_add_program(${name} apps/${name}.cpp ${ARGN})
 	srt_make_application(${name})
-	install(TARGETS ${name} RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
+	install(TARGETS ${name} RUNTIME DESTINATION tools)
 endmacro()
 
 ## FIXME: transmitmedia.cpp does not build on OpenBSD
@@ -1324,7 +1324,7 @@ if (ENABLE_UNITTESTS AND ENABLE_CXX11)
 endif()
 
 
-install(PROGRAMS scripts/srt-ffplay DESTINATION ${CMAKE_INSTALL_BINDIR})
+install(PROGRAMS scripts/srt-ffplay DESTINATION tools)
 
 
 if (DEFINED SRT_EXTRA_APPS_INC)
