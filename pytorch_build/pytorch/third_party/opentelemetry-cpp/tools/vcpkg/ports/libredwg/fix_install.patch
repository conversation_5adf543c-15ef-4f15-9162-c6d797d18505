diff --git a/CMakeLists.txt b/CMakeLists.txt
index f0e04be..8263f23 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -196,10 +196,11 @@ if (BUILD_SHARED_LIBS)
   target_compile_definitions(${redwg} PUBLIC DLL_EXPORT;ENABLE_SHARED)
 endif()
 target_include_directories(${redwg} PRIVATE
-    ${CMAKE_CURRENT_SOURCE_DIR}/src
-    ${CMAKE_CURRENT_BINARY_DIR}/src)
+  PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
+         $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/src>
+         $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)
 target_include_directories(${redwg} PUBLIC
-  ${CMAKE_CURRENT_SOURCE_DIR}/include)
+  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>)
 
 link_libraries(${redwg} ${LIBS} ${CMAKE_THREAD_LIBS_INIT})
 
@@ -309,7 +310,7 @@ add_custom_target(
   COMMAND etags --language=c++ *.c *.h
   DEPENDS ${SRCS}
   WORKING_DIRECTORY ${CMAKE_SOURCE_DIR})
-
+if(0)
 if(MSVC)
   install(TARGETS ${redwg} RUNTIME PUBLIC_HEADER
           DESTINATION libredwg-${PACKAGE_VERSION})
@@ -331,7 +332,21 @@ else()
   endif()
 endif()
 install(TARGETS RUNTIME)
-
+endif()
+include(GNUInstallDirs)
+install(
+    TARGETS ${redwg}
+    EXPORT libredwg-core
+    COMPONENT libredwg
+    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    PUBLIC_HEADER DESTINATION include/libredwg)
+if(NOT LIBREDWG_LIBONLY)
+  install(TARGETS ${executables_TARGETS}
+          DESTINATION "${CMAKE_INSTALL_BINDIR}")
+endif()
+install(EXPORT libredwg-core FILE unofficial-libredwg-config.cmake NAMESPACE unofficial::libredwg:: DESTINATION share/unofficial-libredwg)
 if(WIN32)
   add_custom_target(dist
     COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_SOURCE_DIR}/README README
