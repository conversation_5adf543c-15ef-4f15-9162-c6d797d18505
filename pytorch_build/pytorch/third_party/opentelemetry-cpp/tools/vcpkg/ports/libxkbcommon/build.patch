diff --git a/tools/how-to-type.c b/tools/how-to-type.c
index 0a6f79e68..c1205004d 100644
--- a/tools/how-to-type.c
+++ b/tools/how-to-type.c
@@ -25,8 +25,10 @@
 
 #include <getopt.h>
 #include <stdbool.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <errno.h>
 
 #include "xkbcommon/xkbcommon.h"
diff --git a/tools/tools-common.c b/tools/tools-common.c
index 254499d8c..2465a88c2 100644
--- a/tools/tools-common.c
+++ b/tools/tools-common.c
@@ -42,6 +42,7 @@
 #ifdef _MSC_VER
 #include <io.h>
 #include <windows.h>
+#include <process.h>
 #else
 #include <unistd.h>
 #include <termios.h>
