diff --git a/librtmp/rtmp.c b/librtmp/rtmp.c
index 0865689..b00710e 100644
--- a/librtmp/rtmp.c
+++ b/librtmp/rtmp.c
@@ -155,7 +155,7 @@ static int clk_tck;
 uint32_t
 RTMP_GetTime()
 {
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
   return 0;
 #elif defined(_WIN32)
   return timeGetTime();
@@ -1381,7 +1381,7 @@ RTMP_ClientPacket(RTMP *r, RTMPPacket *packet)
   return bHasMediaPacket;
 }
 
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
 extern FILE *netstackdump;
 extern FILE *netstackdump_read;
 #endif
@@ -1469,7 +1469,7 @@ ReadN(RTMP *r, char *buffer, int n)
 	        return FALSE;
 	}
       /*RTMP_Log(RTMP_LOGDEBUG, "%s: %d bytes\n", __FUNCTION__, nBytes); */
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
       fwrite(ptr, 1, nBytes, netstackdump_read);
 #endif
 
@@ -3757,7 +3757,7 @@ HandShake(RTMP *r, int FP9HandShake)
 
   memset(&clientsig[4], 0, 4);
 
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
   for (i = 8; i < RTMP_SIG_SIZE; i++)
     clientsig[i] = 0xff;
 #else
@@ -3829,7 +3829,7 @@ SHandShake(RTMP *r)
   memcpy(serversig, &uptime, 4);
 
   memset(&serversig[4], 0, 4);
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
   for (i = 8; i < RTMP_SIG_SIZE; i++)
     serversig[i] = 0xff;
 #else
@@ -4298,7 +4298,7 @@ RTMPSockBuf_Send(RTMPSockBuf *sb, const char *buf, int len)
 {
   int rc;
 
-#ifdef _DEBUG
+#if defined(_DEBUG) && !defined(LIBRTMP_ONLY)
   fwrite(buf, 1, len, netstackdump);
 #endif
 
