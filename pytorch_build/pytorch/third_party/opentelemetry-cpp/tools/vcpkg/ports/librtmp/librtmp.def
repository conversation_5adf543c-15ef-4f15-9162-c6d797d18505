EXPORTS
	AMF3_Decode
	AMF3CD_AddProp
	AMF3CD_GetProp
	AMF3Prop_Decode
	AMF3ReadInteger
	AMF3ReadString

	AMF_AddProp
	AMF_CountProp
	AMF_Decode
	AMF_DecodeArray
	AMF_DecodeBoolean
	AMF_DecodeInt16
	AMF_DecodeInt24
	AMF_DecodeInt32
	AMF_DecodeLongString
	AMF_DecodeNumber
	AMF_DecodeString
	AMF_Dump
	AMF_Encode
	AMF_EncodeBoolean
	AMF_EncodeInt16
	AMF_EncodeInt24
	AMF_EncodeInt32
	AMF_EncodeNamedBoolean
	AMF_EncodeNamedNumber
	AMF_EncodeNamedString
	AMF_EncodeNumber
	AMF_EncodeString
	AMF_GetProp
	AMF_Reset

	AMFProp_Decode
	AMFProp_Dump
	AMFProp_Encode
	AMFProp_GetBoolean
	AMFProp_GetName
	AMFProp_GetNumber
	AMFProp_GetObject
	AMFProp_GetString
	AMFProp_GetType
	AMFProp_IsValid
	AMFProp_Reset
	AMFProp_SetName

	RTMP_Alloc
	RTMP_ClientPacket
	RTMP_Close
	RTMP_Connect
	RTMP_Connect0
	RTMP_Connect1
	RTMP_ConnectStream
	RTMP_debuglevel
	RTMP_DeleteStream
	RTMP_DropRequest
	RTMP_EnableWrite
	RTMP_FindFirstMatchingProperty
	RTMP_FindPrefixProperty
	RTMP_Free
	RTMP_GetDuration
	RTMP_GetNextMediaPacket
	RTMP_GetTime
	RTMP_HashSWF
	RTMP_Init
	RTMP_IsConnected
	RTMP_IsTimedout
	RTMP_LibVersion
	RTMP_Log
	RTMP_LogGetLevel
	RTMP_LogHex
	RTMP_LogHexString
	RTMP_LogPrintf
	RTMP_LogSetCallback
	RTMP_LogSetLevel
	RTMP_LogSetOutput
	RTMP_LogStatus
	RTMP_ParsePlaypath
	RTMP_ParseURL
	RTMP_Pause
	RTMP_Read
	RTMP_ReadPacket
	RTMP_ReconnectStream
	RTMP_SendChunk
	RTMP_SendClientBW
	RTMP_SendCreateStream
	RTMP_SendCtrl
	RTMP_SendPacket
	RTMP_SendPause
	RTMP_SendSeek
	RTMP_SendServerBW
	RTMP_Serve
	RTMP_SetBufferMS
	RTMP_SetOpt
	RTMP_SetupStream
	RTMP_SetupURL
	RTMP_Socket
	RTMP_TLS_Init
	RTMP_ToggleStream
	RTMP_UpdateBufferMS
	RTMP_UserInterrupt
	RTMP_Write
	RTMPPacket_Alloc
	RTMPPacket_Dump
	RTMPPacket_Free
	RTMPPacket_Reset
	RTMPSockBuf_Close
	RTMPSockBuf_Fill
	RTMPSockBuf_Send
