{"model_type": "autoencoder", "sample_size": 32000, "sample_rate": 32000, "audio_channels": 1, "model": {"encoder": {"type": "seanet", "config": {"channels": 1, "dimension": 128, "n_filters": 64, "ratios": [4, 4, 5, 8], "n_residual_layers": 1, "dilation_base": 2, "lstm": 2, "norm": "weight_norm"}}, "decoder": {"type": "seanet", "config": {"channels": 1, "dimension": 128, "n_filters": 64, "ratios": [4, 4, 5, 8], "n_residual_layers": 1, "dilation_base": 2, "lstm": 2, "norm": "weight_norm"}}, "bottleneck": {"type": "rvq", "config": {"num_quantizers": 4, "codebook_size": 2048, "dim": 128, "decay": 0.99, "threshold_ema_dead_code": 2}}, "latent_dim": 128, "downsampling_ratio": 640, "io_channels": 1}, "training": {"learning_rate": 0.0001, "warmup_steps": 0, "use_ema": true, "loss_configs": {"discriminator": {"type": "encodec", "config": {"filters": 32, "n_ffts": [2048, 1024, 512, 256, 128], "hop_lengths": [512, 256, 128, 64, 32], "win_lengths": [2048, 1024, 512, 256, 128]}, "weights": {"adversarial": 0.1, "feature_matching": 5.0}}, "spectral": {"type": "mrstft", "config": {"fft_sizes": [2048, 1024, 512, 256, 128, 64, 32], "hop_sizes": [512, 256, 128, 64, 32, 16, 8], "win_lengths": [2048, 1024, 512, 256, 128, 64, 32], "perceptual_weighting": true}, "weights": {"mrstft": 1.0}}, "time": {"type": "l1", "weights": {"l1": 0.0}}}, "demo": {"demo_every": 2000}}}