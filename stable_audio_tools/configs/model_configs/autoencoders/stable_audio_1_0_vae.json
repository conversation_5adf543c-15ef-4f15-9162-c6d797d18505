{"model_type": "autoencoder", "sample_size": 65536, "sample_rate": 44100, "audio_channels": 2, "model": {"encoder": {"type": "dac", "config": {"in_channels": 2, "latent_dim": 128, "d_model": 128, "strides": [4, 4, 8, 8]}}, "decoder": {"type": "dac", "config": {"out_channels": 2, "latent_dim": 64, "channels": 1536, "rates": [8, 8, 4, 4]}}, "bottleneck": {"type": "vae"}, "latent_dim": 64, "downsampling_ratio": 1024, "io_channels": 2}, "training": {"learning_rate": 0.0001, "warmup_steps": 0, "use_ema": true, "optimizer_configs": {"autoencoder": {"optimizer": {"type": "AdamW", "config": {"betas": [0.8, 0.99], "lr": 0.0001}}, "scheduler": {"type": "ExponentialLR", "config": {"gamma": 0.999996}}}, "discriminator": {"optimizer": {"type": "AdamW", "config": {"betas": [0.8, 0.99], "lr": 0.0001}}, "scheduler": {"type": "ExponentialLR", "config": {"gamma": 0.999996}}}}, "loss_configs": {"discriminator": {"type": "encodec", "config": {"filters": 32, "n_ffts": [2048, 1024, 512, 256, 128], "hop_lengths": [512, 256, 128, 64, 32], "win_lengths": [2048, 1024, 512, 256, 128]}, "weights": {"adversarial": 0.1, "feature_matching": 5.0}}, "spectral": {"type": "mrstft", "config": {"fft_sizes": [2048, 1024, 512, 256, 128, 64, 32], "hop_sizes": [512, 256, 128, 64, 32, 16, 8], "win_lengths": [2048, 1024, 512, 256, 128, 64, 32], "perceptual_weighting": true}, "weights": {"mrstft": 1.0}}, "time": {"type": "l1", "weights": {"l1": 0.0}}, "bottleneck": {"type": "kl", "weights": {"kl": 1e-06}}}, "demo": {"demo_every": 2000}}}