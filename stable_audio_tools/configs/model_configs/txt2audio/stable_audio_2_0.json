{"model_type": "diffusion_cond", "sample_size": 12582912, "sample_rate": 44100, "audio_channels": 2, "model": {"pretransform": {"type": "autoencoder", "iterate_batch": true, "config": {"encoder": {"type": "oobleck", "config": {"in_channels": 2, "channels": 128, "c_mults": [1, 2, 4, 8, 16], "strides": [2, 4, 4, 8, 8], "latent_dim": 128, "use_snake": true}}, "decoder": {"type": "oobleck", "config": {"out_channels": 2, "channels": 128, "c_mults": [1, 2, 4, 8, 16], "strides": [2, 4, 4, 8, 8], "latent_dim": 64, "use_snake": true, "final_tanh": false}}, "bottleneck": {"type": "vae"}, "latent_dim": 64, "downsampling_ratio": 2048, "io_channels": 2}}, "conditioning": {"configs": [{"id": "prompt", "type": "clap_text", "config": {"audio_model_type": "HTSAT-base", "enable_fusion": true, "clap_ckpt_path": "/path/to/clap.ckpt", "use_text_features": true, "feature_layer_ix": -2}}, {"id": "seconds_start", "type": "number", "config": {"min_val": 0, "max_val": 512}}, {"id": "seconds_total", "type": "number", "config": {"min_val": 0, "max_val": 512}}], "cond_dim": 768}, "diffusion": {"cross_attention_cond_ids": ["prompt", "seconds_start", "seconds_total"], "global_cond_ids": ["seconds_start", "seconds_total"], "type": "dit", "config": {"io_channels": 64, "embed_dim": 1536, "depth": 24, "num_heads": 24, "cond_token_dim": 768, "global_cond_dim": 1536, "project_cond_tokens": false, "transformer_type": "continuous_transformer"}}, "io_channels": 64}, "training": {"use_ema": true, "log_loss_info": false, "optimizer_configs": {"diffusion": {"optimizer": {"type": "AdamW", "config": {"lr": 5e-05, "betas": [0.9, 0.999], "weight_decay": 0.001}}, "scheduler": {"type": "InverseLR", "config": {"inv_gamma": 1000000, "power": 0.5, "warmup": 0.99}}}}, "demo": {"demo_every": 2000, "demo_steps": 250, "num_demos": 4, "demo_cond": [{"prompt": "A beautiful piano arpeggio", "seconds_start": 0, "seconds_total": 80}, {"prompt": "A tropical house track with upbeat melodies, a driving bassline, and cheery vibes", "seconds_start": 0, "seconds_total": 250}, {"prompt": "A cool 80s glam rock song with driving drums and distorted guitars", "seconds_start": 0, "seconds_total": 180}, {"prompt": "A grand orchestral arrangement with thunderous percussion, epic brass fanfares, and soaring strings, creating a cinematic atmosphere fit for a heroic battle.", "seconds_start": 0, "seconds_total": 190}], "demo_cfg_scales": [3, 6, 9]}}}