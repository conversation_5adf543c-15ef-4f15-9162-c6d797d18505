{"model_type": "autoencoder", "sample_size": 65536, "sample_rate": 44100, "audio_channels": 1, "model": {"encoder": {"type": "dac", "config": {"latent_dim": 64, "d_model": 128, "strides": [4, 8, 8, 8]}}, "decoder": {"type": "dac", "config": {"latent_dim": 32, "channels": 1536, "rates": [8, 8, 8, 4]}}, "bottleneck": {"type": "vae"}, "latent_dim": 32, "downsampling_ratio": 2048, "io_channels": 1}, "training": {"learning_rate": 0.0001, "warmup_steps": 0, "use_ema": false, "loss_configs": {"discriminator": {"type": "encodec", "config": {"filters": 32, "n_ffts": [2048, 1024, 512, 256, 128, 64, 32], "hop_lengths": [512, 256, 128, 64, 32, 16, 8], "win_lengths": [2048, 1024, 512, 256, 128, 64, 32]}, "weights": {"adversarial": 0.1, "feature_matching": 5.0}}, "spectral": {"type": "mrstft", "config": {"fft_sizes": [2048, 1024, 512, 256, 128, 64, 32], "hop_sizes": [512, 256, 128, 64, 32, 16, 8], "win_lengths": [2048, 1024, 512, 256, 128, 64, 32], "perceptual_weighting": true}, "weights": {"mrstft": 1.0}}, "time": {"type": "l1", "weights": {"l1": 0.0}}}, "demo": {"demo_every": 2000}}}