#!/usr/bin/env python3
"""
Patch stable-audio-tools to work with older PyTorch versions
This creates a compatibility layer for flex_attention
"""

import os
import sys
import torch

def create_flex_attention_fallback():
    """Create a fallback implementation for flex_attention"""
    
    def flex_attention_fallback(query, key, value, score_mod=None, block_mask=None, scale=None):
        """
        Fallback implementation of flex_attention using standard attention
        """
        print("⚠️  Using fallback flex_attention implementation")
        
        # Basic scaled dot-product attention
        if scale is None:
            scale = 1.0 / (query.size(-1) ** 0.5)
        
        # Compute attention scores
        scores = torch.matmul(query, key.transpose(-2, -1)) * scale
        
        # Apply score modification if provided
        if score_mod is not None:
            try:
                scores = score_mod(scores)
            except:
                print("⚠️  Score modification failed, using unmodified scores")
        
        # Apply block mask if provided
        if block_mask is not None:
            try:
                scores = scores.masked_fill(~block_mask, float('-inf'))
            except:
                print("⚠️  Block mask application failed, ignoring mask")
        
        # Apply softmax
        attn_weights = torch.softmax(scores, dim=-1)
        
        # Apply attention to values
        output = torch.matmul(attn_weights, value)
        
        return output
    
    return flex_attention_fallback

def patch_torch_attention():
    """Patch torch.nn.attention to include flex_attention"""
    
    try:
        import torch.nn.attention
        
        # Check if flex_attention already exists
        if hasattr(torch.nn.attention, 'flex_attention'):
            print("✅ flex_attention already available")
            return True
        
        # Add our fallback implementation
        torch.nn.attention.flex_attention = create_flex_attention_fallback()
        print("✅ Added flex_attention fallback to torch.nn.attention")
        return True
        
    except ImportError:
        print("❌ Could not import torch.nn.attention")
        return False

def patch_stable_audio_files():
    """Patch stable-audio-tools files to use fallback attention"""
    
    stable_audio_path = "stable-audio-tools"
    
    if not os.path.exists(stable_audio_path):
        print(f"❌ {stable_audio_path} directory not found")
        return False
    
    # Find files that use flex_attention
    import glob
    python_files = glob.glob(f"{stable_audio_path}/**/*.py", recursive=True)
    
    patched_files = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            if 'flex_attention' in content:
                print(f"📝 Found flex_attention usage in {file_path}")
                
                # Add our patch at the top of the file
                patch_code = '''
# Compatibility patch for flex_attention
try:
    from torch.nn.attention import flex_attention
except (ImportError, AttributeError):
    import torch
    def flex_attention(query, key, value, score_mod=None, block_mask=None, scale=None):
        """Fallback flex_attention implementation"""
        if scale is None:
            scale = 1.0 / (query.size(-1) ** 0.5)
        scores = torch.matmul(query, key.transpose(-2, -1)) * scale
        if score_mod is not None:
            try:
                scores = score_mod(scores)
            except:
                pass
        if block_mask is not None:
            try:
                scores = scores.masked_fill(~block_mask, float('-inf'))
            except:
                pass
        attn_weights = torch.softmax(scores, dim=-1)
        return torch.matmul(attn_weights, value)

'''
                
                # Insert patch after imports
                lines = content.split('\n')
                insert_index = 0
                
                # Find the last import statement
                for i, line in enumerate(lines):
                    if line.strip().startswith(('import ', 'from ')) and not line.strip().startswith('#'):
                        insert_index = i + 1
                
                # Insert our patch
                lines.insert(insert_index, patch_code)
                
                # Write back to file
                with open(file_path, 'w') as f:
                    f.write('\n'.join(lines))
                
                patched_files.append(file_path)
                
        except Exception as e:
            print(f"⚠️  Error processing {file_path}: {e}")
    
    if patched_files:
        print(f"✅ Patched {len(patched_files)} files:")
        for file_path in patched_files:
            print(f"   - {file_path}")
    else:
        print("ℹ️  No files needed patching")
    
    return True

def test_stable_audio_import():
    """Test if stable-audio-tools can be imported after patching"""
    
    try:
        # Add stable-audio-tools to path
        sys.path.insert(0, 'stable-audio-tools')
        
        # Try importing
        from stable_audio_tools import get_pretrained_model
        print("✅ stable-audio-tools import successful!")
        return True
        
    except Exception as e:
        print(f"❌ stable-audio-tools import failed: {e}")
        return False

def main():
    """Main patching function"""
    
    print("🔧 Patching stable-audio-tools for PyTorch compatibility...")
    print(f"PyTorch version: {torch.__version__}")
    
    # Step 1: Patch torch.nn.attention
    if not patch_torch_attention():
        print("❌ Failed to patch torch.nn.attention")
        return False
    
    # Step 2: Patch stable-audio-tools files
    if not patch_stable_audio_files():
        print("❌ Failed to patch stable-audio-tools files")
        return False
    
    # Step 3: Test import
    if not test_stable_audio_import():
        print("❌ stable-audio-tools still not working after patching")
        return False
    
    print("🎉 Patching completed successfully!")
    print("You can now try running the Gradio interface")
    
    return True

if __name__ == "__main__":
    main()
