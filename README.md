# Stable Audio Open Small - Complete Setup Guide

This repository contains multiple approaches for running Stable Audio Open Small with a Gradio interface for text-to-audio generation.

## 🎵 What is Stable Audio Open Small?

Stable Audio Open Small is a fast, efficient text-to-audio model from Stability AI that can:
- Generate up to 11 seconds of stereo audio at 44.1kHz
- Create music, sound effects, and ambient audio from text descriptions
- Run efficiently on CPU and mobile devices
- Generate audio in real-time on modern hardware

## 📁 Repository Structure

```
├── stable-audio-tools/              # Cloned stable-audio-tools repository
├── pytorch_build/                   # PyTorch source compilation (in progress)
├── gradio_stable_audio_small.py     # Custom Gradio interface
├── simple_stable_audio_demo.py      # Simple demo script
├── install_conda_pytorch.sh         # Conda installation script
├── install_pytorch_nightly.sh       # PyTorch nightly installation
├── patch_stable_audio.py            # Compatibility patch
└── README.md                        # This file
```

## ⚠️ Current Issue: PyTorch Compatibility

The stable-audio-tools requires `torch.nn.attention.flex_attention` which is only available in PyTorch 2.5.1+. However, PyTorch 2.5.1+ pre-built binaries are not available for Intel Macs.

## 🚀 Solution Options (Ranked by Ease)

### Option 1: Conda with conda-forge (⭐ RECOMMENDED)

**Fastest and most reliable method:**

```bash
# Make the script executable and run it
chmod +x install_conda_pytorch.sh
./install_conda_pytorch.sh
```

This will:
- Install Miniconda
- Create a dedicated environment
- Install PyTorch from conda-forge (often has newer versions)
- Install all dependencies

### Option 2: PyTorch Nightly Build

**Try the latest nightly builds:**

```bash
chmod +x install_pytorch_nightly.sh
./install_pytorch_nightly.sh
```

### Option 3: Compatibility Patch (Current PyTorch)

**Use current PyTorch with a compatibility layer:**

```bash
python3 patch_stable_audio.py
```

This creates a fallback implementation of `flex_attention` that works with older PyTorch versions.

### Option 4: Compile PyTorch from Source (⚠️ ADVANCED)

**Only if other options fail - takes 2-4 hours:**

The PyTorch repository is currently being cloned in `pytorch_build/`. To continue:

```bash
cd pytorch_build/pytorch

# Wait for clone to complete, then:
git checkout v2.5.1  # or latest stable tag

# Set environment variables
export LDFLAGS="-L/usr/local/opt/libomp/lib"
export CPPFLAGS="-I/usr/local/opt/libomp/include"
export CMAKE_PREFIX_PATH="/usr/local"
export MACOSX_DEPLOYMENT_TARGET=10.15

# Install Python dependencies
pip install numpy pyyaml setuptools cffi typing_extensions future six requests

# Build PyTorch (this takes 2-4 hours!)
python setup.py develop
```

## 🎯 Quick Test After Installation

After using any of the above methods, test the installation:

```bash
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
try:
    import torch.nn.attention
    if hasattr(torch.nn.attention, 'flex_attention'):
        print('✅ flex_attention is available!')
    else:
        print('❌ flex_attention not found')
except ImportError as e:
    print('❌ Error importing torch.nn.attention:', e)
"
```

### Running the Interface

#### Option 1: Built-in Gradio Interface (Recommended)

```bash
cd stable-audio-tools
python run_gradio.py --pretrained-name stabilityai/stable-audio-open-small --share
```

This will:
- Download the model automatically (first run only)
- Start a Gradio interface on `http://localhost:7860`
- Create a shareable public link if `--share` is used

#### Option 2: Simple Demo Script

```bash
python simple_stable_audio_demo.py
```

This will test the installation and generate a few sample audio files.

#### Option 3: Custom Gradio Interface (May have compatibility issues)

```bash
python gradio_stable_audio_small.py
```

## 🎛️ Usage

### Web Interface

1. Open the Gradio interface in your browser
2. Enter a text prompt describing the audio you want to generate
3. Adjust parameters:
   - **Duration**: 1-11 seconds
   - **Steps**: 4-50 (higher = better quality, slower)
   - **CFG Scale**: 0.1-5.0 (higher = more prompt adherence)
4. Click "Generate Audio"
5. Download the generated audio file

### Example Prompts

**Music:**
- "128 BPM tech house drum loop"
- "acoustic guitar fingerpicking"
- "jazz piano solo"
- "electronic ambient pad"

**Sound Effects:**
- "rain on window"
- "footsteps on gravel"
- "door creaking open"
- "car engine starting"

**Ambient:**
- "forest birds chirping"
- "ocean waves"
- "crackling fireplace"
- "wind through trees"

## 🔧 Troubleshooting

### Common Issues

1. **PyTorch version compatibility**:
   ```
   ModuleNotFoundError: No module named 'torch.nn.attention'
   ```
   **Solution**: Update PyTorch to version 2.5.1 or higher

2. **PyWavelets build error with Python 3.13**:
   ```
   AttributeError: module 'pkgutil' has no attribute 'ImpImporter'
   ```
   **Solution**: Use Python 3.9-3.12 instead of 3.13

3. **Gradio compatibility issues**:
   ```
   TypeError: argument of type 'bool' is not iterable
   ```
   **Solution**: Use the built-in `run_gradio.py` script instead

4. **Model download issues**:
   - Ensure you have a stable internet connection
   - The model is ~500MB and will be downloaded on first use
   - Check Hugging Face Hub access

### Performance Tips

- **CPU Usage**: The small model is optimized for CPU inference
- **Memory**: Requires ~2GB RAM for model loading
- **Generation Time**:
  - CPU: 5-30 seconds depending on hardware
  - GPU: 1-5 seconds with CUDA

## 📝 Model Information

- **Model**: stabilityai/stable-audio-open-small
- **Max Duration**: 11 seconds
- **Sample Rate**: 44.1kHz
- **Channels**: Stereo
- **Model Size**: ~500MB
- **License**: Stability AI Community License

## 🔗 Links

- [Stable Audio Open Small on Hugging Face](https://huggingface.co/stabilityai/stable-audio-open-small)
- [Stable Audio Tools GitHub](https://github.com/Stability-AI/stable-audio-tools)
- [Research Paper](https://arxiv.org/abs/2505.08175)
- [Stability AI](https://stability.ai/)

## 📄 License

This setup follows the Stability AI Community License. Please refer to the original repositories for detailed licensing information.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
