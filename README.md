# Stable Audio Open Small - Gradio Setup

This repository contains the setup for running Stable Audio Open Small with a Gradio interface for text-to-audio generation.

## 🎵 What is Stable Audio Open Small?

Stable Audio Open Small is a fast, efficient text-to-audio model from Stability AI that can:
- Generate up to 11 seconds of stereo audio at 44.1kHz
- Create music, sound effects, and ambient audio from text descriptions
- Run efficiently on CPU and mobile devices
- Generate audio in real-time on modern hardware

## 📁 Repository Structure

```
├── stable-audio-tools/          # Cloned stable-audio-tools repository
├── gradio_stable_audio_small.py # Custom Gradio interface (has compatibility issues)
├── simple_stable_audio_demo.py  # Simple demo script
└── README.md                    # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.9+ (Python 3.13 has compatibility issues)
- PyTorch 2.5.1+ (for torch.nn.attention.flex_attention support)
- CUDA (optional, for GPU acceleration)

### Installation Steps

1. **Clone this repository** (already done):
   ```bash
   # Repository is already cloned with stable-audio-tools
   cd "Stable Audio"
   ```

2. **Install PyTorch with the correct version**:
   ```bash
   # For CPU only
   pip install torch>=2.5.1 torchaudio>=2.5.1 --index-url https://download.pytorch.org/whl/cpu
   
   # For CUDA (if you have a compatible GPU)
   pip install torch>=2.5.1 torchaudio>=2.5.1 --index-url https://download.pytorch.org/whl/cu121
   ```

3. **Install stable-audio-tools dependencies**:
   ```bash
   cd stable-audio-tools
   pip install -e .
   ```

4. **Install additional dependencies**:
   ```bash
   pip install gradio einops k-diffusion
   ```

### Running the Interface

#### Option 1: Built-in Gradio Interface (Recommended)

```bash
cd stable-audio-tools
python run_gradio.py --pretrained-name stabilityai/stable-audio-open-small --share
```

This will:
- Download the model automatically (first run only)
- Start a Gradio interface on `http://localhost:7860`
- Create a shareable public link if `--share` is used

#### Option 2: Simple Demo Script

```bash
python simple_stable_audio_demo.py
```

This will test the installation and generate a few sample audio files.

#### Option 3: Custom Gradio Interface (May have compatibility issues)

```bash
python gradio_stable_audio_small.py
```

## 🎛️ Usage

### Web Interface

1. Open the Gradio interface in your browser
2. Enter a text prompt describing the audio you want to generate
3. Adjust parameters:
   - **Duration**: 1-11 seconds
   - **Steps**: 4-50 (higher = better quality, slower)
   - **CFG Scale**: 0.1-5.0 (higher = more prompt adherence)
4. Click "Generate Audio"
5. Download the generated audio file

### Example Prompts

**Music:**
- "128 BPM tech house drum loop"
- "acoustic guitar fingerpicking"
- "jazz piano solo"
- "electronic ambient pad"

**Sound Effects:**
- "rain on window"
- "footsteps on gravel"
- "door creaking open"
- "car engine starting"

**Ambient:**
- "forest birds chirping"
- "ocean waves"
- "crackling fireplace"
- "wind through trees"

## 🔧 Troubleshooting

### Common Issues

1. **PyTorch version compatibility**:
   ```
   ModuleNotFoundError: No module named 'torch.nn.attention'
   ```
   **Solution**: Update PyTorch to version 2.5.1 or higher

2. **PyWavelets build error with Python 3.13**:
   ```
   AttributeError: module 'pkgutil' has no attribute 'ImpImporter'
   ```
   **Solution**: Use Python 3.9-3.12 instead of 3.13

3. **Gradio compatibility issues**:
   ```
   TypeError: argument of type 'bool' is not iterable
   ```
   **Solution**: Use the built-in `run_gradio.py` script instead

4. **Model download issues**:
   - Ensure you have a stable internet connection
   - The model is ~500MB and will be downloaded on first use
   - Check Hugging Face Hub access

### Performance Tips

- **CPU Usage**: The small model is optimized for CPU inference
- **Memory**: Requires ~2GB RAM for model loading
- **Generation Time**: 
  - CPU: 5-30 seconds depending on hardware
  - GPU: 1-5 seconds with CUDA

## 📝 Model Information

- **Model**: stabilityai/stable-audio-open-small
- **Max Duration**: 11 seconds
- **Sample Rate**: 44.1kHz
- **Channels**: Stereo
- **Model Size**: ~500MB
- **License**: Stability AI Community License

## 🔗 Links

- [Stable Audio Open Small on Hugging Face](https://huggingface.co/stabilityai/stable-audio-open-small)
- [Stable Audio Tools GitHub](https://github.com/Stability-AI/stable-audio-tools)
- [Research Paper](https://arxiv.org/abs/2505.08175)
- [Stability AI](https://stability.ai/)

## 📄 License

This setup follows the Stability AI Community License. Please refer to the original repositories for detailed licensing information.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
