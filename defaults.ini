
[DEFAULTS]

#name of the run
name = stable_audio_tools

# name of the project
project = None

# the batch size
batch_size = 4

# If `true`, attempts to resume training from latest checkpoint.
# In this case, each run must have unique config filename.
recover = false

# Save top K model checkpoints during training.
save_top_k = -1

# number of nodes to use for training
num_nodes = 1

# Multi-GPU strategy for PyTorch Lightning
strategy = "auto"

# Precision to use for training
precision = "16-mixed"

# number of CPU workers for the DataLoader
num_workers = 6

# the random seed
seed = 42

# Batches for gradient accumulation
accum_batches = 1

# Number of steps between checkpoints
checkpoint_every = 10000

# Number of steps between validation runs
val_every = -1

# trainer checkpoint file to restart training from
ckpt_path = ''

# model checkpoint file to start a new training run from
pretrained_ckpt_path = ''

# Checkpoint path for the pretransform model if needed
pretransform_ckpt_path = ''

# configuration model specifying model hyperparameters
model_config = ''

# configuration for datasets
dataset_config = ''

# configuration for validation datasets
val_dataset_config = ''

# directory to save the checkpoints in
save_dir = ''

# gradient_clip_val passed into PyTorch Lightning Trainer
gradient_clip_val = 0.0

# remove the weight norm from the pretransform model
remove_pretransform_weight_norm = ''

# Logger type to use
logger = 'wandb'
