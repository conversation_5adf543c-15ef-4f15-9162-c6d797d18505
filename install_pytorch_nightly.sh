#!/bin/bash
# Install PyTorch nightly build with flex_attention support

echo "🌙 Installing PyTorch Nightly Build..."

# Create virtual environment
python3 -m venv pytorch_nightly_env
source pytorch_nightly_env/bin/activate

echo "📦 Installing PyTorch nightly..."

# Try different nightly builds
pip install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cpu

# If that fails, try specific date
# pip install --pre torch==2.6.0.dev20241201 torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cpu

echo "📚 Installing additional dependencies..."
pip install gradio einops transformers safetensors k-diffusion huggingface_hub

echo "🧪 Testing flex_attention availability..."
python -c "
try:
    import torch.nn.attention
    if hasattr(torch.nn.attention, 'flex_attention'):
        print('✅ flex_attention is available!')
    else:
        print('❌ flex_attention not found')
        print('Available attention functions:', [attr for attr in dir(torch.nn.attention) if not attr.startswith('_')])
except ImportError as e:
    print('❌ Error importing torch.nn.attention:', e)
"

echo "✅ Installation complete!"
echo "To activate: source pytorch_nightly_env/bin/activate"
