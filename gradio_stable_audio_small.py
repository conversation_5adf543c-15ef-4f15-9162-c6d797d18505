#!/usr/bin/env python3
"""
Gradio interface for Stable Audio Open Small
A simple web interface to generate audio from text prompts using the stable-audio-open-small model.
"""

import os
import sys
import torch
import torchaudio
import gradio as gr
import numpy as np
from pathlib import Path
import tempfile
import traceback

# Add the stable-audio-tools to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'stable-audio-tools'))

try:
    from einops import rearrange
    from stable_audio_tools import get_pretrained_model
    from stable_audio_tools.inference.generation import generate_diffusion_cond
    STABLE_AUDIO_AVAILABLE = True
except ImportError as e:
    print(f"Warning: stable-audio-tools not fully available: {e}")
    STABLE_AUDIO_AVAILABLE = False

class StableAudioGenerator:
    def __init__(self):
        self.model = None
        self.model_config = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.sample_rate = 44100
        self.sample_size = None
        
    def load_model(self):
        """Load the stable-audio-open-small model"""
        if not STABLE_AUDIO_AVAILABLE:
            raise Exception("stable-audio-tools is not properly installed")
            
        try:
            print("Loading stable-audio-open-small model...")
            self.model, self.model_config = get_pretrained_model("stabilityai/stable-audio-open-small")
            self.sample_rate = self.model_config["sample_rate"]
            self.sample_size = self.model_config["sample_size"]
            self.model = self.model.to(self.device)
            print(f"Model loaded successfully on {self.device}")
            print(f"Sample rate: {self.sample_rate}, Sample size: {self.sample_size}")
            return True
        except Exception as e:
            print(f"Error loading model: {e}")
            traceback.print_exc()
            return False
    
    def generate_audio(self, prompt, duration=8, steps=8, cfg_scale=1.0):
        """Generate audio from text prompt"""
        if self.model is None:
            if not self.load_model():
                return None, "Failed to load model"
        
        try:
            # Clamp duration to model limits (up to 11 seconds for small model)
            duration = max(1, min(duration, 11))
            
            # Set up conditioning
            conditioning = [{
                "prompt": prompt,
                "seconds_total": duration
            }]
            
            print(f"Generating audio for prompt: '{prompt}' (duration: {duration}s)")
            
            # Generate audio
            with torch.no_grad():
                output = generate_diffusion_cond(
                    self.model,
                    steps=int(steps),
                    cfg_scale=float(cfg_scale),
                    conditioning=conditioning,
                    sample_size=self.sample_size,
                    sampler_type="pingpong",
                    device=self.device
                )
            
            # Rearrange audio batch to a single sequence
            output = rearrange(output, "b d n -> d (b n)")
            
            # Peak normalize, clip, convert to int16
            output = output.to(torch.float32)
            output = output.div(torch.max(torch.abs(output))).clamp(-1, 1)
            output = output.mul(32767).to(torch.int16).cpu()
            
            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            torchaudio.save(temp_file.name, output, self.sample_rate)
            
            return temp_file.name, "Audio generated successfully!"
            
        except Exception as e:
            error_msg = f"Error generating audio: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg

# Initialize the generator
generator = StableAudioGenerator()

def generate_audio_interface(prompt, duration, steps, cfg_scale):
    """Gradio interface function"""
    if not prompt.strip():
        return None, "Please enter a text prompt"
    
    audio_file, message = generator.generate_audio(
        prompt=prompt.strip(),
        duration=duration,
        steps=steps,
        cfg_scale=cfg_scale
    )
    
    return audio_file, message

def create_interface():
    """Create the Gradio interface"""
    
    # Custom CSS for better styling
    css = """
    .gradio-container {
        max-width: 800px !important;
        margin: auto !important;
    }
    .title {
        text-align: center;
        margin-bottom: 20px;
    }
    """
    
    with gr.Blocks(css=css, title="Stable Audio Open Small") as interface:
        gr.HTML("""
        <div class="title">
            <h1>🎵 Stable Audio Open Small</h1>
            <p>Generate high-quality audio from text descriptions using Stability AI's open-source model</p>
            <p><em>Model can generate up to 11 seconds of stereo audio at 44.1kHz</em></p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column():
                prompt_input = gr.Textbox(
                    label="Text Prompt",
                    placeholder="Enter your audio description (e.g., '128 BPM tech house drum loop', 'acoustic guitar melody', 'rain sounds')",
                    lines=3,
                    value="128 BPM tech house drum loop"
                )
                
                with gr.Row():
                    duration_slider = gr.Slider(
                        minimum=1,
                        maximum=11,
                        value=8,
                        step=1,
                        label="Duration (seconds)"
                    )
                    
                    steps_slider = gr.Slider(
                        minimum=4,
                        maximum=50,
                        value=8,
                        step=1,
                        label="Generation Steps (higher = better quality, slower)"
                    )
                
                cfg_scale_slider = gr.Slider(
                    minimum=0.1,
                    maximum=5.0,
                    value=1.0,
                    step=0.1,
                    label="CFG Scale (higher = more prompt adherence)"
                )
                
                generate_btn = gr.Button("🎵 Generate Audio", variant="primary", size="lg")
                
            with gr.Column():
                audio_output = gr.Audio(
                    label="Generated Audio",
                    type="filepath"
                )
                
                status_output = gr.Textbox(
                    label="Status",
                    interactive=False
                )
        
        # Example prompts
        gr.HTML("""
        <div style="margin-top: 20px;">
            <h3>Example Prompts:</h3>
            <ul>
                <li><strong>Music:</strong> "128 BPM tech house drum loop", "acoustic guitar fingerpicking", "jazz piano solo"</li>
                <li><strong>Sound Effects:</strong> "rain on window", "footsteps on gravel", "door creaking open"</li>
                <li><strong>Ambient:</strong> "forest birds chirping", "ocean waves", "crackling fireplace"</li>
            </ul>
        </div>
        """)
        
        # Connect the interface
        generate_btn.click(
            fn=generate_audio_interface,
            inputs=[prompt_input, duration_slider, steps_slider, cfg_scale_slider],
            outputs=[audio_output, status_output]
        )
        
        # Example button functionality
        examples = [
            ["128 BPM tech house drum loop", 8, 8, 1.0],
            ["acoustic guitar fingerpicking", 6, 12, 1.2],
            ["rain on window", 10, 8, 1.0],
            ["jazz piano solo", 8, 15, 1.1],
            ["ocean waves crashing", 11, 8, 1.0]
        ]
        
        gr.Examples(
            examples=examples,
            inputs=[prompt_input, duration_slider, steps_slider, cfg_scale_slider],
            outputs=[audio_output, status_output],
            fn=generate_audio_interface,
            cache_examples=False
        )
    
    return interface

def main():
    """Main function to run the Gradio interface"""
    print("Starting Stable Audio Open Small Gradio Interface...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    if not STABLE_AUDIO_AVAILABLE:
        print("\n⚠️  Warning: stable-audio-tools is not fully available.")
        print("The interface will start but model loading may fail.")
        print("Please install stable-audio-tools properly for full functionality.")
    
    interface = create_interface()
    
    # Launch the interface
    interface.launch(
        server_name="0.0.0.0",  # Allow external connections
        server_port=7860,       # Default Gradio port
        share=False,            # Set to True to create a public link
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    main()
